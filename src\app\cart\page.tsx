'use client';

import React from 'react';
import Link from 'next/link';
import { ArrowLeft, ShoppingCart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { useCart } from '@/context/cart-context';
import { CartItem } from '@/components/cart/cart-item';
import { CartSummary } from '@/components/cart/cart-summary';
import { EmptyCart } from '@/components/cart/empty-cart';

export default function CartPage() {
  const { items, totalItems, clearCart } = useCart();

  // Якщо кошик порожній, показуємо компонент EmptyCart
  if (items.length === 0) {
    return (
      <main className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8">Кошик</h1>
        <EmptyCart />
      </main>
    );
  }

  return (
    <main className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold">Кошик</h1>
        <Button variant="outline" size="sm" onClick={clearCart}>
          Очистити кошик
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <div className="bg-card rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <ShoppingCart className="h-5 w-5 mr-2" />
                <h2 className="text-xl font-semibold">Товари ({totalItems})</h2>
              </div>
              <Link href="/marketplace" className="text-sm text-primary flex items-center">
                <ArrowLeft className="h-4 w-4 mr-1" />
                Продовжити покупки
              </Link>
            </div>

            <Separator className="mb-4" />

            <div className="space-y-1">
              {items.map((item) => (
                <CartItem key={item.id} item={item} />
              ))}
            </div>
          </div>
        </div>

        <div>
          <CartSummary />
        </div>
      </div>
    </main>
  );
}
