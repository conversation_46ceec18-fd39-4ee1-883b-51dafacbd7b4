'use client';

import Client3DScene from '@/components/marketplace/client-3d-scene';
import AdvancedSearch from '@/components/marketplace/advanced-search';
import ModelGrid from '@/components/marketplace/model-grid';
import { Skeleton } from '@/components/ui/skeleton';
import { Model, ModelsQueryParams } from '@/types/models';
import { useSearchParams, useRouter } from 'next/navigation';
import React, { Suspense, useEffect, useState } from 'react';

// Loading skeleton for models list
function ModelsListSkeleton() {
  return (
    <div className="w-full">
      {/* Skeleton для фільтрів та пошуку */}
      <div className="flex flex-col lg:flex-row gap-8">
        {/* Skeleton для бічної панелі */}
        <div className="w-full lg:w-1/4 space-y-6">
          <div className="bg-card rounded-lg p-6 shadow-sm">
            <Skeleton className="h-8 w-32 mb-4" />
            <div className="space-y-2">
              {Array.from({ length: 6 }).map((_, i) => (
                <Skeleton key={i} className="h-10 w-full" />
              ))}
            </div>
          </div>

          <div className="bg-card rounded-lg p-6 shadow-sm">
            <Skeleton className="h-8 w-32 mb-4" />
            <div className="space-y-4">
              <div className="flex space-x-2">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </div>
              <Skeleton className="h-6 w-full" />
            </div>
          </div>
        </div>

        {/* Skeleton для основного вмісту */}
        <div className="w-full lg:w-3/4">
          {/* Skeleton для пошуку */}
          <Skeleton className="h-14 w-full mb-6" />

          {/* Skeleton для заголовка моделей */}
          <div className="flex justify-between items-center mb-6">
            <Skeleton className="h-8 w-32" />
            <Skeleton className="h-10 w-48" />
          </div>

          {/* Skeleton для сітки моделей */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 9 }).map((_, i) => (
              <div key={i} className="rounded-lg overflow-hidden border border-border">
                <Skeleton className="h-48 w-full" />
                <div className="p-4">
                  <Skeleton className="h-6 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-1/2 mb-4" />
                  <div className="flex justify-between">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Skeleton для пагінації */}
          <div className="flex justify-center mt-8">
            <div className="flex items-center gap-1">
              {Array.from({ length: 5 }).map((_, i) => (
                <Skeleton key={i} className="h-10 w-10 rounded" />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Function to fetch models from API
async function getModels(params?: ModelsQueryParams): Promise<{
  models: Model[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}> {
  try {
    // Build query parameters
    const queryParams = new URLSearchParams();

    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.category) queryParams.append('category', params.category);
    if (params?.search) queryParams.append('search', params.search);
    if (params?.tags) queryParams.append('tags', params.tags.join(','));
    if (params?.minPrice !== undefined) queryParams.append('minPrice', params.minPrice.toString());
    if (params?.maxPrice !== undefined) queryParams.append('maxPrice', params.maxPrice.toString());
    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);

    // Make API request - спочатку пробуємо скраповані моделі
    const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
    let response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || window.location.origin}/api/scraped-models${queryString}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'no-store', // Disable caching to get the latest data
    });

    // Якщо скрапованих моделей немає, використовуємо основний API
    if (!response.ok) {
      response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || ''}/api/models${queryString}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        cache: 'no-store',
      });
    }

    if (!response.ok) {
      throw new Error(`Failed to fetch models: ${response.statusText}`);
    }

    const data = await response.json() as any;
    // Перевіряємо структуру відповіді та повертаємо правильний формат
    if (data.data && typeof data.data === 'object') {
      return data.data as {
        models: Model[];
        pagination: {
          total: number;
          page: number;
          limit: number;
          pages: number;
        };
      };
    } else if (data.models && data.pagination) {
      return data as {
        models: Model[];
        pagination: {
          total: number;
          page: number;
          limit: number;
          pages: number;
        };
      };
    } else {
      // Якщо структура не відповідає очікуваній, спробуємо адаптувати її
      return {
        models: Array.isArray(data.data) ? data.data : (data.models || []) as Model[],
        pagination: data.pagination || {
          total: Array.isArray(data.data) ? data.data.length : 0,
          page: 1,
          limit: 20,
          pages: 1
        }
      };
    }
  } catch (error) {
    console.error('Error fetching models:', error);
    // Повертаємо порожній результат у випадку помилки
    return {
      models: [],
      pagination: {
        total: 0,
        page: params?.page || 1,
        limit: params?.limit || 20,
        pages: 0
      }
    };
  }
}

// Models list component with client-side data fetching
function ModelsList() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [models, setModels] = useState<Model[]>([]);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 20,
    pages: 0
  });
  const [loading, setLoading] = useState(true);

  // Функція для оновлення URL з новими фільтрами
  const updateSearchParams = (filters: any) => {
    const params = new URLSearchParams();

    if (filters.query) params.set('search', filters.query);
    if (filters.category && filters.category !== 'Всі категорії') params.set('category', filters.category);
    if (filters.sortBy) params.set('sortBy', filters.sortBy);
    if (filters.priceRange[0] > 0) params.set('minPrice', filters.priceRange[0].toString());
    if (filters.priceRange[1] < 100) params.set('maxPrice', filters.priceRange[1].toString());
    if (filters.tags.length > 0) params.set('tags', filters.tags.join(','));
    if (filters.isFree !== null) params.set('isFree', filters.isFree.toString());
    if (filters.dateRange !== 'all') params.set('dateRange', filters.dateRange);
    if (filters.minDownloads > 0) params.set('minDownloads', filters.minDownloads.toString());
    if (filters.fileFormats.length > 0) params.set('fileFormats', filters.fileFormats.join(','));

    router.push(`/marketplace?${params.toString()}`);
  };

  useEffect(() => {
    const fetchModels = async () => {
      setLoading(true);
      try {
        // Parse search parameters
        const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : 1;
        const category = searchParams.get('category') || undefined;
        const search = searchParams.get('search') || undefined;
        const sortBy = searchParams.get('sortBy') as ModelsQueryParams['sortBy'] | undefined;
        const minPrice = searchParams.get('minPrice') ? parseFloat(searchParams.get('minPrice')!) : undefined;
        const maxPrice = searchParams.get('maxPrice') ? parseFloat(searchParams.get('maxPrice')!) : undefined;
        const tags = searchParams.get('tags')?.split(',') || undefined;
        const isFree = searchParams.get('isFree') ? searchParams.get('isFree') === 'true' : undefined;

        // Fetch models from API
        const result = await getModels({
          page,
          limit: 20,
          category,
          search,
          sortBy,
          minPrice,
          maxPrice,
          tags,
          isFree
        });

        setModels(result.models);
        setPagination(result.pagination);
      } catch (error) {
        console.error('Error fetching models:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchModels();
  }, [searchParams]);

  // Перевіряємо, чи є моделі для відображення
  const hasModels = Array.isArray(models) && models.length > 0;

  if (loading) {
    return <ModelsListSkeleton />;
  }

  return (
    <div className="w-full space-y-6">
      {/* Розширений пошук */}
      <AdvancedSearch
        onFiltersChange={updateSearchParams}
        initialFilters={{
          query: searchParams.get('search') || '',
          category: searchParams.get('category') || 'Всі категорії',
          sortBy: searchParams.get('sortBy') || 'newest',
          priceRange: [
            searchParams.get('minPrice') ? parseInt(searchParams.get('minPrice')!) : 0,
            searchParams.get('maxPrice') ? parseInt(searchParams.get('maxPrice')!) : 100
          ],
          tags: searchParams.get('tags')?.split(',') || [],
          isFree: searchParams.get('isFree') ? searchParams.get('isFree') === 'true' : null,
        }}
      />

      {/* Результати пошуку */}
      <div>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">
            {hasModels ? `Знайдено ${pagination.total} моделей` : 'Моделі не знайдено'}
          </h2>
        </div>

        {hasModels ? (
          <ModelGrid models={models} />
        ) : (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-medium mb-2">Нічого не знайдено</h3>
            <p className="text-gray-500 mb-4">
              Спробуйте змінити параметри пошуку або фільтри
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

// Main marketplace page component
export default function ClientMarketplacePage() {
  return (
    <main className="min-h-screen">
      {/* Marketplace header */}
      <section className="bg-primary/5 py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl font-bold text-center mb-4">Маркетплейс 3D моделей</h1>
          <p className="text-xl text-center text-muted-foreground mb-8">
            Знаходьте, купуйте та завантажуйте високоякісні 3D моделі для ваших проектів
          </p>

          {/* 3D Scene */}
          <Client3DScene
            sceneUrl="https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode"
            height="400px"
            className="mb-8"
          />
        </div>
      </section>

      {/* Main content */}
      <section className="container mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Main content area with models */}
          <div className="w-full">
            <Suspense fallback={<ModelsListSkeleton />}>
              <ModelsList />
            </Suspense>
          </div>
        </div>
      </section>
    </main>
  );
}
