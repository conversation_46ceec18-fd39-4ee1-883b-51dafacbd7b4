import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

// GET handler for user models
export async function GET(
  request: NextRequest,
  context: any
) {
  try {
    const { params } = context;
    const userId = params.id;

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = Number(searchParams.get('page')) || 1;
    const limit = Number(searchParams.get('limit')) || 20;
    const category = searchParams.get('category');
    const sortBy = searchParams.get('sortBy') || 'newest';

    const offset = (page - 1) * limit;

    // Build SQL query
    let sql = `
      SELECT m.*, u.name as author_name, u.avatar_url as author_avatar
      FROM models m
      JOIN users u ON m.user_id = u.id
      WHERE m.user_id = ?
    `;

    const queryParams: any[] = [userId];

    // Filter by category
    if (category) {
      sql += ` AND m.category = ?`;
      queryParams.push(category);
    }

    // Sorting
    switch (sortBy) {
      case 'newest':
        sql += ` ORDER BY m.created_at DESC`;
        break;
      case 'oldest':
        sql += ` ORDER BY m.created_at ASC`;
        break;
      case 'popular':
        sql += ` ORDER BY m.download_count DESC`;
        break;
      case 'price_asc':
        sql += ` ORDER BY m.price ASC`;
        break;
      case 'price_desc':
        sql += ` ORDER BY m.price DESC`;
        break;
      case 'name':
        sql += ` ORDER BY m.name ASC`;
        break;
      default:
        sql += ` ORDER BY m.created_at DESC`;
    }

    // Pagination
    sql += ` LIMIT ? OFFSET ?`;
    queryParams.push(limit, offset);

    // Execute query
    const models = await query(sql, queryParams);

    // Get total count for pagination
    const countSql = `
      SELECT COUNT(*) as total
      FROM models m
      WHERE m.user_id = ?
      ${category ? 'AND m.category = ?' : ''}
    `;

    const countParams = category ? [userId, category] : [userId];
    const totalResult = await query(countSql, countParams);
    const total = totalResult[0]?.total || 0;

    // Parse JSON fields
    const processedModels = models.map((model: any) => ({
      ...model,
      additional_files: model.additional_files ? JSON.parse(model.additional_files) : [],
      additional_images: model.additional_images ? JSON.parse(model.additional_images) : [],
      print_settings: model.print_settings ? JSON.parse(model.print_settings) : {},
      tags: model.tags ? model.tags.split(',').map((tag: string) => tag.trim()) : [],
    }));

    return NextResponse.json({
      success: true,
      data: processedModels,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching user models:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch user models' },
      { status: 500 }
    );
  }
}
