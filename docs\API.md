# API Documentation

This document outlines the API endpoints available in the 3D Printing Marketplace application.

## Base URL

All API endpoints are relative to:

- Development: `http://localhost:3000/api`
- Production: `https://your-domain.com/api`
- Cloudflare: `https://your-project.pages.dev/api` or your custom domain

## Authentication

Most API endpoints require authentication. Include the authentication token in the request header:

```http
Authorization: Bearer YOUR_AUTH_TOKEN
```

You can obtain an authentication token by logging in through the `/api/auth/signin` endpoint.

### Authentication with Cloudflare

When deploying to Cloudflare, you have several options for authentication:

1. **NextAuth.js**: Works with Cloudflare Pages and handles authentication through the standard Next.js API routes.

2. **Cloudflare Access**: For internal APIs or admin functionality, consider using Cloudflare Access to add an additional layer of authentication.

3. **JWT Verification in Workers**: For custom authentication in Workers:

```js
// Example: JWT verification in a Cloudflare Worker
async function verifyToken(request) {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.split(' ')[1];

  try {
    // Verify JWT token (implementation depends on your JWT library)
    // This is a simplified example
    const decoded = await verifyJWT(token, SECRET_KEY);
    return decoded;
  } catch (error) {
    return null;
  }
}

export default {
  async fetch(request, env, ctx) {
    // Protected route example
    if (request.url.includes('/api/protected')) {
      const user = await verifyToken(request);

      if (!user) {
        return new Response(JSON.stringify({
          success: false,
          error: { message: 'Unauthorized' }
        }), {
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // Process authenticated request
      // ...
    }

    // Continue with other routes
  }
};
```

## Response Format

All API responses follow this general format:

```json
{
  "success": true|false,
  "data": { ... },  // Response data (if success is true)
  "error": { ... }  // Error details (if success is false)
}
```

## Error Codes

| Code | Description |
|------|-------------|
| 400  | Bad Request - Invalid input parameters |
| 401  | Unauthorized - Authentication required |
| 403  | Forbidden - Insufficient permissions |
| 404  | Not Found - Resource not found |
| 500  | Server Error - Internal server error |

## API Endpoints

### Authentication

#### Sign In

```http
POST /auth/signin
```

Request body:

```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

Response:

```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_id",
      "name": "User Name",
      "email": "<EMAIL>"
    },
    "token": "auth_token"
  }
}
```

#### Sign Up

```http
POST /auth/signup
```

Request body:

```json
{
  "name": "User Name",
  "email": "<EMAIL>",
  "password": "password123"
}
```

Response:

```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_id",
      "name": "User Name",
      "email": "<EMAIL>"
    }
  }
}
```

### Models

#### Get All Models

```http
GET /models
```

Query parameters:

- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)
- `category` (optional): Filter by category
- `search` (optional): Search term
- `source` (optional): Filter by source platform (local, printables, makerworld, thangs)
- `license` (optional): Filter by license type
- `isFree` (optional): Filter free models (true/false)

Response:

```json
{
  "success": true,
  "data": {
    "models": [
      {
        "id": "model_id",
        "title": "Model Title",
        "description": "Model description",
        "price": 10.99,
        "thumbnailUrl": "https://example.com/thumbnail.jpg",
        "createdAt": "2023-01-01T00:00:00Z",
        "source": "printables",
        "isFree": true,
        "license": {
          "type": "CC-BY",
          "name": "Creative Commons Attribution 4.0",
          "allowCommercialUse": true,
          "requireAttribution": true
        },
        "externalSource": {
          "platform": "printables",
          "originalUrl": "https://www.printables.com/model/123456",
          "importedAt": "2023-01-01T00:00:00Z"
        },
        "creator": {
          "id": "user_id",
          "name": "Creator Name"
        }
      }
    ],
    "pagination": {
      "total": 100,
      "page": 1,
      "limit": 20,
      "pages": 5
    }
  }
}
```

#### Get Model by ID

```http
GET /models/:id
```

Response:

```json
{
  "success": true,
  "data": {
    "id": "model_id",
    "title": "Model Title",
    "description": "Model description",
    "price": 10.99,
    "fileUrl": "https://example.com/model.stl",
    "thumbnailUrl": "https://example.com/thumbnail.jpg",
    "images": ["https://example.com/image1.jpg", "https://example.com/image2.jpg"],
    "category": "category_name",
    "tags": ["tag1", "tag2"],
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-02T00:00:00Z",
    "creator": {
      "id": "user_id",
      "name": "Creator Name",
      "profileUrl": "https://example.com/profile"
    },
    "stats": {
      "downloads": 100,
      "likes": 50,
      "views": 500
    }
  }
}
```

#### Create Model

```http
POST /models
```

Request body (multipart/form-data):

```yaml
title: "Model Title"
description: "Model description"
price: 10.99
category: "category_id"
tags: ["tag1", "tag2"]
modelFile: [FILE]
thumbnailImage: [FILE]
additionalImages: [FILE, FILE, ...]
```

Response:

```json
{
  "success": true,
  "data": {
    "id": "model_id",
    "title": "Model Title",
    "description": "Model description",
    "price": 10.99,
    "fileUrl": "https://example.com/model.stl",
    "thumbnailUrl": "https://example.com/thumbnail.jpg",
    "createdAt": "2023-01-01T00:00:00Z"
  }
}
```

#### Update Model

```http
PUT /models/:id
```

Request body:

```json
{
  "title": "Updated Title",
  "description": "Updated description",
  "price": 15.99,
  "category": "new_category_id",
  "tags": ["tag1", "tag3"]
}
```

Response:

```json
{
  "success": true,
  "data": {
    "id": "model_id",
    "title": "Updated Title",
    "description": "Updated description",
    "price": 15.99,
    "updatedAt": "2023-01-03T00:00:00Z"
  }
}
```

#### Delete Model

```http
DELETE /models/:id
```

Response:

```json
{
  "success": true,
  "data": {
    "message": "Model deleted successfully"
  }
}
```

### Web Scraping

#### Import Model from External Platform

```http
POST /api/scraping/import
```

Request body:

```json
{
  "url": "https://www.printables.com/model/123456-example-model",
  "options": {
    "includeFiles": true,
    "includeImages": true,
    "validateLicense": true,
    "autoPublish": false
  }
}
```

Response:

```json
{
  "success": true,
  "data": {
    "modelId": "imported_123456_1234567890",
    "status": "imported",
    "model": {
      "id": "imported_123456_1234567890",
      "title": "Example Model",
      "platform": "printables",
      "originalUrl": "https://www.printables.com/model/123456-example-model",
      "license": {
        "type": "CC-BY",
        "allowCommercialUse": true,
        "requireAttribution": true
      },
      "importedAt": "2023-01-01T00:00:00Z"
    }
  }
}
```

#### Platform-Specific Import Endpoints

```http
POST /api/scraping/printables
POST /api/scraping/makerworld
POST /api/scraping/thangs
```

Each endpoint accepts the same request format as the general import endpoint but is optimized for the specific platform.

#### Batch Import

```http
POST /api/scraping/batch
```

Request body:

```json
{
  "urls": [
    "https://www.printables.com/model/123456-model-1",
    "https://makerworld.com/en/models/789012",
    "https://thangs.com/designer/user/model/345678"
  ],
  "options": {
    "parallel": 3,
    "retryFailed": true,
    "includeFiles": true,
    "includeImages": true
  }
}
```

Response:

```json
{
  "success": true,
  "data": {
    "batchId": "batch_1234567890",
    "status": "processing",
    "total": 3,
    "completed": 0,
    "failed": 0,
    "results": []
  }
}
```

#### Get Batch Status

```http
GET /api/scraping/batch/:batchId
```

Response:

```json
{
  "success": true,
  "data": {
    "batchId": "batch_1234567890",
    "status": "completed",
    "total": 3,
    "completed": 2,
    "failed": 1,
    "results": [
      {
        "url": "https://www.printables.com/model/123456-model-1",
        "status": "success",
        "modelId": "imported_123456_1234567890"
      },
      {
        "url": "https://makerworld.com/en/models/789012",
        "status": "success",
        "modelId": "imported_789012_1234567891"
      },
      {
        "url": "https://thangs.com/designer/user/model/345678",
        "status": "failed",
        "error": "Model not found or private"
      }
    ]
  }
}
```

#### Scraping Health Check

```http
GET /api/scraping/health
```

Response:

```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "platforms": {
      "printables": {
        "status": "operational",
        "lastCheck": "2023-01-01T10:30:00Z",
        "responseTime": 245,
        "rateLimitRemaining": 8
      },
      "makerworld": {
        "status": "operational",
        "lastCheck": "2023-01-01T10:30:00Z",
        "responseTime": 312,
        "rateLimitRemaining": 12
      },
      "thangs": {
        "status": "degraded",
        "lastCheck": "2023-01-01T10:30:00Z",
        "responseTime": 1205,
        "rateLimitRemaining": 5
      }
    }
  }
}
```

### Orders

#### Create Order

```http
POST /orders
```

Request body:

```json
{
  "modelId": "model_id",
  "quantity": 1,
  "printService": true,
  "printOptions": {
    "material": "PLA",
    "color": "Red",
    "quality": "High",
    "infill": 20
  },
  "shippingAddress": {
    "name": "Recipient Name",
    "street": "123 Main St",
    "city": "Anytown",
    "state": "CA",
    "zip": "12345",
    "country": "US"
  }
}
```

Response:

```json
{
  "success": true,
  "data": {
    "orderId": "order_id",
    "checkoutUrl": "https://checkout.stripe.com/session/xyz"
  }
}
```

#### Get User Orders

```http
GET /orders
```

Response:

```json
{
  "success": true,
  "data": {
    "orders": [
      {
        "id": "order_id",
        "status": "completed",
        "total": 25.99,
        "createdAt": "2023-01-01T00:00:00Z",
        "items": [
          {
            "modelId": "model_id",
            "title": "Model Title",
            "price": 10.99,
            "quantity": 1,
            "thumbnailUrl": "https://example.com/thumbnail.jpg"
          }
        ]
      }
    ]
  }
}
```

## Webhooks

### Stripe Webhook

```http
POST /webhooks/stripe
```

This endpoint handles Stripe webhook events for payment processing. It's not meant to be called directly from your application.

## Rate Limiting

API requests are rate-limited to 100 requests per minute per IP address. If you exceed this limit, you'll receive a 429 Too Many Requests response.

## Cloudflare Integration

When deploying to Cloudflare Pages, there are several options for implementing API functionality:

### Next.js API Routes

Next.js API routes work seamlessly with Cloudflare Pages. The routes defined in `src/app/api` will be automatically deployed and available at the endpoints described in this document.

### Cloudflare Workers

For advanced use cases or performance optimization, you can implement API functionality using Cloudflare Workers:

```js
// Example: Implementing the GET /models endpoint with a Cloudflare Worker
export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);

    // Handle models endpoint
    if (url.pathname === '/api/models' && request.method === 'GET') {
      // Parse query parameters
      const params = new URLSearchParams(url.search);
      const page = parseInt(params.get('page') || '1');
      const limit = parseInt(params.get('limit') || '20');
      const category = params.get('category');
      const search = params.get('search');

      // Fetch data from your database (e.g., using D1 or external API)
      // This is a simplified example
      const models = await fetchModelsFromDatabase(page, limit, category, search);

      // Return JSON response
      return new Response(JSON.stringify({
        success: true,
        data: {
          models,
          pagination: {
            total: 100, // Replace with actual count
            page,
            limit,
            pages: Math.ceil(100 / limit)
          }
        }
      }), {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }

    // Handle 404 for unknown endpoints
    return new Response(JSON.stringify({
      success: false,
      error: {
        message: 'Not Found'
      }
    }), {
      status: 404,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
};
```

### Cloudflare D1 Database

For database operations, consider using Cloudflare D1 (SQLite-compatible database) for optimal performance:

```js
// Example: Accessing D1 database from a Worker
export default {
  async fetch(request, env, ctx) {
    // Access your D1 database
    const db = env.DB;

    // Example query
    const { results } = await db.prepare(
      "SELECT * FROM models WHERE category = ? LIMIT ? OFFSET ?"
    )
      .bind("Art", 20, 0)
      .all();

    return new Response(JSON.stringify({
      success: true,
      data: { models: results }
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
```

### Performance Considerations

When deploying API endpoints to Cloudflare:

1. **Edge Caching**: Consider adding cache headers to responses that don't change frequently
2. **Cold Starts**: Workers have minimal cold start times compared to traditional serverless functions
3. **Compute Limits**: Be aware of Cloudflare Workers CPU time limits for complex operations
4. **Bandwidth**: Monitor bandwidth usage as it may affect billing on high-traffic applications

### File Uploads with Cloudflare

For file uploads (like 3D models and images), Cloudflare provides several options:

#### Direct Upload to R2 Storage

```http
POST /api/upload/direct
```

Request body (multipart/form-data):

```yaml
file: [FILE]
modelId: "model_id" # Optional, for associating with a model
type: "model" # or "thumbnail" or "image"
```

Response:

```json
{
  "success": true,
  "data": {
    "url": "https://pub-XXXX.r2.dev/models/filename.stl",
    "key": "models/filename.stl",
    "size": 1024000
  }
}
```

#### Presigned Upload URLs

For larger files, use presigned URLs:

```http
GET /api/upload/presigned?filename=model.stl&type=model
```

Response:

```json
{
  "success": true,
  "data": {
    "uploadUrl": "https://pub-XXXX.r2.dev/models/filename.stl?signature=XXXX",
    "fileUrl": "https://pub-XXXX.r2.dev/models/filename.stl",
    "expiresAt": "2023-01-01T01:00:00Z"
  }
}
```

Then upload directly to the presigned URL:

```http
PUT https://pub-XXXX.r2.dev/models/filename.stl?signature=XXXX
Content-Type: application/octet-stream

[FILE_BINARY_DATA]
```
