import { hashPassword } from '@/lib/auth';
import { execute, generateId, getDb, queryOne } from '@/lib/db';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Схема валідації для реєстрації
const registerSchema = z.object({
  name: z.string().min(2, { message: "Ім'я має містити щонайменше 2 символи" }),
  email: z.string().email({ message: 'Невірний формат email' }),
  password: z.string().min(8, { message: 'Пароль має містити щонайменше 8 символів' }),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Валідація даних за допомогою Zod
    const result = registerSchema.safeParse(body);
    if (!result.success) {
      return NextResponse.json(
        { success: false, message: result.error.errors[0].message },
        { status: 400 }
      );
    }

    const { name, email, password } = result.data;

    // Перевірка, чи доступна база даних
    const db = getDb();
    if (!db) {
      console.warn('База даних недоступна. Використовується тестовий режим реєстрації.');

      // В режимі розробки імітуємо успішну реєстрацію
      if (email === '<EMAIL>') {
        return NextResponse.json(
          { success: false, message: 'Користувач з таким email вже існує' },
          { status: 409 }
        );
      }

      const userId = generateId();

      return NextResponse.json({
        success: true,
        message: 'Користувач успішно зареєстрований (тестовий режим)',
        data: {
          id: userId,
          email,
          name,
        }
      }, { status: 201 });
    }

    // Перевірка, чи існує користувач з такою електронною поштою
    const existingUser = await queryOne(
      'SELECT id FROM users WHERE email = ?',
      [email]
    );

    if (existingUser) {
      return NextResponse.json(
        { success: false, message: 'Користувач з таким email вже існує' },
        { status: 409 }
      );
    }

    // Хешування пароля
    const hashedPassword = await hashPassword(password);

    // Створення нового користувача
    const userId = generateId();

    await execute(
      `INSERT INTO users (
        id, email, name, password, created_at, updated_at
      ) VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
      [
        userId,
        email,
        name,
        hashedPassword
      ]
    );

    return NextResponse.json({
      success: true,
      message: 'Користувач успішно зареєстрований',
      data: {
        id: userId,
        email,
        name,
      }
    }, { status: 201 });
  } catch (error) {
    console.error('Помилка при реєстрації користувача:', error);
    return NextResponse.json(
      { success: false, message: 'Сталася помилка при реєстрації. Спробуйте ще раз.' },
      { status: 500 }
    );
  }
}
