'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  Search,
  Filter,
  X,
  ChevronDown,
  SlidersHorizontal,
  Tag,
  DollarSign,
  Calendar,
  TrendingUp
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { Badge } from '@/components/ui/badge';

interface SearchFilters {
  query: string;
  category: string;
  priceRange: [number, number];
  sortBy: string;
  tags: string[];
  isFree: boolean | null;
  dateRange: string;
  minDownloads: number;
  fileFormats: string[];
}

const categories = [
  'Всі категорії',
  'Абстракції',
  'Архітектура', 
  'Транспорт',
  'Ігри',
  'Будинки',
  'Меблі',
  'Кухня',
  'Інше'
];

const sortOptions = [
  { value: 'newest', label: 'Найновіші' },
  { value: 'oldest', label: 'Найстаріші' },
  { value: 'popular', label: 'Популярні' },
  { value: 'price_asc', label: 'Ціна: від низької' },
  { value: 'price_desc', label: 'Ціна: від високої' },
  { value: 'name', label: 'За назвою' },
  { value: 'downloads', label: 'За завантаженнями' },
];

const fileFormats = ['STL', 'OBJ', 'PLY', 'FBX', 'GLTF', '3MF', 'AMF'];

const popularTags = [
  'мініатюра', 'настільна гра', 'декор', 'іграшка', 'функціональний',
  'механізм', 'фігурка', 'посуд', 'інструмент', 'прикраса'
];

interface AdvancedSearchProps {
  onFiltersChange: (filters: SearchFilters) => void;
  initialFilters?: Partial<SearchFilters>;
}

export default function AdvancedSearch({ onFiltersChange, initialFilters }: AdvancedSearchProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [filters, setFilters] = useState<SearchFilters>({
    query: searchParams.get('search') || '',
    category: searchParams.get('category') || 'Всі категорії',
    priceRange: [0, 100],
    sortBy: searchParams.get('sortBy') || 'newest',
    tags: [],
    isFree: null,
    dateRange: 'all',
    minDownloads: 0,
    fileFormats: [],
    ...initialFilters,
  });

  const [isFiltersOpen, setIsFiltersOpen] = useState(false);
  const [activeFiltersCount, setActiveFiltersCount] = useState(0);

  // Підрахунок активних фільтрів
  useEffect(() => {
    let count = 0;
    if (filters.query) count++;
    if (filters.category !== 'Всі категорії') count++;
    if (filters.priceRange[0] > 0 || filters.priceRange[1] < 100) count++;
    if (filters.tags.length > 0) count++;
    if (filters.isFree !== null) count++;
    if (filters.dateRange !== 'all') count++;
    if (filters.minDownloads > 0) count++;
    if (filters.fileFormats.length > 0) count++;
    
    setActiveFiltersCount(count);
  }, [filters]);

  // Оновлення фільтрів
  const updateFilters = (newFilters: Partial<SearchFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    onFiltersChange(updatedFilters);
  };

  // Скидання фільтрів
  const resetFilters = () => {
    const defaultFilters: SearchFilters = {
      query: '',
      category: 'Всі категорії',
      priceRange: [0, 100],
      sortBy: 'newest',
      tags: [],
      isFree: null,
      dateRange: 'all',
      minDownloads: 0,
      fileFormats: [],
    };
    setFilters(defaultFilters);
    onFiltersChange(defaultFilters);
  };

  // Додавання/видалення тегів
  const toggleTag = (tag: string) => {
    const newTags = filters.tags.includes(tag)
      ? filters.tags.filter(t => t !== tag)
      : [...filters.tags, tag];
    updateFilters({ tags: newTags });
  };

  // Додавання/видалення форматів файлів
  const toggleFileFormat = (format: string) => {
    const newFormats = filters.fileFormats.includes(format)
      ? filters.fileFormats.filter(f => f !== format)
      : [...filters.fileFormats, format];
    updateFilters({ fileFormats: newFormats });
  };

  const FilterContent = () => (
    <div className="space-y-6">
      {/* Категорія */}
      <div>
        <Label className="text-sm font-medium mb-3 block">Категорія</Label>
        <Select value={filters.category} onValueChange={(value) => updateFilters({ category: value })}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {categories.map((category) => (
              <SelectItem key={category} value={category}>
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Ціновий діапазон */}
      <div>
        <Label className="text-sm font-medium mb-3 block">
          Ціна: ${filters.priceRange[0]} - ${filters.priceRange[1]}
        </Label>
        <Slider
          value={filters.priceRange}
          onValueChange={(value) => updateFilters({ priceRange: value as [number, number] })}
          max={100}
          min={0}
          step={1}
          className="w-full"
        />
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>$0</span>
          <span>$100+</span>
        </div>
      </div>

      {/* Безкоштовні/Платні */}
      <div>
        <Label className="text-sm font-medium mb-3 block">Тип</Label>
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="free"
              checked={filters.isFree === true}
              onCheckedChange={(checked) => 
                updateFilters({ isFree: checked ? true : null })
              }
            />
            <Label htmlFor="free" className="text-sm">Тільки безкоштовні</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="paid"
              checked={filters.isFree === false}
              onCheckedChange={(checked) => 
                updateFilters({ isFree: checked ? false : null })
              }
            />
            <Label htmlFor="paid" className="text-sm">Тільки платні</Label>
          </div>
        </div>
      </div>

      {/* Теги */}
      <div>
        <Label className="text-sm font-medium mb-3 block">Популярні теги</Label>
        <div className="flex flex-wrap gap-2">
          {popularTags.map((tag) => (
            <Badge
              key={tag}
              variant={filters.tags.includes(tag) ? "default" : "outline"}
              className="cursor-pointer"
              onClick={() => toggleTag(tag)}
            >
              {tag}
            </Badge>
          ))}
        </div>
      </div>

      {/* Формати файлів */}
      <div>
        <Label className="text-sm font-medium mb-3 block">Формати файлів</Label>
        <div className="grid grid-cols-2 gap-2">
          {fileFormats.map((format) => (
            <div key={format} className="flex items-center space-x-2">
              <Checkbox
                id={format}
                checked={filters.fileFormats.includes(format)}
                onCheckedChange={() => toggleFileFormat(format)}
              />
              <Label htmlFor={format} className="text-sm">{format}</Label>
            </div>
          ))}
        </div>
      </div>

      {/* Мінімальна кількість завантажень */}
      <div>
        <Label className="text-sm font-medium mb-3 block">
          Мін. завантажень: {filters.minDownloads}
        </Label>
        <Slider
          value={[filters.minDownloads]}
          onValueChange={(value) => updateFilters({ minDownloads: value[0] })}
          max={1000}
          min={0}
          step={10}
          className="w-full"
        />
      </div>

      {/* Період */}
      <div>
        <Label className="text-sm font-medium mb-3 block">Період</Label>
        <Select value={filters.dateRange} onValueChange={(value) => updateFilters({ dateRange: value })}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Весь час</SelectItem>
            <SelectItem value="today">Сьогодні</SelectItem>
            <SelectItem value="week">Цей тиждень</SelectItem>
            <SelectItem value="month">Цей місяць</SelectItem>
            <SelectItem value="year">Цей рік</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Кнопки дій */}
      <div className="flex space-x-2 pt-4 border-t">
        <Button onClick={resetFilters} variant="outline" className="flex-1">
          Скинути
        </Button>
        <Button onClick={() => setIsFiltersOpen(false)} className="flex-1">
          Застосувати
        </Button>
      </div>
    </div>
  );

  return (
    <div className="space-y-4">
      {/* Основний пошук */}
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Пошук моделей..."
            value={filters.query}
            onChange={(e) => updateFilters({ query: e.target.value })}
            className="pl-10"
          />
        </div>
        
        <Select value={filters.sortBy} onValueChange={(value) => updateFilters({ sortBy: value })}>
          <SelectTrigger className="w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {sortOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Кнопка фільтрів для мобільних */}
        <Sheet open={isFiltersOpen} onOpenChange={setIsFiltersOpen}>
          <SheetTrigger asChild>
            <Button variant="outline" className="relative">
              <SlidersHorizontal className="h-4 w-4 mr-2" />
              Фільтри
              {activeFiltersCount > 0 && (
                <Badge className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs">
                  {activeFiltersCount}
                </Badge>
              )}
            </Button>
          </SheetTrigger>
          <SheetContent>
            <SheetHeader>
              <SheetTitle>Фільтри пошуку</SheetTitle>
              <SheetDescription>
                Налаштуйте параметри для точного пошуку
              </SheetDescription>
            </SheetHeader>
            <div className="mt-6">
              <FilterContent />
            </div>
          </SheetContent>
        </Sheet>
      </div>

      {/* Активні фільтри */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap gap-2 items-center">
          <span className="text-sm text-gray-500">Активні фільтри:</span>
          {filters.category !== 'Всі категорії' && (
            <Badge variant="secondary" className="gap-1">
              {filters.category}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => updateFilters({ category: 'Всі категорії' })}
              />
            </Badge>
          )}
          {filters.tags.map((tag) => (
            <Badge key={tag} variant="secondary" className="gap-1">
              {tag}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => toggleTag(tag)}
              />
            </Badge>
          ))}
          {filters.isFree !== null && (
            <Badge variant="secondary" className="gap-1">
              {filters.isFree ? 'Безкоштовні' : 'Платні'}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => updateFilters({ isFree: null })}
              />
            </Badge>
          )}
          <Button variant="ghost" size="sm" onClick={resetFilters}>
            Скинути всі
          </Button>
        </div>
      )}
    </div>
  );
}
