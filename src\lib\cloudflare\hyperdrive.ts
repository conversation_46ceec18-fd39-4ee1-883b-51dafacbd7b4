/**
 * Утиліти для роботи з Cloudflare Hyperdrive
 */

// Інтерфейс для Hyperdrive
export interface Hyperdrive {
  query: <T = any>(query: string, params?: any[]) => Promise<HyperdriveResult<T>>;
  prepare: (query: string) => HyperdrivePreparedStatement;
  transaction: <T = any>(callback: (tx: Hyperdrive) => Promise<T>) => Promise<T>;
}

// Інтерфейс для результату запиту Hyperdrive
export interface HyperdriveResult<T = any> {
  rows: T[];
  rowCount: number;
  duration: number;
}

// Інтерфейс для підготовленого запиту Hyperdrive
export interface HyperdrivePreparedStatement {
  bind: (...params: any[]) => HyperdrivePreparedStatement;
  execute: <T = any>() => Promise<HyperdriveResult<T>>;
  first: <T = any>() => Promise<T | null>;
  all: <T = any>() => Promise<T[]>;
}

// Глобальна змінна для зберігання екземпляра Hyperdrive
let hyperdrive: Hyperdrive | null = null;

/**
 * Отримує екземпляр Hyperdrive
 * @returns Екземпляр Hyperdrive
 */
export function getHyperdrive(): Hyperdrive {
  if (!hyperdrive) {
    // В середовищі Cloudflare Pages, Hyperdrive доступний через env.HYPERDRIVE
    if (process.env.HYPERDRIVE) {
      hyperdrive = process.env.HYPERDRIVE as unknown as Hyperdrive;
    } else if (globalThis.HYPERDRIVE) {
      // Для серверних компонентів Next.js
      hyperdrive = globalThis.HYPERDRIVE as unknown as Hyperdrive;
    } else {
      throw new Error('Hyperdrive недоступний. Переконайтеся, що ви налаштували Hyperdrive в wrangler.toml і запустили проект з прапорцем --hyperdrive=HYPERDRIVE');
    }
  }
  return hyperdrive;
}

/**
 * Виконує SQL-запит до Hyperdrive
 * @param query SQL-запит
 * @param params Параметри запиту
 * @returns Результат запиту
 */
export async function queryHyperdrive<T = any>(
  query: string,
  params: any[] = []
): Promise<T[]> {
  const hyperdrive = getHyperdrive();
  const result = await hyperdrive.query<T>(query, params);
  return result.rows;
}

/**
 * Отримує один запис з Hyperdrive
 * @param query SQL-запит
 * @param params Параметри запиту
 * @returns Перший запис або null, якщо запис не знайдено
 */
export async function queryHyperdriveOne<T = any>(
  query: string,
  params: any[] = []
): Promise<T | null> {
  const hyperdrive = getHyperdrive();
  const stmt = hyperdrive.prepare(query);
  return await stmt.bind(...params).first<T>();
}

/**
 * Виконує транзакцію в Hyperdrive
 * @param callback Функція, яка виконується в транзакції
 * @returns Результат виконання функції
 */
export async function hyperdriveTransaction<T = any>(
  callback: (tx: Hyperdrive) => Promise<T>
): Promise<T> {
  const hyperdrive = getHyperdrive();
  return await hyperdrive.transaction(callback);
}

/**
 * Створює підготовлений запит до Hyperdrive
 * @param query SQL-запит
 * @returns Підготовлений запит
 */
export function prepareHyperdriveStatement(query: string): HyperdrivePreparedStatement {
  const hyperdrive = getHyperdrive();
  return hyperdrive.prepare(query);
}

/**
 * Виконує запит з пагінацією до Hyperdrive
 * @param query SQL-запит без LIMIT та OFFSET
 * @param params Параметри запиту
 * @param page Номер сторінки (починається з 1)
 * @param limit Кількість записів на сторінці
 * @returns Результат запиту з пагінацією
 */
export async function queryHyperdriveWithPagination<T = any>(
  query: string,
  params: any[] = [],
  page: number = 1,
  limit: number = 10
): Promise<{ data: T[]; total: number; page: number; limit: number; totalPages: number }> {
  const hyperdrive = getHyperdrive();
  
  // Запит для отримання загальної кількості записів
  const countQuery = `SELECT COUNT(*) as total FROM (${query})`;
  const countResult = await hyperdrive.query<{ total: number }>(countQuery, params);
  const total = countResult.rows[0]?.total || 0;
  
  // Запит з пагінацією
  const paginatedQuery = `${query} LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
  const offset = (page - 1) * limit;
  const paginatedParams = [...params, limit, offset];
  
  const result = await hyperdrive.query<T>(paginatedQuery, paginatedParams);
  
  return {
    data: result.rows,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
  };
}
