/**
 * NextAuth API route з Cloudflare інтеграцією
 */

import { authOptions } from "@/lib/auth/config";
import { initializeCloudflare } from "@/lib/cloudflare";
import NextAuth from "next-auth";

// Ініціалізуємо Cloudflare сервіси для аутентифікації
try {
  // В Cloudflare Pages environment змінні доступні через process.env
  const env = {
    DB: process.env.DB,
    CACHE_KV: process.env.CACHE_KV,
    R2_BUCKET: process.env.R2_BUCKET,
    ANALYTICS: process.env.ANALYTICS,
    ENVIRONMENT: process.env.NODE_ENV || 'development'
  };

  initializeCloudflare(env);
} catch (error) {
  console.warn('Failed to initialize Cloudflare for auth:', error);
}

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
