#!/bin/bash
set -x

# 🚀 Cloudflare Deployment Script for 3D Marketplace
# This script deploys your entire 3D marketplace to Cloudflare

echo "🎨 3D Marketplace - Cloudflare Deployment"
echo "=========================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
print_step() {
    echo -e "${BLUE}📦 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if wrangler is installed
check_wrangler() {
    print_step "Checking Wrangler CLI..."
    if ! command -v wrangler &> /dev/null; then
        print_warning "Wrangler CLI not found. Installing..."
        npm install -g wrangler
        if [ $? -eq 0 ]; then
            print_success "Wrangler CLI installed successfully"
        else
            print_error "Failed to install Wrangler CLI"
            exit 1
        fi
    else
        print_success "Wrangler CLI is installed"
    fi
}

# Check authentication
check_auth() {
    print_step "Checking Cloudflare authentication..."
    if wrangler whoami &> /dev/null; then
        print_success "Authenticated with Cloudflare"
    else
        print_error "Not authenticated with Cloudflare"
        echo "Please run: wrangler login"
        exit 1
    fi
}

# Build the project
build_project() {
    print_step "Building Next.js application..."
    npm run build
    if [ $? -eq 0 ]; then
        print_success "Build completed successfully"
    else
        print_error "Build failed"
        exit 1
    fi
}

# Create Cloudflare resources
create_resources() {
    print_step "Creating Cloudflare resources..."
    
    # Create D1 Database
    print_step "Creating D1 database..."
    wrangler d1 create marketplace-db || print_warning "Database might already exist"
    
    # Create R2 bucket
    print_step "Creating R2 storage bucket..."
    wrangler r2 bucket create marketplace-storage || print_warning "Bucket might already exist"
    
    # Create KV namespace
    print_step "Creating KV namespace..."
    wrangler kv:namespace create "CACHE_KV" || print_warning "KV namespace might already exist"
    
    print_success "Resources creation completed"
}

# Deploy to Cloudflare Pages
deploy_pages() {
    print_step "Deploying to Cloudflare Pages..."
    
    # Deploy the built application
    npm run deploy:quick -- --project-name=3d-marketplace --compatibility-date=2023-10-30 --d1 DB="marketplace-db" --kv CACHE_KV="CACHE_KV" --r2 STORAGE="marketplace-storage" --analytics-engine-dataset ANALYTICS_ENGINE="3d-marketplace-analytics"
    
    if [ $? -eq 0 ]; then
        print_success "Deployment to Cloudflare Pages completed"
    else
        print_error "Deployment failed"
        exit 1
    fi
}

# Set environment variables
set_env_vars() {
    print_step "Setting environment variables..."
    
    # Set secrets
    echo "your-secret-key-here" | wrangler pages secret put NEXTAUTH_SECRET --project-name=3d-marketplace || print_warning "Could not set NEXTAUTH_SECRET"
    echo "production" | wrangler pages secret put NODE_ENV --project-name=3d-marketplace || print_warning "Could not set NODE_ENV"
    
    print_success "Environment variables set"
}

# Main deployment function
main() {
    echo "Starting deployment process..."
    echo ""
    
    # Step 1: Check prerequisites
    check_wrangler
    check_auth
    
    # Step 2: Build project
    build_project
    
    # Step 3: Create Cloudflare resources
    create_resources
    
    # Step 4: Deploy to Pages
    deploy_pages
    
    # Step 5: Set environment variables
    set_env_vars
    
    # Success message
    echo ""
    echo "🎉 Deployment completed successfully!"
    echo ""
    echo "📱 Your 3D Marketplace is now live at:"
    echo "   https://3d-marketplace.pages.dev"
    echo ""
    echo "🔧 Next steps:"
    echo "   1. Visit your site and test functionality"
    echo "   2. Go to /admin/scraper to generate test data"
    echo "   3. Check /marketplace to see your models"
    echo "   4. Configure custom domain if needed"
    echo ""
    echo "📚 Useful commands:"
    echo "   wrangler pages deployment list --project-name=3d-marketplace"
    echo "   wrangler d1 info marketplace-db"
    echo "   wrangler r2 bucket list"
    echo ""
}

# Run deployment
main
