'use client';

import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Shapes, Building2, Car, Gamepad2, Home, Sofa, Utensils, Box } from 'lucide-react';

interface CategoryCardProps {
  icon: React.ReactNode;
  title: string;
  count: number;
  slug: string;
  className?: string;
}

const CategoryCard = ({ icon, title, count, slug, className }: CategoryCardProps) => {
  return (
    <Card
      className={`flex flex-col items-center justify-center p-6 transition-all duration-200 hover:shadow-md cursor-pointer ${className}`}
      onClick={() => window.location.href = `/marketplace?category=${encodeURIComponent(slug)}`}
    >
      <div className="mb-4 p-3 rounded-full bg-muted flex items-center justify-center">
        {icon}
      </div>
      <h3 className="text-lg font-medium mb-1">{title}</h3>
      <p className="text-sm text-muted-foreground">{count} моделей</p>
    </Card>
  );
};

interface CategoriesGridProps {
  className?: string;
}

const CategoriesDemo = ({ className }: CategoriesGridProps) => {
  const categories = [
    { icon: <Shapes className="h-6 w-6 stroke-primary" />, title: "Абстракції", count: 245, slug: "abstract" },
    { icon: <Building2 className="h-6 w-6 stroke-primary" />, title: "Архітектура", count: 183, slug: "architecture" },
    { icon: <Car className="h-6 w-6 stroke-primary" />, title: "Транспорт", count: 127, slug: "vehicles" },
    { icon: <Gamepad2 className="h-6 w-6 stroke-primary" />, title: "Ігри", count: 89, slug: "games" },
    { icon: <Home className="h-6 w-6 stroke-primary" />, title: "Будинки", count: 156, slug: "buildings" },
    { icon: <Sofa className="h-6 w-6 stroke-primary" />, title: "Меблі", count: 204, slug: "furniture" },
    { icon: <Utensils className="h-6 w-6 stroke-primary" />, title: "Кухня", count: 78, slug: "kitchen" },
    { icon: <Box className="h-6 w-6 stroke-primary" />, title: "Інше", count: 112, slug: "other" },
  ];

  return (
    <div className={`w-full py-12 ${className}`}>
      <div className="container mx-auto">
        <div className="flex flex-col gap-10">
          <div className="flex gap-4 flex-col items-start">
            <Badge>Категорії</Badge>
            <div className="flex gap-2 flex-col">
              <h2 className="text-3xl md:text-4xl tracking-tighter font-medium">
                Знайдіть потрібну 3D-модель
              </h2>
              <p className="text-lg max-w-2xl leading-relaxed text-muted-foreground">
                Перегляньте нашу колекцію 3D-моделей за категоріями та знайдіть ідеальний варіант для вашого проекту.
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {categories.map((category, index) => (
              <CategoryCard
                key={index}
                icon={category.icon}
                title={category.title}
                count={category.count}
                slug={category.slug}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CategoriesDemo;
