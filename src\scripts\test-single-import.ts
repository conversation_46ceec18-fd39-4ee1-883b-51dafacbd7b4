#!/usr/bin/env tsx

/**
 * Тестовий скрипт для перевірки імпорту одиночної моделі
 */

import { thingiverseScraper } from '@/lib/api/thingiverse';
import { myMiniFactoryScraper } from '@/lib/api/myminifactory';
import { cloudflareMonitoring } from '@/lib/observability/cloudflare-monitoring';

// Тестові URL-и (перевірені та актуальні)
const TEST_URLS = {
  thingiverse: 'https://www.thingiverse.com/thing:3495390', // Articulated Dragon
  myminifactory: 'https://www.myminifactory.com/object/3d-print-articulated-dragon-mcgybeer-738'
};

async function testSingleImport() {
  console.log('🧪 Тестування імпорту одиночних моделей');
  console.log('=' .repeat(50));

  // Тест Thingiverse
  console.log('\n🔧 Тестування Thingiverse...');
  try {
    const thingiverseResult = await thingiverseScraper.scrapeModel(TEST_URLS.thingiverse);
    console.log('✅ Thingiverse успішно:', {
      title: thingiverseResult.title,
      platform: thingiverseResult.platform,
      designer: thingiverseResult.designer.name,
      images: thingiverseResult.images.length,
      files: thingiverseResult.files.length
    });

    await cloudflareMonitoring.trackScrapingOperation('thingiverse', 'success', {
      modelTitle: thingiverseResult.title,
      imagesCount: thingiverseResult.images.length,
      filesCount: thingiverseResult.files.length
    });

  } catch (error) {
    console.error('❌ Thingiverse помилка:', error instanceof Error ? error.message : error);
    
    await cloudflareMonitoring.trackScrapingOperation('thingiverse', 'error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      url: TEST_URLS.thingiverse
    });
  }

  // Тест MyMiniFactory
  console.log('\n🏭 Тестування MyMiniFactory...');
  try {
    const myMiniFactoryResult = await myMiniFactoryScraper.scrapeModel(TEST_URLS.myminifactory);
    console.log('✅ MyMiniFactory успішно:', {
      title: myMiniFactoryResult.title,
      platform: myMiniFactoryResult.platform,
      designer: myMiniFactoryResult.designer.name,
      images: myMiniFactoryResult.images.length,
      files: myMiniFactoryResult.files.length,
      isFree: myMiniFactoryResult.isFree,
      price: myMiniFactoryResult.price
    });

    await cloudflareMonitoring.trackScrapingOperation('myminifactory', 'success', {
      modelTitle: myMiniFactoryResult.title,
      imagesCount: myMiniFactoryResult.images.length,
      filesCount: myMiniFactoryResult.files.length,
      isFree: myMiniFactoryResult.isFree
    });

  } catch (error) {
    console.error('❌ MyMiniFactory помилка:', error instanceof Error ? error.message : error);
    
    await cloudflareMonitoring.trackScrapingOperation('myminifactory', 'error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      url: TEST_URLS.myminifactory
    });
  }

  console.log('\n🎯 Тестування завершено!');
  console.log('📊 Перевірте логи Cloudflare Observability для детальної інформації');
}

// Запуск тесту
if (require.main === module) {
  testSingleImport().catch(console.error);
}
