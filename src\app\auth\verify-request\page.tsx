'use client';

import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { Suspense } from 'react';

function VerifyRequestContent() {
  const searchParams = useSearchParams();
  const email = searchParams.get('email') || '';

  return (
    <main className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Перевірте вашу електронну пошту
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Ми надіслали посилання для входу на вашу електронну пошту
          </p>
        </div>

        <div className="mt-8 bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            <div className="text-blue-500 mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Посилання надіслано</h3>
            <p className="text-sm text-gray-600 mb-6">
              {email ? (
                <>
                  Ми надіслали посилання для входу на адресу <strong>{email}</strong>. Перевірте вашу електронну пошту та перейдіть за посиланням для входу в систему.
                </>
              ) : (
                <>
                  Ми надіслали посилання для входу на вашу електронну пошту. Перевірте вашу електронну пошту та перейдіть за посиланням для входу в систему.
                </>
              )}
            </p>
            <p className="text-xs text-gray-500 mb-6">
              Якщо ви не отримали лист, перевірте папку &quot;Спам&quot; або &quot;Небажана пошта&quot;.
            </p>

            <div className="mt-6 flex justify-center space-x-4">
              <Link href="/auth/signin" className="text-sm font-medium text-blue-600 hover:text-blue-500">
                Повернутися до сторінки входу
              </Link>
              <span className="text-gray-300">|</span>
              <Link href="/" className="text-sm font-medium text-blue-600 hover:text-blue-500">
                На головну
              </Link>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}

export default function VerifyRequest() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    }>
      <VerifyRequestContent />
    </Suspense>
  );
}
