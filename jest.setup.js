// Jest setup file
import 'jest-environment-node'

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter() {
    return {
      route: '/',
      pathname: '/',
      query: {},
      asPath: '/',
      push: jest.fn(),
      replace: jest.fn(),
      reload: jest.fn(),
      back: jest.fn(),
      prefetch: jest.fn(),
      beforePopState: jest.fn(),
      events: {
        on: jest.fn(),
        off: jest.fn(),
        emit: jest.fn(),
      },
    }
  },
}))

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      refresh: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
    }
  },
  useSearchParams() {
    return new URLSearchParams()
  },
  usePathname() {
    return '/'
  },
}))

// Mock environment variables
process.env.NODE_ENV = 'test'
process.env.NEXT_PUBLIC_API_URL = 'http://localhost:3000'

// Mock fetch globally
global.fetch = jest.fn()

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  // Uncomment to ignore specific log levels
  // log: jest.fn(),
  // debug: jest.fn(),
  // info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
}

// Mock window object for browser-specific tests
if (typeof window !== 'undefined') {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: jest.fn(), // deprecated
      removeListener: jest.fn(), // deprecated
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    })),
  })
} else {
  // Create a minimal window mock for node environment
  global.window = {
    matchMedia: jest.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    })),
  }
}

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}))

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}))

// Extend Jest matchers
expect.extend({
  toBeValidUrl(received) {
    try {
      new URL(received)
      return {
        message: () => `expected ${received} not to be a valid URL`,
        pass: true,
      }
    } catch {
      return {
        message: () => `expected ${received} to be a valid URL`,
        pass: false,
      }
    }
  },

  toHaveValidImageExtension(received) {
    const validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg']
    const hasValidExtension = validExtensions.some(ext =>
      received.toLowerCase().includes(ext)
    )

    return {
      message: () => hasValidExtension
        ? `expected ${received} not to have a valid image extension`
        : `expected ${received} to have a valid image extension`,
      pass: hasValidExtension,
    }
  },
})

// Global test utilities
global.testUtils = {
  // Helper to create mock scraped model
  createMockScrapedModel: (overrides = {}) => ({
    title: 'Test Model',
    description: 'Test description',
    summary: 'Test summary',
    images: [{ id: '1', url: 'https://example.com/image.jpg' }],
    thumbnail: 'https://example.com/thumb.jpg',
    files: [{
      id: '1',
      name: 'test.stl',
      url: 'https://example.com/file.stl',
      downloadUrl: 'https://example.com/download/file.stl',
      size: 1024,
      format: 'STL'
    }],
    fileFormats: ['STL'],
    totalSize: 1024,
    designer: { id: 'test-designer', name: 'Test Designer' },
    tags: ['test', 'model'],
    category: 'Test',
    license: {
      type: 'CC-BY',
      name: 'Creative Commons Attribution 4.0',
      detected: true,
      confidence: 0.9,
      allowCommercialUse: true,
      requireAttribution: true,
      allowDerivatives: true,
    },
    stats: { views: 100, downloads: 50, likes: 25, comments: 10 },
    platform: 'printables',
    originalId: '123456',
    originalUrl: 'https://www.printables.com/model/123456',
    scrapedAt: new Date().toISOString(),
    isFree: true,
    ...overrides,
  }),

  // Helper to create mock HTTP response
  createMockResponse: (data, status = 200) => ({
    ok: status >= 200 && status < 300,
    status,
    statusText: status === 200 ? 'OK' : 'Error',
    json: jest.fn().mockResolvedValue(data),
    text: jest.fn().mockResolvedValue(JSON.stringify(data)),
    headers: new Headers(),
  }),

  // Helper to wait for async operations
  waitFor: (ms = 0) => new Promise(resolve => setTimeout(resolve, ms)),
}
