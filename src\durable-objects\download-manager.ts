export interface Env {
  DOWNLOAD_MANAGER: DurableObjectNamespace;
  DB: D1Database;
  R2_BUCKET: R2Bucket;
}

export interface DownloadRequest {
  modelId: string;
  userId: string;
  userEmail: string;
  timestamp: number;
  ipAddress?: string;
  userAgent?: string;
}

export interface DownloadSession {
  id: string;
  modelId: string;
  userId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'expired';
  downloadUrl?: string;
  expiresAt: number;
  createdAt: number;
  attempts: number;
  maxAttempts: number;
}

export class DownloadManager {
  private state: DurableObjectState;
  private env: Env;
  private sessions: Map<string, DownloadSession> = new Map();

  constructor(state: DurableObjectState, env: Env) {
    this.state = state;
    this.env = env;
    
    // Відновлюємо стан з persistent storage
    this.state.blockConcurrencyWhile(async () => {
      const stored = await this.state.storage.get<Map<string, DownloadSession>>('sessions');
      if (stored) {
        this.sessions = new Map(stored);
      }
    });

    // Очищуємо застарілі сесії кожні 5 хвилин
    setInterval(() => this.cleanupExpiredSessions(), 5 * 60 * 1000);
  }

  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const path = url.pathname;

    try {
      switch (path) {
        case '/create-session':
          return await this.createDownloadSession(request);
        case '/get-session':
          return await this.getDownloadSession(request);
        case '/download':
          return await this.processDownload(request);
        case '/cleanup':
          return await this.cleanupExpiredSessions();
        default:
          return new Response('Not Found', { status: 404 });
      }
    } catch (error) {
      console.error('DownloadManager error:', error);
      return new Response('Internal Server Error', { status: 500 });
    }
  }

  private async createDownloadSession(request: Request): Promise<Response> {
    const downloadRequest: DownloadRequest = await request.json();
    
    // Перевіряємо права доступу до моделі
    const hasAccess = await this.checkModelAccess(downloadRequest.modelId, downloadRequest.userId);
    if (!hasAccess) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Access denied'
      }), { 
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Створюємо унікальну сесію завантаження
    const sessionId = crypto.randomUUID();
    const session: DownloadSession = {
      id: sessionId,
      modelId: downloadRequest.modelId,
      userId: downloadRequest.userId,
      status: 'pending',
      expiresAt: Date.now() + (30 * 60 * 1000), // 30 хвилин
      createdAt: Date.now(),
      attempts: 0,
      maxAttempts: 3
    };

    this.sessions.set(sessionId, session);
    await this.persistSessions();

    // Логуємо спробу завантаження
    await this.logDownloadAttempt(downloadRequest);

    return new Response(JSON.stringify({
      success: true,
      sessionId,
      expiresAt: session.expiresAt
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  private async getDownloadSession(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const sessionId = url.searchParams.get('sessionId');

    if (!sessionId) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Session ID required'
      }), { 
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const session = this.sessions.get(sessionId);
    if (!session) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Session not found'
      }), { 
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Перевіряємо, чи не застаріла сесія
    if (Date.now() > session.expiresAt) {
      this.sessions.delete(sessionId);
      await this.persistSessions();
      
      return new Response(JSON.stringify({
        success: false,
        error: 'Session expired'
      }), { 
        status: 410,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      session: {
        id: session.id,
        status: session.status,
        downloadUrl: session.downloadUrl,
        expiresAt: session.expiresAt,
        attempts: session.attempts,
        maxAttempts: session.maxAttempts
      }
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  private async processDownload(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const sessionId = url.searchParams.get('sessionId');

    if (!sessionId) {
      return new Response('Session ID required', { status: 400 });
    }

    const session = this.sessions.get(sessionId);
    if (!session || Date.now() > session.expiresAt) {
      return new Response('Session not found or expired', { status: 404 });
    }

    if (session.attempts >= session.maxAttempts) {
      return new Response('Maximum download attempts exceeded', { status: 429 });
    }

    try {
      // Оновлюємо статус сесії
      session.status = 'processing';
      session.attempts += 1;
      await this.persistSessions();

      // Отримуємо інформацію про модель
      const model = await this.getModelInfo(session.modelId);
      if (!model) {
        session.status = 'failed';
        await this.persistSessions();
        return new Response('Model not found', { status: 404 });
      }

      // Генеруємо підписане URL для завантаження з R2
      const downloadUrl = await this.generateSignedDownloadUrl(model.model_url);
      
      session.status = 'completed';
      session.downloadUrl = downloadUrl;
      await this.persistSessions();

      // Оновлюємо лічильник завантажень
      await this.incrementDownloadCount(session.modelId);

      return new Response(JSON.stringify({
        success: true,
        downloadUrl,
        fileName: `${model.name}.zip`,
        fileSize: model.file_size || 0
      }), {
        headers: { 'Content-Type': 'application/json' }
      });

    } catch (error) {
      console.error('Download processing error:', error);
      session.status = 'failed';
      await this.persistSessions();
      
      return new Response('Download processing failed', { status: 500 });
    }
  }

  private async checkModelAccess(modelId: string, userId: string): Promise<boolean> {
    try {
      // Перевіряємо, чи користувач є власником моделі
      const ownerCheck = await this.env.DB.prepare(
        'SELECT id FROM models WHERE id = ? AND user_id = ?'
      ).bind(modelId, userId).first();

      if (ownerCheck) {
        return true;
      }

      // Перевіряємо, чи модель безкоштовна
      const freeCheck = await this.env.DB.prepare(
        'SELECT id FROM models WHERE id = ? AND is_free = 1'
      ).bind(modelId).first();

      if (freeCheck) {
        return true;
      }

      // Перевіряємо, чи користувач купив модель
      const purchaseCheck = await this.env.DB.prepare(
        'SELECT id FROM user_models WHERE model_id = ? AND user_id = ?'
      ).bind(modelId, userId).first();

      return !!purchaseCheck;
    } catch (error) {
      console.error('Access check error:', error);
      return false;
    }
  }

  private async getModelInfo(modelId: string): Promise<any> {
    try {
      return await this.env.DB.prepare(
        'SELECT id, name, model_url, file_size FROM models WHERE id = ?'
      ).bind(modelId).first();
    } catch (error) {
      console.error('Model info error:', error);
      return null;
    }
  }

  private async generateSignedDownloadUrl(objectKey: string): Promise<string> {
    // Генеруємо підписане URL для R2 з терміном дії 1 година
    const expirationTime = Math.floor(Date.now() / 1000) + 3600; // 1 година
    
    // Тут має бути логіка генерації підписаного URL для R2
    // Поки що повертаємо базове URL
    return `${objectKey}?expires=${expirationTime}`;
  }

  private async incrementDownloadCount(modelId: string): Promise<void> {
    try {
      await this.env.DB.prepare(
        'UPDATE models SET download_count = download_count + 1 WHERE id = ?'
      ).bind(modelId).run();
    } catch (error) {
      console.error('Download count update error:', error);
    }
  }

  private async logDownloadAttempt(downloadRequest: DownloadRequest): Promise<void> {
    try {
      await this.env.DB.prepare(`
        INSERT INTO download_logs (id, model_id, user_id, timestamp, ip_address, user_agent)
        VALUES (?, ?, ?, ?, ?, ?)
      `).bind(
        crypto.randomUUID(),
        downloadRequest.modelId,
        downloadRequest.userId,
        downloadRequest.timestamp,
        downloadRequest.ipAddress || '',
        downloadRequest.userAgent || ''
      ).run();
    } catch (error) {
      console.error('Download log error:', error);
    }
  }

  private async cleanupExpiredSessions(): Promise<Response> {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [sessionId, session] of this.sessions.entries()) {
      if (now > session.expiresAt) {
        this.sessions.delete(sessionId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      await this.persistSessions();
    }

    return new Response(JSON.stringify({
      success: true,
      cleanedSessions: cleanedCount
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  private async persistSessions(): Promise<void> {
    await this.state.storage.put('sessions', Array.from(this.sessions.entries()));
  }
}
