'use client'

import { useState, useEffect } from 'react'
import { SplineScene } from "@/components/ui/splite"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { cn } from '@/lib/utils'

interface Category {
  id: string
  name: string
  count: number
  description: string
  splineObjectName: string
}

interface CategoryExplorerProps {
  splineSceneUrl: string
  categories: Category[]
  onCategorySelect: (category: Category) => void
  className?: string
}

export function CategoryExplorer({ 
  splineSceneUrl, 
  categories, 
  onCategorySelect,
  className 
}: CategoryExplorerProps) {
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [splineApp, setSplineApp] = useState<any>(null);
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);
  
  const handleSceneLoad = (app: any) => {
    setSplineApp(app);
    console.log("Spline scene loaded for category explorer");
  };
  
  const handleObjectClick = (e: any) => {
    if (!e.target) return;
    
    const clickedObjectName = e.target.name;
    const category = categories.find(cat => cat.splineObjectName === clickedObjectName);
    
    if (category) {
      setSelectedCategory(category);
      onCategorySelect(category);
    }
  };
  
  const handleObjectHover = (e: any) => {
    if (!e.target) return;
    
    const hoveredObjectName = e.target.name;
    const category = categories.find(cat => cat.splineObjectName === hoveredObjectName);
    
    if (category) {
      setHoveredCategory(category.id);
    } else {
      setHoveredCategory(null);
    }
  };
  
  // Navigate to category in the 3D scene
  const navigateToCategory = (category: Category) => {
    if (!splineApp) return;
    
    // Focus on the category object in the scene
    try {
      // This assumes your Spline scene has a camera animation named after each category
      splineApp.emitEvent('mouseDown', category.splineObjectName);
      
      // Or you could use the built-in zoomTo method
      // splineApp.zoomTo(category.splineObjectName);
      
      setSelectedCategory(category);
      onCategorySelect(category);
    } catch (error) {
      console.error("Error navigating to category in Spline scene:", error);
    }
  };
  
  return (
    <div className={cn("relative", className)}>
      <div className="h-[600px] rounded-lg overflow-hidden">
        <SplineScene 
          scene={splineSceneUrl}
          preset="INTERACTIVE_SHOWCASE"
          onLoad={handleSceneLoad}
          onMouseDown={handleObjectClick}
          onMouseHover={handleObjectHover}
        />
      </div>
      
      {/* Category info overlay */}
      {selectedCategory && (
        <div className="absolute bottom-6 left-6 max-w-md bg-background/90 backdrop-blur-sm rounded-lg p-4 shadow-lg">
          <h3 className="text-xl font-bold">{selectedCategory.name}</h3>
          <Badge variant="secondary" className="mt-1">
            {selectedCategory.count} models
          </Badge>
          <p className="mt-2 text-sm text-muted-foreground">
            {selectedCategory.description}
          </p>
          <Button className="mt-3" size="sm">
            Explore {selectedCategory.name}
          </Button>
        </div>
      )}
      
      {/* Category navigation buttons */}
      <Card className="absolute top-6 right-6 w-64">
        <CardContent className="p-3">
          <h3 className="font-medium mb-2">Categories</h3>
          <div className="space-y-1">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory?.id === category.id ? "default" : 
                        hoveredCategory === category.id ? "secondary" : "ghost"}
                className="w-full justify-start text-left"
                size="sm"
                onClick={() => navigateToCategory(category)}
              >
                {category.name}
                <span className="ml-auto text-xs text-muted-foreground">
                  {category.count}
                </span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>
      
      {/* Instruction overlay */}
      <div className="absolute top-6 left-6 bg-background/80 backdrop-blur-sm rounded-lg p-3 shadow-md">
        <p className="text-sm">
          Click on a category in the 3D space or use the menu on the right
        </p>
      </div>
    </div>
  );
}
