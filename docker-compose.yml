version: '3.8'

services:
  # 3D Marketplace Application
  marketplace:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=your-secret-key-here
      - DATABASE_URL=********************************************/marketplace
    depends_on:
      - postgres
      - redis
    networks:
      - marketplace-network
    restart: unless-stopped

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=marketplace
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - marketplace-network
    restart: unless-stopped

  # Redis for caching
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - marketplace-network
    restart: unless-stopped

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - marketplace
    networks:
      - marketplace-network
    restart: unless-stopped

  # Scraper service (separate container for heavy scraping)
  scraper:
    build:
      context: .
      dockerfile: Dockerfile.scraper
    environment:
      - NODE_ENV=production
      - DATABASE_URL=********************************************/marketplace
    depends_on:
      - postgres
    networks:
      - marketplace-network
    restart: unless-stopped
    # Run scraper every hour
    command: ["node", "scripts/scraper-cron.js"]

volumes:
  postgres_data:
  redis_data:

networks:
  marketplace-network:
    driver: bridge
