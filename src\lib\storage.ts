/**
 * Утиліти для роботи з Cloudflare R2 Storage
 */

// Типи для R2
export interface R2Bucket {
  head: (key: string) => Promise<R2Object | null>;
  get: (key: string) => Promise<R2ObjectBody | null>;
  put: (key: string, value: ReadableStream | ArrayBuffer | string) => Promise<R2Object>;
  delete: (key: string) => Promise<void>;
  list: (options?: { prefix?: string; limit?: number; cursor?: string }) => Promise<R2Objects>;
}

export interface R2Object {
  key: string;
  version: string;
  size: number;
  etag: string;
  httpEtag: string;
  uploaded: Date;
  httpMetadata?: Record<string, string>;
  customMetadata?: Record<string, string>;
}

export interface R2ObjectBody extends R2Object {
  body: ReadableStream;
  bodyUsed: boolean;
  arrayBuffer: () => Promise<ArrayBuffer>;
  text: () => Promise<string>;
  json: () => Promise<any>;
  blob: () => Promise<Blob>;
}

export interface R2Objects {
  objects: R2Object[];
  truncated: boolean;
  cursor?: string;
  delimitedPrefixes?: string[];
}

// Глобальна змінна для зберігання екземпляра бакета
let bucket: R2Bucket | null = null;

/**
 * Отримує екземпляр бакета R2
 * @returns Екземпляр бакета R2
 */
export function getStorage(): R2Bucket {
  if (!bucket) {
    // В середовищі Cloudflare Pages, R2 доступний через env.STORAGE
    if (typeof process !== 'undefined' && process.env && process.env.STORAGE) {
      bucket = process.env.STORAGE as unknown as R2Bucket;
    } else {
      throw new Error('Сховище недоступне');
    }
  }
  return bucket;
}

/**
 * Завантажує файл у сховище
 * @param key Ключ файлу
 * @param file Файл для завантаження
 * @param metadata Метадані файлу
 * @returns Об'єкт R2
 */
export async function uploadFile(
  key: string,
  file: File | Blob | ArrayBuffer | ReadableStream | string,
  metadata?: Record<string, string>
): Promise<R2Object> {
  const storage = getStorage();

  const options: any = {};

  if (metadata) {
    options.customMetadata = metadata;
  }

  // Конвертуємо File або Blob в ArrayBuffer для сумісності з R2
  let fileData: ArrayBuffer | ReadableStream | string;

  if (file instanceof File || file instanceof Blob) {
    options.httpMetadata = {
      contentType: file.type || 'application/octet-stream',
    };
    fileData = await file.arrayBuffer();
  } else {
    fileData = file;
  }

  return await storage.put(key, fileData);
}

/**
 * Отримує файл зі сховища
 * @param key Ключ файлу
 * @returns Об'єкт R2 або null, якщо файл не знайдено
 */
export async function getFile(key: string): Promise<R2ObjectBody | null> {
  const storage = getStorage();
  return await storage.get(key);
}

/**
 * Видаляє файл зі сховища
 * @param key Ключ файлу
 */
export async function deleteFile(key: string): Promise<void> {
  const storage = getStorage();
  await storage.delete(key);
}

/**
 * Отримує URL для доступу до файлу
 * @param key Ключ файлу
 * @returns URL файлу
 */
export function getFileUrl(key: string): string {
  // Базовий URL для доступу до файлів R2
  const baseUrl = process.env.NEXT_PUBLIC_R2_URL || 'https://storage.example.com';
  return `${baseUrl}/${key}`;
}

/**
 * Генерує унікальний ключ для файлу
 * @param fileName Ім'я файлу
 * @param prefix Префікс для ключа
 * @returns Унікальний ключ
 */
export function generateFileKey(fileName: string, prefix: string = ''): string {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 10);
  const extension = fileName.includes('.') ? fileName.split('.').pop() : '';

  return `${prefix ? prefix + '/' : ''}${timestamp}-${randomString}${extension ? '.' + extension : ''}`;
}
