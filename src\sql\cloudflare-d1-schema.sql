-- Cloudflare D1 Database Schema for 3D Marketplace
-- Enhanced with job queue, error tracking, and observability

-- Job Queue Table
CREATE TABLE IF NOT EXISTS job_queue (
    id TEXT PRIMARY KEY,
    type TEXT NOT NULL,
    data TEXT NOT NULL, -- JSON
    status TEXT NOT NULL DEFAULT 'pending',
    priority INTEGER NOT NULL DEFAULT 2,
    progress TEXT NOT NULL, -- JSON
    result TEXT, -- <PERSON><PERSON><PERSON>
    created_at TEXT NOT NULL,
    started_at TEXT,
    completed_at TEXT,
    retry_count INTEGER NOT NULL DEFAULT 0,
    max_retries INTEGER NOT NULL DEFAULT 3,
    retry_delay INTEGER NOT NULL DEFAULT 5000,
    user_id TEXT,
    metadata TEXT -- JSON
);

-- Error Logs Table
CREATE TABLE IF NOT EXISTS error_logs (
    id TEXT PRIMARY KEY,
    code TEXT NOT NULL,
    message TEXT NOT NULL,
    user_message TEXT NOT NULL,
    category TEXT NOT NULL,
    severity TEXT NOT NULL,
    platform TEXT,
    url TEXT,
    user_id TEXT,
    session_id TEXT,
    user_agent TEXT,
    context TEXT, -- JSON
    recovery_actions TEXT, -- JSON
    retryable BOOLEAN NOT NULL DEFAULT FALSE,
    reportable BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TEXT NOT NULL,
    resolved_at TEXT,
    resolution_notes TEXT
);

-- Scraping Sessions Table
CREATE TABLE IF NOT EXISTS scraping_sessions (
    id TEXT PRIMARY KEY,
    platform TEXT NOT NULL,
    session_type TEXT NOT NULL, -- 'single', 'batch', 'scheduled'
    status TEXT NOT NULL DEFAULT 'started',
    total_urls INTEGER NOT NULL DEFAULT 0,
    processed_urls INTEGER NOT NULL DEFAULT 0,
    successful_imports INTEGER NOT NULL DEFAULT 0,
    failed_imports INTEGER NOT NULL DEFAULT 0,
    started_at TEXT NOT NULL,
    completed_at TEXT,
    user_id TEXT,
    configuration TEXT, -- JSON
    results TEXT -- JSON
);

-- Scraped Models Cache Table
CREATE TABLE IF NOT EXISTS scraped_models_cache (
    id TEXT PRIMARY KEY,
    platform TEXT NOT NULL,
    original_id TEXT NOT NULL,
    original_url TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    thumbnail_url TEXT,
    category TEXT,
    tags TEXT, -- JSON array
    designer_name TEXT,
    designer_avatar TEXT,
    stats TEXT, -- JSON with downloads, likes, views
    is_free BOOLEAN NOT NULL DEFAULT TRUE,
    price REAL DEFAULT 0,
    file_formats TEXT, -- JSON array
    total_size INTEGER DEFAULT 0,
    license_info TEXT, -- JSON
    scraped_at TEXT NOT NULL,
    last_updated TEXT NOT NULL,
    cache_expires_at TEXT NOT NULL,
    is_valid BOOLEAN NOT NULL DEFAULT TRUE
);

-- Platform Rate Limits Table
CREATE TABLE IF NOT EXISTS platform_rate_limits (
    platform TEXT PRIMARY KEY,
    requests_per_minute INTEGER NOT NULL DEFAULT 10,
    requests_per_hour INTEGER NOT NULL DEFAULT 100,
    requests_per_day INTEGER NOT NULL DEFAULT 1000,
    current_minute_count INTEGER NOT NULL DEFAULT 0,
    current_hour_count INTEGER NOT NULL DEFAULT 0,
    current_day_count INTEGER NOT NULL DEFAULT 0,
    last_reset_minute TEXT NOT NULL,
    last_reset_hour TEXT NOT NULL,
    last_reset_day TEXT NOT NULL,
    is_blocked BOOLEAN NOT NULL DEFAULT FALSE,
    blocked_until TEXT,
    last_request_at TEXT
);

-- User Import History Table
CREATE TABLE IF NOT EXISTS user_import_history (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    import_type TEXT NOT NULL, -- 'single', 'batch'
    platform TEXT NOT NULL,
    url TEXT,
    urls_count INTEGER DEFAULT 1,
    status TEXT NOT NULL,
    job_id TEXT,
    started_at TEXT NOT NULL,
    completed_at TEXT,
    success_count INTEGER DEFAULT 0,
    error_count INTEGER DEFAULT 0,
    total_models_imported INTEGER DEFAULT 0,
    notes TEXT
);

-- Analytics Events Table
CREATE TABLE IF NOT EXISTS analytics_events (
    id TEXT PRIMARY KEY,
    event_type TEXT NOT NULL,
    event_name TEXT NOT NULL,
    user_id TEXT,
    session_id TEXT,
    platform TEXT,
    model_id TEXT,
    properties TEXT, -- JSON
    timestamp TEXT NOT NULL,
    ip_address TEXT,
    user_agent TEXT,
    referrer TEXT,
    page_url TEXT
);

-- System Metrics Table
CREATE TABLE IF NOT EXISTS system_metrics (
    id TEXT PRIMARY KEY,
    metric_name TEXT NOT NULL,
    metric_value REAL NOT NULL,
    metric_unit TEXT,
    tags TEXT, -- JSON
    timestamp TEXT NOT NULL,
    source TEXT NOT NULL -- 'scraper', 'api', 'worker', etc.
);

-- Create Indexes for Performance
CREATE INDEX IF NOT EXISTS idx_job_queue_status ON job_queue(status);
CREATE INDEX IF NOT EXISTS idx_job_queue_user_id ON job_queue(user_id);
CREATE INDEX IF NOT EXISTS idx_job_queue_created_at ON job_queue(created_at);
CREATE INDEX IF NOT EXISTS idx_job_queue_priority_status ON job_queue(priority, status);

CREATE INDEX IF NOT EXISTS idx_error_logs_category ON error_logs(category);
CREATE INDEX IF NOT EXISTS idx_error_logs_severity ON error_logs(severity);
CREATE INDEX IF NOT EXISTS idx_error_logs_platform ON error_logs(platform);
CREATE INDEX IF NOT EXISTS idx_error_logs_created_at ON error_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_error_logs_user_id ON error_logs(user_id);

CREATE INDEX IF NOT EXISTS idx_scraping_sessions_platform ON scraping_sessions(platform);
CREATE INDEX IF NOT EXISTS idx_scraping_sessions_status ON scraping_sessions(status);
CREATE INDEX IF NOT EXISTS idx_scraping_sessions_user_id ON scraping_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_scraping_sessions_started_at ON scraping_sessions(started_at);

CREATE INDEX IF NOT EXISTS idx_scraped_models_cache_platform ON scraped_models_cache(platform);
CREATE INDEX IF NOT EXISTS idx_scraped_models_cache_original_id ON scraped_models_cache(platform, original_id);
CREATE INDEX IF NOT EXISTS idx_scraped_models_cache_expires_at ON scraped_models_cache(cache_expires_at);
CREATE INDEX IF NOT EXISTS idx_scraped_models_cache_is_valid ON scraped_models_cache(is_valid);

CREATE INDEX IF NOT EXISTS idx_platform_rate_limits_platform ON platform_rate_limits(platform);

CREATE INDEX IF NOT EXISTS idx_user_import_history_user_id ON user_import_history(user_id);
CREATE INDEX IF NOT EXISTS idx_user_import_history_platform ON user_import_history(platform);
CREATE INDEX IF NOT EXISTS idx_user_import_history_started_at ON user_import_history(started_at);

CREATE INDEX IF NOT EXISTS idx_analytics_events_event_type ON analytics_events(event_type);
CREATE INDEX IF NOT EXISTS idx_analytics_events_user_id ON analytics_events(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_timestamp ON analytics_events(timestamp);
CREATE INDEX IF NOT EXISTS idx_analytics_events_platform ON analytics_events(platform);

CREATE INDEX IF NOT EXISTS idx_system_metrics_metric_name ON system_metrics(metric_name);
CREATE INDEX IF NOT EXISTS idx_system_metrics_timestamp ON system_metrics(timestamp);
CREATE INDEX IF NOT EXISTS idx_system_metrics_source ON system_metrics(source);

-- Insert default platform rate limits
INSERT OR IGNORE INTO platform_rate_limits (
    platform, requests_per_minute, requests_per_hour, requests_per_day,
    current_minute_count, current_hour_count, current_day_count,
    last_reset_minute, last_reset_hour, last_reset_day
) VALUES 
('thingiverse', 8, 100, 1000, 0, 0, 0, datetime('now'), datetime('now'), datetime('now')),
('myminifactory', 10, 150, 1500, 0, 0, 0, datetime('now'), datetime('now'), datetime('now')),
('printables', 12, 200, 2000, 0, 0, 0, datetime('now'), datetime('now'), datetime('now')),
('thangs', 10, 120, 1200, 0, 0, 0, datetime('now'), datetime('now'), datetime('now')),
('makerworld', 15, 180, 1800, 0, 0, 0, datetime('now'), datetime('now'), datetime('now'));

-- Create triggers for automatic cleanup
CREATE TRIGGER IF NOT EXISTS cleanup_old_jobs
AFTER INSERT ON job_queue
BEGIN
    DELETE FROM job_queue 
    WHERE completed_at IS NOT NULL 
    AND datetime(completed_at) < datetime('now', '-7 days');
END;

CREATE TRIGGER IF NOT EXISTS cleanup_old_error_logs
AFTER INSERT ON error_logs
BEGIN
    DELETE FROM error_logs 
    WHERE created_at < datetime('now', '-30 days');
END;

CREATE TRIGGER IF NOT EXISTS cleanup_expired_cache
AFTER INSERT ON scraped_models_cache
BEGIN
    DELETE FROM scraped_models_cache 
    WHERE cache_expires_at < datetime('now');
END;

CREATE TRIGGER IF NOT EXISTS cleanup_old_analytics
AFTER INSERT ON analytics_events
BEGIN
    DELETE FROM analytics_events 
    WHERE timestamp < datetime('now', '-90 days');
END;
