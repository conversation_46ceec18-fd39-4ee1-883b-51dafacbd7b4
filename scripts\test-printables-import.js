/**
 * Простий скрипт для тестування функціональності імпорту з Printables
 */

const { 
  extractPrintablesModelId, 
  fetchPrintablesModel, 
  convertPrintablesModelToInternal,
  importModelFromPrintables 
} = require('../src/lib/api/printables.ts');

async function testPrintablesImport() {
  console.log('🧪 Тестування функціональності імпорту з Printables...\n');

  const testUrl = 'https://www.printables.com/model/1286204-dummy-13-terminator-t800-armor';

  try {
    // Тест 1: Витягування ID
    console.log('1️⃣ Тестування витягування ID з URL...');
    const modelId = extractPrintablesModelId(testUrl);
    console.log(`   ✅ ID моделі: ${modelId}\n`);

    if (!modelId) {
      throw new Error('Не вдалося витягти ID моделі');
    }

    // Тест 2: Отримання даних моделі
    console.log('2️⃣ Тестування отримання даних моделі...');
    const printablesModel = await fetchPrintablesModel(modelId);
    
    if (!printablesModel) {
      throw new Error('Не вдалося отримати дані моделі');
    }

    console.log(`   ✅ Назва моделі: ${printablesModel.name}`);
    console.log(`   ✅ Дизайнер: ${printablesModel.user.name}`);
    console.log(`   ✅ Безкоштовна: ${printablesModel.is_free ? 'Так' : 'Ні'}`);
    console.log(`   ✅ Кількість файлів: ${printablesModel.files.length}`);
    console.log(`   ✅ Теги: ${printablesModel.tags.join(', ')}\n`);

    // Тест 3: Конвертація у внутрішній формат
    console.log('3️⃣ Тестування конвертації у внутрішній формат...');
    const internalModel = convertPrintablesModelToInternal(printablesModel, 'test_model_id');
    
    console.log(`   ✅ Внутрішній ID: ${internalModel.id}`);
    console.log(`   ✅ Джерело: ${internalModel.source}`);
    console.log(`   ✅ Ліцензія: ${internalModel.license?.name}`);
    console.log(`   ✅ Комерційне використання: ${internalModel.license?.allowCommercialUse ? 'Дозволено' : 'Заборонено'}`);
    console.log(`   ✅ Розмір файлів: ${internalModel.fileSize}\n`);

    // Тест 4: Повний імпорт
    console.log('4️⃣ Тестування повного імпорту...');
    const importedModel = await importModelFromPrintables(testUrl);
    
    if (!importedModel) {
      throw new Error('Не вдалося імпортувати модель');
    }

    console.log(`   ✅ Модель успішно імпортована!`);
    console.log(`   ✅ ID: ${importedModel.id}`);
    console.log(`   ✅ Назва: ${importedModel.title}`);
    console.log(`   ✅ Ціна: ${importedModel.price === 0 ? 'Безкоштовно' : `$${importedModel.price}`}`);
    console.log(`   ✅ Зовнішнє джерело: ${importedModel.externalSource?.originalUrl}\n`);

    // Тест 5: Тестування невалідного URL
    console.log('5️⃣ Тестування невалідного URL...');
    const invalidResult = await importModelFromPrintables('https://example.com/invalid');
    console.log(`   ✅ Невалідний URL правильно оброблено: ${invalidResult === null ? 'Так' : 'Ні'}\n`);

    console.log('🎉 Всі тести пройшли успішно!');
    console.log('\n📋 Резюме:');
    console.log('   • Витягування ID з URL: ✅');
    console.log('   • Отримання даних з Printables: ✅');
    console.log('   • Конвертація у внутрішній формат: ✅');
    console.log('   • Повний імпорт моделі: ✅');
    console.log('   • Обробка помилок: ✅');

  } catch (error) {
    console.error('❌ Помилка під час тестування:', error.message);
    process.exit(1);
  }
}

// Запуск тестів
if (require.main === module) {
  testPrintablesImport();
}

module.exports = { testPrintablesImport };
