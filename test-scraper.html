<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест скрапера 3D моделей</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .loading {
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Тест скрапера 3D моделей</h1>
        
        <div>
            <h3>Швидкі тести:</h3>
            <button onclick="testFakeData()">Згенерувати тестові дані (10 моделей)</button>
            <button onclick="testGetModels()">Отримати моделі</button>
            <button onclick="testSearchModels()">Пошук моделей</button>
            <button onclick="clearResults()">Очистити результати</button>
        </div>

        <div>
            <h3>Скрапінг (може зайняти час):</h3>
            <button onclick="testPrintablesScraping()">Скрапити Printables (5 моделей)</button>
            <button onclick="testThangsScraping()">Скрапити Thangs (5 моделей)</button>
            <button onclick="testFullScraping()">Повний скрапінг (10 моделей)</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        const resultsDiv = document.getElementById('results');
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(div);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            resultsDiv.innerHTML = '';
        }

        async function makeRequest(url, options = {}) {
            try {
                addResult(`Запит до: ${url}`, 'loading');
                const response = await fetch(url, options);
                const data = await response.json();
                
                if (response.ok) {
                    addResult(`✅ Успіх: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    addResult(`❌ Помилка: ${JSON.stringify(data, null, 2)}`, 'error');
                }
                
                return data;
            } catch (error) {
                addResult(`❌ Помилка запиту: ${error.message}`, 'error');
                throw error;
            }
        }

        async function testFakeData() {
            await makeRequest('/api/scrape', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'fake-data',
                    count: 10
                })
            });
        }

        async function testGetModels() {
            await makeRequest('/api/scraped-models?limit=5');
        }

        async function testSearchModels() {
            await makeRequest('/api/scraped-models?search=модель&limit=3');
        }

        async function testPrintablesScraping() {
            addResult('⚠️ Увага: Скрапінг може зайняти 1-2 хвилини...', 'loading');
            await makeRequest('/api/scrape', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'scrape-printables',
                    count: 5
                })
            });
        }

        async function testThangsScraping() {
            addResult('⚠️ Увага: Скрапінг може зайняти 1-2 хвилини...', 'loading');
            await makeRequest('/api/scrape', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'scrape-thangs',
                    count: 5
                })
            });
        }

        async function testFullScraping() {
            addResult('⚠️ Увага: Повний скрапінг може зайняти 3-5 хвилин...', 'loading');
            await makeRequest('/api/scrape', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'populate',
                    count: 10
                })
            });
        }

        // Автоматичний тест при завантаженні сторінки
        window.onload = function() {
            addResult('🚀 Сторінка завантажена. Готовий до тестування!', 'success');
            addResult('💡 Рекомендую почати з "Згенерувати тестові дані"', 'info');
        };
    </script>
</body>
</html>
