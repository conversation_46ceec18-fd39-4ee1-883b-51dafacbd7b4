'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import Image from 'next/image';
import { ChevronLeft, ChevronRight, Star, TrendingUp, Heart, Download } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import AddToCartButton from './add-to-cart-button';

interface RecommendedModel {
  id: string;
  name: string;
  description: string;
  thumbnail_url: string;
  price: number;
  is_free: boolean;
  category: string;
  author_name: string;
  author_avatar: string;
  download_count: number;
  like_count: number;
  view_count: number;
  rating: number;
  user_id: string;
}

interface RecommendedModelsProps {
  title?: string;
  subtitle?: string;
  category?: string;
  limit?: number;
  excludeModelId?: string;
  basedOnUserHistory?: boolean;
}

export default function RecommendedModels({
  title = "Рекомендовані моделі",
  subtitle = "Моделі, які можуть вас зацікавити",
  category,
  limit = 8,
  excludeModelId,
  basedOnUserHistory = false
}: RecommendedModelsProps) {
  const { data: session } = useSession();
  const [models, setModels] = useState<RecommendedModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    const fetchRecommendedModels = async () => {
      try {
        const params = new URLSearchParams();
        if (category) params.append('category', category);
        if (excludeModelId) params.append('exclude', excludeModelId);
        if (basedOnUserHistory && session?.user?.id) {
          params.append('userId', session.user.id);
          params.append('personalized', 'true');
        }
        params.append('limit', limit.toString());

        const response = await fetch(`/api/models/recommended?${params.toString()}`);
        if (!response.ok) {
          throw new Error('Failed to fetch recommended models');
        }

        const data = await response.json();
        setModels(data.data || []);
      } catch (error) {
        console.error('Error fetching recommended models:', error);
        setError('Failed to load recommendations');
      } finally {
        setLoading(false);
      }
    };

    fetchRecommendedModels();
  }, [category, excludeModelId, basedOnUserHistory, session?.user?.id, limit]);

  const itemsPerPage = 4;
  const maxIndex = Math.max(0, models.length - itemsPerPage);

  const nextSlide = () => {
    setCurrentIndex(prev => Math.min(prev + 1, maxIndex));
  };

  const prevSlide = () => {
    setCurrentIndex(prev => Math.max(prev - 1, 0));
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div>
            <div className="h-6 bg-gray-200 rounded w-48 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-64"></div>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <div className="w-full h-48 bg-gray-200 rounded-t-lg"></div>
              <CardContent className="p-4">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error || models.length === 0) {
    return null; // Не показуємо секцію, якщо немає рекомендацій
  }

  return (
    <div className="space-y-6">
      {/* Заголовок */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">{title}</h2>
          <p className="text-gray-600">{subtitle}</p>
        </div>
        
        {models.length > itemsPerPage && (
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={prevSlide}
              disabled={currentIndex === 0}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={nextSlide}
              disabled={currentIndex >= maxIndex}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

      {/* Карусель моделей */}
      <div className="relative overflow-hidden">
        <div 
          className="flex transition-transform duration-300 ease-in-out"
          style={{ transform: `translateX(-${currentIndex * (100 / itemsPerPage)}%)` }}
        >
          {models.map((model) => (
            <div key={model.id} className="w-full md:w-1/2 lg:w-1/4 flex-shrink-0 px-3">
              <Card className="h-full hover:shadow-lg transition-shadow">
                <Link href={`/models/${model.id}`}>
                  <div className="relative">
                    <Image
                      src={model.thumbnail_url || '/placeholder-model.jpg'}
                      alt={model.name}
                      width={300}
                      height={200}
                      className="w-full h-48 object-cover rounded-t-lg"
                    />
                    
                    {/* Бейджі */}
                    <div className="absolute top-2 left-2 flex flex-col space-y-1">
                      {model.is_free ? (
                        <Badge className="bg-green-500">Безкоштовно</Badge>
                      ) : (
                        <Badge className="bg-blue-500">${model.price}</Badge>
                      )}
                      
                      {model.rating >= 4.5 && (
                        <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                          <Star className="h-3 w-3 mr-1 fill-current" />
                          {model.rating.toFixed(1)}
                        </Badge>
                      )}
                    </div>

                    {/* Популярність */}
                    {model.download_count > 100 && (
                      <div className="absolute top-2 right-2">
                        <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                          <TrendingUp className="h-3 w-3 mr-1" />
                          Популярна
                        </Badge>
                      </div>
                    )}
                  </div>
                </Link>

                <CardHeader className="pb-2">
                  <CardTitle className="text-lg line-clamp-1">
                    <Link href={`/models/${model.id}`} className="hover:text-blue-600">
                      {model.name}
                    </Link>
                  </CardTitle>
                  <CardDescription className="line-clamp-2">
                    {model.description}
                  </CardDescription>
                </CardHeader>

                <CardContent className="pb-2">
                  <div className="flex items-center space-x-2 mb-3">
                    <Image
                      src={model.author_avatar || '/default-avatar.png'}
                      alt={model.author_name}
                      width={24}
                      height={24}
                      className="w-6 h-6 rounded-full"
                    />
                    <span className="text-sm text-gray-600">{model.author_name}</span>
                  </div>

                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <div className="flex items-center space-x-3">
                      <span className="flex items-center">
                        <Download className="mr-1 h-3 w-3" />
                        {model.download_count}
                      </span>
                      <span className="flex items-center">
                        <Heart className="mr-1 h-3 w-3" />
                        {model.like_count}
                      </span>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {model.category}
                    </Badge>
                  </div>
                </CardContent>

                <CardFooter className="pt-2">
                  <AddToCartButton
                    model={{
                      id: model.id,
                      name: model.name,
                      price: model.price,
                      thumbnail_url: model.thumbnail_url,
                      is_free: model.is_free,
                      user_id: model.user_id,
                    }}
                    size="sm"
                    className="w-full"
                  />
                </CardFooter>
              </Card>
            </div>
          ))}
        </div>
      </div>

      {/* Індикатори */}
      {models.length > itemsPerPage && (
        <div className="flex justify-center space-x-2">
          {Array.from({ length: Math.ceil(models.length / itemsPerPage) }).map((_, index) => (
            <button
              key={index}
              className={`w-2 h-2 rounded-full transition-colors ${
                Math.floor(currentIndex / itemsPerPage) === index
                  ? 'bg-blue-600'
                  : 'bg-gray-300'
              }`}
              onClick={() => setCurrentIndex(index * itemsPerPage)}
            />
          ))}
        </div>
      )}

      {/* Посилання на всі моделі категорії */}
      {category && (
        <div className="text-center">
          <Link href={`/marketplace?category=${encodeURIComponent(category)}`}>
            <Button variant="outline">
              Переглянути всі моделі в категорії "{category}"
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
}
