/**
 * NextAuth конфігурація з Cloudflare інтеграцією
 * Підтримка Google OAuth та інших провайдерів
 */

import { NextAuthOptions } from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import GitHubProvider from 'next-auth/providers/github';
import { CloudflareAdapter } from './cloudflare-adapter';
import { trackUserEvent } from '@/lib/cloudflare/analytics';

/**
 * Отримує Cloudflare environment для NextAuth
 */
function getCloudflareEnv() {
  // В Cloudflare Pages environment змінні доступні через process.env
  return {
    GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID!,
    GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET!,
    GITHUB_CLIENT_ID: process.env.GITHUB_CLIENT_ID!,
    GITHUB_CLIENT_SECRET: process.env.GITHUB_CLIENT_SECRET!,
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET!,
    NEXTAUTH_URL: process.env.NEXTAUTH_URL!,
  };
}

export const authOptions: NextAuthOptions = {
  // Використовуємо Cloudflare адаптер для зберігання сесій в D1/KV
  adapter: CloudflareAdapter(),
  
  providers: [
    GoogleProvider({
      clientId: getCloudflareEnv().GOOGLE_CLIENT_ID,
      clientSecret: getCloudflareEnv().GOOGLE_CLIENT_SECRET,
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code",
          scope: "openid email profile"
        }
      }
    }),
    GitHubProvider({
      clientId: getCloudflareEnv().GITHUB_CLIENT_ID,
      clientSecret: getCloudflareEnv().GITHUB_CLIENT_SECRET,
    })
  ],

  pages: {
    signIn: '/auth/signin',
    signOut: '/auth/signout',
    error: '/auth/error',
    verifyRequest: '/auth/verify-request',
    newUser: '/auth/new-user'
  },

  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 днів
  },

  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 днів
  },

  callbacks: {
    async signIn({ user, account, profile, email, credentials }) {
      try {
        // Трекінг входу користувача в Cloudflare Analytics
        await trackUserEvent('user_signin', user.id, {
          provider: account?.provider,
          email: user.email,
          name: user.name,
          timestamp: new Date().toISOString()
        });

        return true;
      } catch (error) {
        console.error('Error tracking sign in:', error);
        return true; // Не блокуємо вхід через помилку трекінгу
      }
    },

    async jwt({ token, user, account, profile }) {
      // Додаємо додаткову інформацію до JWT токену
      if (user) {
        token.userId = user.id;
        token.provider = account?.provider;
      }
      return token;
    },

    async session({ session, token }) {
      // Додаємо інформацію з JWT до сесії
      if (token) {
        session.user.id = token.userId as string;
        session.user.provider = token.provider as string;
      }

      try {
        // Трекінг активної сесії
        await trackUserEvent('session_active', session.user.id, {
          email: session.user.email,
          provider: session.user.provider,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error('Error tracking session:', error);
      }

      return session;
    },

    async redirect({ url, baseUrl }) {
      // Перенаправлення після аутентифікації
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      else if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    }
  },

  events: {
    async signOut({ token, session }) {
      try {
        // Трекінг виходу користувача
        const userId = token?.userId || session?.user?.id;
        if (userId) {
          await trackUserEvent('user_signout', userId as string, {
            timestamp: new Date().toISOString()
          });
        }
      } catch (error) {
        console.error('Error tracking sign out:', error);
      }
    },

    async createUser({ user }) {
      try {
        // Трекінг нового користувача
        await trackUserEvent('user_created', user.id, {
          email: user.email,
          name: user.name,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error('Error tracking user creation:', error);
      }
    }
  },

  debug: process.env.NODE_ENV === 'development',
  
  secret: getCloudflareEnv().NEXTAUTH_SECRET,
};

/**
 * Типи для розширеної сесії
 */
declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      provider?: string;
    }
  }

  interface User {
    id: string;
    provider?: string;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    userId?: string;
    provider?: string;
  }
}
