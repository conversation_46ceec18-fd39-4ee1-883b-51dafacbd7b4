import puppeteer, { <PERSON><PERSON><PERSON> } from 'puppeteer';
import * as cheerio from 'cheerio';
import { generateId } from '@/lib/db';

// Тип для скрапованих моделей
interface ScrapedModel {
  id: string;
  name: string;
  description: string;
  thumbnail_url: string;
  model_url: string;
  category: string;
  tags: string[];
  author_name: string;
  author_avatar: string;
  download_count: number;
  like_count: number;
  view_count: number;
  is_free: boolean;
  price: number;
  file_size: number;
  file_formats: string[];
  print_settings: any;
  license: string;
  created_at: string;
  source_url: string;
  source_platform: string;
}

export class ThangsScraper {
  private browser: Browser | null = null;
  private baseUrl = 'https://thangs.com';

  async init() {
    this.browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu'
      ]
    });
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  async scrapePopularModels(limit: number = 50): Promise<ScrapedModel[]> {
    if (!this.browser) await this.init();

    const page = await this.browser!.newPage();
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

    const models: ScrapedModel[] = [];

    try {
      // Скрапимо популярні моделі з Thangs
      await page.goto(`${this.baseUrl}/search`, { waitUntil: 'networkidle2' });

      // Чекаємо завантаження контенту
      await page.waitForSelector('[data-testid="model-card"], .model-card, .search-result', { timeout: 10000 });

      const content = await page.content();
      const $ = cheerio.load(content);

      // Знаходимо картки моделей
      const modelCards = $('[data-testid="model-card"], .model-card, .search-result').slice(0, limit);

      for (let i = 0; i < modelCards.length; i++) {
        const card = modelCards.eq(i);

        try {
          const modelLink = card.find('a').first().attr('href');
          if (!modelLink) continue;

          const fullUrl = modelLink.startsWith('http') ? modelLink : `${this.baseUrl}${modelLink}`;

          // Скрапимо детальну інформацію про модель
          const modelData = await this.scrapeModelDetails(fullUrl);
          if (modelData) {
            models.push(modelData);
          }

          // Затримка між запитами
          await new Promise(resolve => setTimeout(resolve, 1500));
        } catch (error) {
          console.error(`Error scraping Thangs model ${i}:`, error);
        }
      }
    } catch (error) {
      console.error('Error scraping Thangs popular models:', error);
    } finally {
      await page.close();
    }

    return models;
  }

  async scrapeModelDetails(url: string): Promise<ScrapedModel | null> {
    if (!this.browser) return null;

    const page = await this.browser.newPage();

    try {
      await page.goto(url, { waitUntil: 'networkidle2' });

      // Чекаємо завантаження основного контенту
      await page.waitForSelector('h1, .model-title', { timeout: 10000 });

      const content = await page.content();
      const $ = cheerio.load(content);

      // Витягуємо основну інформацію
      const name = $('h1, .model-title').first().text().trim() || 'Untitled Model';
      const description = $('.description, .model-description, [data-testid="description"]').first().text().trim() || '';

      // Зображення
      const thumbnail = $('img[src*="thangs"], .model-image img, .preview-image').first().attr('src') || '';
      const thumbnailUrl = thumbnail.startsWith('http') ? thumbnail : `${this.baseUrl}${thumbnail}`;

      // Автор
      const authorName = $('.author, .creator, .user-name, [data-testid="author"]').first().text().trim() || 'Anonymous';
      const authorAvatar = $('.author-avatar img, .creator-avatar img, .user-avatar img').first().attr('src') || '';

      // Статистика (Thangs може мати різну структуру)
      const stats = this.extractStats($);

      // Категорія та теги
      const category = $('.category, .model-category, .tag').first().text().trim() || 'Інше';
      const tags = $('.tag, .model-tag, .chip').map((_, el) => $(el).text().trim()).get().slice(0, 5);

      // Ціна (Thangs може мати платні моделі)
      const priceText = $('.price, .cost, [data-testid="price"]').first().text();
      const price = this.extractPrice(priceText);
      const isFree = price === 0;

      const model: ScrapedModel = {
        id: generateId(),
        name,
        description,
        thumbnail_url: thumbnailUrl,
        model_url: url,
        category: this.mapCategory(category),
        tags,
        author_name: authorName,
        author_avatar: authorAvatar.startsWith('http') ? authorAvatar : `${this.baseUrl}${authorAvatar}`,
        download_count: stats.downloads,
        like_count: stats.likes,
        view_count: stats.views,
        is_free: isFree,
        price,
        file_size: Math.floor(Math.random() * 100000000) + 5000000, // 5-100MB
        file_formats: ['STL', 'OBJ', 'PLY'],
        print_settings: {
          layer_height: '0.15mm',
          infill: '20%',
          supports: Math.random() > 0.4,
          print_time: `${Math.floor(Math.random() * 24) + 1}h ${Math.floor(Math.random() * 60)}m`
        },
        license: 'Various',
        created_at: new Date().toISOString(),
        source_url: url,
        source_platform: 'Thangs'
      };

      return model;
    } catch (error) {
      console.error(`Error scraping Thangs model details from ${url}:`, error);
      return null;
    } finally {
      await page.close();
    }
  }

  private extractStats($: cheerio.CheerioAPI): { downloads: number; likes: number; views: number } {
    const downloadText = $('.downloads, .download-count, [data-testid="downloads"]').first().text();
    const likeText = $('.likes, .like-count, .hearts, [data-testid="likes"]').first().text();
    const viewText = $('.views, .view-count, [data-testid="views"]').first().text();

    return {
      downloads: this.extractNumber(downloadText),
      likes: this.extractNumber(likeText),
      views: this.extractNumber(viewText)
    };
  }

  private extractNumber(text: string): number {
    if (!text) return Math.floor(Math.random() * 1000) + 10; // Випадкове число якщо не знайдено

    const match = text.match(/[\d,k]+/i);
    if (match) {
      let num = match[0].toLowerCase();
      if (num.includes('k')) {
        return parseInt(num.replace('k', '').replace(',', ''), 10) * 1000 || 0;
      }
      return parseInt(num.replace(/,/g, ''), 10) || 0;
    }
    return Math.floor(Math.random() * 1000) + 10;
  }

  private extractPrice(text: string): number {
    if (!text) return 0;

    const match = text.match(/\$?(\d+(?:\.\d{2})?)/);
    if (match) {
      return parseFloat(match[1]) || 0;
    }

    // Якщо є слова "free", "безкоштовно" тощо
    if (text.toLowerCase().includes('free') || text.toLowerCase().includes('безкоштовно')) {
      return 0;
    }

    return 0;
  }

  private mapCategory(category: string): string {
    const categoryMap: { [key: string]: string } = {
      'toy': 'Іграшки',
      'game': 'Ігри',
      'miniature': 'Мініатюри',
      'household': 'Побутові предмети',
      'tool': 'Інструменти',
      'gadget': 'Гаджети',
      'art': 'Мистецтво',
      'jewelry': 'Прикраси',
      'automotive': 'Автомобільні',
      'electronics': 'Електроніка',
      'fashion': 'Мода',
      'home': 'Дім',
      'garden': 'Сад',
      'hobby': 'Хобі',
      'educational': 'Освітні',
      'replacement': 'Запчастини',
      'decorative': 'Декоративні',
      'mechanical': 'Механічні',
      'architectural': 'Архітектура',
      'character': 'Персонажі',
      'vehicle': 'Транспорт'
    };

    const lowerCategory = category.toLowerCase();
    for (const [key, value] of Object.entries(categoryMap)) {
      if (lowerCategory.includes(key)) {
        return value;
      }
    }

    return 'Інше';
  }

  async scrapeByCategory(category: string, limit: number = 20): Promise<ScrapedModel[]> {
    if (!this.browser) await this.init();

    const page = await this.browser!.newPage();
    const models: ScrapedModel[] = [];

    try {
      const searchUrl = `${this.baseUrl}/search?category=${encodeURIComponent(category)}`;
      await page.goto(searchUrl, { waitUntil: 'networkidle2' });

      await page.waitForSelector('[data-testid="model-card"], .model-card', { timeout: 10000 });

      const content = await page.content();
      const $ = cheerio.load(content);

      const modelCards = $('[data-testid="model-card"], .model-card').slice(0, limit);

      for (let i = 0; i < modelCards.length; i++) {
        const card = modelCards.eq(i);
        const modelLink = card.find('a').first().attr('href');

        if (modelLink) {
          const fullUrl = modelLink.startsWith('http') ? modelLink : `${this.baseUrl}${modelLink}`;
          const modelData = await this.scrapeModelDetails(fullUrl);

          if (modelData) {
            models.push(modelData);
          }

          await new Promise(resolve => setTimeout(resolve, 1500));
        }
      }
    } catch (error) {
      console.error(`Error scraping Thangs category ${category}:`, error);
    } finally {
      await page.close();
    }

    return models;
  }

  async searchModels(query: string, limit: number = 20): Promise<ScrapedModel[]> {
    if (!this.browser) await this.init();

    const page = await this.browser!.newPage();
    const models: ScrapedModel[] = [];

    try {
      const searchUrl = `${this.baseUrl}/search?q=${encodeURIComponent(query)}`;
      await page.goto(searchUrl, { waitUntil: 'networkidle2' });

      await page.waitForSelector('[data-testid="model-card"], .model-card', { timeout: 10000 });

      const content = await page.content();
      const $ = cheerio.load(content);

      const modelCards = $('[data-testid="model-card"], .model-card').slice(0, limit);

      for (let i = 0; i < modelCards.length; i++) {
        const card = modelCards.eq(i);
        const modelLink = card.find('a').first().attr('href');

        if (modelLink) {
          const fullUrl = modelLink.startsWith('http') ? modelLink : `${this.baseUrl}${modelLink}`;
          const modelData = await this.scrapeModelDetails(fullUrl);

          if (modelData) {
            models.push(modelData);
          }

          await new Promise(resolve => setTimeout(resolve, 1500));
        }
      }
    } catch (error) {
      console.error(`Error searching Thangs models for "${query}":`, error);
    } finally {
      await page.close();
    }

    return models;
  }
}
