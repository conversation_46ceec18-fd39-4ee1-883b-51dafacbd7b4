'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select';
import {
    Sheet,
    SheetContent,
    SheetDescription,
    SheetHeader,
    SheetTitle,
    SheetTrigger,
} from "@/components/ui/sheet";
import {
    Download,
    Filter,
    Heart,
    Search,
    SlidersHorizontal,
    X
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';

// Тимчасові дані для демонстрації
const MOCK_MODELS = [
  {
    id: '1',
    title: 'Low Poly Skull',
    thumbnail: 'https://images.unsplash.com/photo-1611505908502-5b67e53e3a76?q=80&w=1887&auto=format&fit=crop',
    designer: 'DesignMaster',
    price: 4.99,
    category: 'Art',
    likes: 245,
    downloads: 1200,
  },
  {
    id: '2',
    title: 'Smartphone Stand',
    thumbnail: 'https://images.unsplash.com/photo-1517420704952-d9f39e95b43e?q=80&w=1854&auto=format&fit=crop',
    designer: 'TechPrints',
    price: 0,
    category: 'Gadgets',
    likes: 189,
    downloads: 3400,
  },
  {
    id: '3',
    title: 'Desk Organizer',
    thumbnail: 'https://images.unsplash.com/photo-1517420879524-86d64ac2f339?q=80&w=1887&auto=format&fit=crop',
    designer: 'HomeDesigns',
    price: 2.99,
    category: 'Home',
    likes: 132,
    downloads: 870,
  },
  {
    id: '4',
    title: 'Dragon Figurine',
    thumbnail: 'https://images.unsplash.com/photo-1581235720704-06d3acfcb36f?q=80&w=1935&auto=format&fit=crop',
    designer: 'FantasyCreations',
    price: 9.99,
    category: 'Figurines',
    likes: 521,
    downloads: 1500,
  },
  {
    id: '5',
    title: 'Planter Pot',
    thumbnail: 'https://images.unsplash.com/photo-1593062096033-9a26b09da705?q=80&w=2070&auto=format&fit=crop',
    designer: 'GreenThumb',
    price: 3.99,
    category: 'Home',
    likes: 98,
    downloads: 650,
  },
  {
    id: '6',
    title: 'Mechanical Keyboard Key',
    thumbnail: 'https://images.unsplash.com/photo-1535350356005-fd52b3b524fb?q=80&w=2070&auto=format&fit=crop',
    designer: 'KeyboardMods',
    price: 1.99,
    category: 'Gadgets',
    likes: 76,
    downloads: 420,
  },
  {
    id: '7',
    title: 'Articulated Dragon',
    thumbnail: 'https://images.unsplash.com/photo-1581235720704-06d3acfcb36f?q=80&w=1935&auto=format&fit=crop',
    designer: 'DragonMaster3D',
    price: 0,
    category: 'Toys',
    likes: 3254,
    downloads: 12543,
  },
  {
    id: '8',
    title: 'Vase with Pattern',
    thumbnail: 'https://images.unsplash.com/photo-1513384312027-9fa69a360337?q=80&w=2080&auto=format&fit=crop',
    designer: 'HomeDecor',
    price: 5.99,
    category: 'Home',
    likes: 421,
    downloads: 2100,
  },
];

// Категорії для фільтрації
const CATEGORIES = [
  'All',
  'Art',
  'Gadgets',
  'Home',
  'Figurines',
  'Toys',
  'Tools',
  'Fashion',
  'Educational',
];

export default function ModelsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [sortBy, setSortBy] = useState('popular');
  const [priceFilter, setPriceFilter] = useState('all');
  const [filtersVisible, setFiltersVisible] = useState(false);

  // Фільтрація моделей
  const filteredModels = MOCK_MODELS.filter(model => {
    const searchMatch = model.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                        model.designer.toLowerCase().includes(searchQuery.toLowerCase());
    const categoryMatch = selectedCategory === 'All' || model.category === selectedCategory;
    const priceMatch =
      priceFilter === 'all' ||
      (priceFilter === 'free' && model.price === 0) ||
      (priceFilter === 'paid' && model.price > 0);

    return searchMatch && categoryMatch && priceMatch;
  });

  // Сортування моделей
  const sortedModels = [...filteredModels].sort((a, b) => {
    if (sortBy === 'popular') return b.likes - a.likes;
    if (sortBy === 'downloads') return b.downloads - a.downloads;
    if (sortBy === 'newest') return 0; // В реальному додатку тут буде сортування за датою
    if (sortBy === 'price-low') return a.price - b.price;
    if (sortBy === 'price-high') return b.price - a.price;
    return 0;
  });

  // Очищення всіх фільтрів
  const clearFilters = () => {
    setSearchQuery('');
    setSelectedCategory('All');
    setPriceFilter('all');
    setSortBy('popular');
  };

  return (
    <main className="container mx-auto py-8 px-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">3D Моделі</h1>
          <p className="text-muted-foreground">Знайдіть ідеальну 3D модель для вашого проекту</p>
        </div>
        <div className="mt-4 md:mt-0">
          <Button asChild>
            <Link href="/models/upload">Завантажити модель</Link>
          </Button>
        </div>
      </div>

      {/* Пошук та фільтри */}
      <div className="mb-8">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              type="text"
              placeholder="Пошук моделей..."
              className="pl-10"
              value={searchQuery}
              onChange={(e) => {
                // Використовуємо безпечне приведення типу
                const value = (e.target as any).value;
                if (typeof value === 'string') {
                  setSearchQuery(value);
                }
              }}
            />
          </div>

          <div className="hidden md:flex gap-4">
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Сортувати за" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="popular">Популярні</SelectItem>
                <SelectItem value="downloads">Завантаження</SelectItem>
                <SelectItem value="newest">Нові</SelectItem>
                <SelectItem value="price-low">Ціна: від низької</SelectItem>
                <SelectItem value="price-high">Ціна: від високої</SelectItem>
              </SelectContent>
            </Select>

            <Select value={priceFilter} onValueChange={setPriceFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Ціна" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Всі ціни</SelectItem>
                <SelectItem value="free">Безкоштовно</SelectItem>
                <SelectItem value="paid">Платні</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              className="flex items-center gap-2"
              onClick={() => setFiltersVisible(!filtersVisible)}
            >
              <Filter className="h-4 w-4" />
              Фільтри
            </Button>
          </div>

          <div className="md:hidden">
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="outline" className="w-full flex items-center justify-center gap-2">
                  <SlidersHorizontal className="h-4 w-4" />
                  Фільтри та сортування
                </Button>
              </SheetTrigger>
              <SheetContent>
                <SheetHeader>
                  <SheetTitle>Фільтри</SheetTitle>
                  <SheetDescription>
                    Налаштуйте параметри пошуку
                  </SheetDescription>
                </SheetHeader>
                <div className="py-4 space-y-4">
                  <div>
                    <h3 className="text-sm font-medium mb-2">Сортувати за</h3>
                    <Select value={sortBy} onValueChange={setSortBy}>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Сортувати за" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="popular">Популярні</SelectItem>
                        <SelectItem value="downloads">Завантаження</SelectItem>
                        <SelectItem value="newest">Нові</SelectItem>
                        <SelectItem value="price-low">Ціна: від низької</SelectItem>
                        <SelectItem value="price-high">Ціна: від високої</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium mb-2">Ціна</h3>
                    <Select value={priceFilter} onValueChange={setPriceFilter}>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Ціна" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Всі ціни</SelectItem>
                        <SelectItem value="free">Безкоштовно</SelectItem>
                        <SelectItem value="paid">Платні</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium mb-2">Категорія</h3>
                    <div className="flex flex-wrap gap-2">
                      {CATEGORIES.map((category) => (
                        <Badge
                          key={category}
                          variant={selectedCategory === category ? "default" : "outline"}
                          className="cursor-pointer"
                          onClick={() => setSelectedCategory(category)}
                        >
                          {category}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <Button
                    variant="outline"
                    className="w-full mt-4"
                    onClick={clearFilters}
                  >
                    Очистити фільтри
                  </Button>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>

        {/* Категорії (десктоп) */}
        <div className="hidden md:block mt-4">
          <div className="flex flex-wrap gap-2">
            {CATEGORIES.map((category) => (
              <Badge
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                className="cursor-pointer"
                onClick={() => setSelectedCategory(category)}
              >
                {category}
              </Badge>
            ))}
          </div>
        </div>

        {/* Активні фільтри */}
        {(selectedCategory !== 'All' || priceFilter !== 'all' || searchQuery) && (
          <div className="mt-4 flex flex-wrap items-center gap-2">
            <span className="text-sm text-muted-foreground">Активні фільтри:</span>

            {selectedCategory !== 'All' && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Категорія: {selectedCategory}
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => setSelectedCategory('All')}
                />
              </Badge>
            )}

            {priceFilter !== 'all' && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Ціна: {priceFilter === 'free' ? 'Безкоштовно' : 'Платні'}
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => setPriceFilter('all')}
                />
              </Badge>
            )}

            {searchQuery && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Пошук: {searchQuery}
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => setSearchQuery('')}
                />
              </Badge>
            )}

            <Button
              variant="ghost"
              size="sm"
              className="text-sm h-7"
              onClick={clearFilters}
            >
              Очистити всі
            </Button>
          </div>
        )}
      </div>

      {/* Результати пошуку */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {sortedModels.length > 0 ? (
          sortedModels.map((model) => (
            <Link key={model.id} href={`/models/${model.id}`}>
              <Card className="overflow-hidden h-full transition-all duration-200 hover:shadow-md">
                <div className="relative aspect-square">
                  <Image
                    src={model.thumbnail}
                    alt={model.title}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute top-2 right-2 bg-background/80 backdrop-blur-sm rounded-full px-2 py-1 text-xs font-medium">
                    {model.price === 0 ? 'Безкоштовно' : `$${model.price.toFixed(2)}`}
                  </div>
                </div>
                <CardContent className="p-4">
                  <h3 className="font-medium text-lg mb-1 line-clamp-1">{model.title}</h3>
                  <p className="text-sm text-muted-foreground mb-3">by {model.designer}</p>
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <div className="flex items-center">
                      <Heart className="h-4 w-4 mr-1 text-red-500" />
                      {model.likes}
                    </div>
                    <div className="flex items-center">
                      <Download className="h-4 w-4 mr-1" />
                      {model.downloads}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))
        ) : (
          <div className="col-span-full text-center py-12">
            <div className="text-muted-foreground mb-4">Немає результатів для вашого пошуку</div>
            <Button variant="outline" onClick={clearFilters}>Очистити фільтри</Button>
          </div>
        )}
      </div>
    </main>
  );
}
