# 3D Marketplace Integration Documentation

## Table of Contents

1. [Introduction](#introduction)
2. [3D Technologies](#3d-technologies)
   - [Spline](#spline)
   - [Three.js](#threejs)
   - [React Three Fiber](#react-three-fiber)
3. [External Platform Integration](#external-platform-integration)
   - [Web Scraping System](#web-scraping-system)
   - [Supported Platforms](#supported-platforms)
   - [Data Synchronization](#data-synchronization)
4. [3D Integration Components](#3d-integration-components)
   - [SplineScene](#splinescene)
   - [ModelViewer](#modelviewer)
   - [CategoryExplorer](#categoryexplorer)
5. [3D File Formats](#3d-file-formats)
   - [Supported Formats](#supported-formats)
   - [Optimization Guidelines](#optimization-guidelines)
6. [3D Model Interaction](#3d-model-interaction)
   - [Rotation](#rotation)
   - [Scaling](#scaling)
   - [Panning](#panning)
   - [Lighting Changes](#lighting-changes)
7. [3D Scene Configuration](#3d-scene-configuration)
   - [Lighting](#lighting)
   - [Background](#background)
   - [Camera](#camera)
8. [Performance](#performance)
   - [Model Optimization](#model-optimization)
   - [Lazy Loading](#lazy-loading)
   - [Level of Detail (LOD)](#level-of-detail-lod)
9. [Usage Examples](#usage-examples)
   - [Model Viewing](#model-viewing)
   - [3D Categories](#3d-categories)
   - [Print Simulation](#print-simulation)
10. [Developer Guidelines](#developer-guidelines)

## Introduction

This documentation describes the integration of 3D technologies and external platform scraping in the 3D marketplace. It is intended for developers working on the project and provides understanding of the structure and usage of 3D components and external data integration.

## External Platform Integration

### Web Scraping System

The marketplace integrates with multiple external 3D model platforms through a comprehensive web scraping system. This allows users to import models from various sources while maintaining proper attribution and licensing information.

#### Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend UI   │───▶│  API Endpoints   │───▶│  Scraping Core  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   Data Storage   │    │  External APIs  │
                       └──────────────────┘    └─────────────────┘
```

#### Core Components

1. **Scraping Utilities** (`src/lib/api/`)
   - Platform-specific scrapers for each supported site
   - Common interfaces for data normalization
   - Error handling and retry mechanisms

2. **API Endpoints** (`src/app/api/scraping/`)
   - RESTful endpoints for each platform
   - Rate limiting and caching
   - Authentication and validation

3. **Data Models** (`src/types/models.ts`)
   - Extended model types for external sources
   - License and attribution tracking
   - Source metadata preservation

### Supported Platforms

#### 1. Printables.com
- **Status**: ✅ Active (Real scraping implemented)
- **Features**: Model metadata, images, files, licensing
- **Rate Limit**: 10 requests/minute
- **Authentication**: Not required for public models

#### 2. MakerWorld.com (Bambu Lab)
- **Status**: ✅ Active (Real scraping implemented)
- **Features**: Model metadata, images, files, print settings
- **Rate Limit**: 15 requests/minute
- **Authentication**: Not required for public models

#### 3. Thangs.com
- **Status**: ✅ Active (Real scraping implemented)
- **Features**: Model metadata, images, files, designer info
- **Rate Limit**: 12 requests/minute
- **Authentication**: Not required for public models

#### 4. Thingiverse.com
- **Status**: 🔄 Planned
- **Features**: Model metadata, images, files, collections
- **Rate Limit**: TBD
- **Authentication**: API key required

#### 5. MyMiniFactory.com
- **Status**: 🔄 Planned
- **Features**: Model metadata, images, files, premium content
- **Rate Limit**: TBD
- **Authentication**: API key required

### Data Synchronization

#### Import Process

1. **URL Validation**: Verify the provided URL matches supported platform patterns
2. **Data Extraction**: Scrape model metadata, images, and file information
3. **License Detection**: Automatically identify and parse license information
4. **Data Normalization**: Convert platform-specific data to internal format
5. **Storage**: Save model data with proper source attribution
6. **Indexing**: Add to search index with appropriate tags and categories

#### Sync Strategies

- **Manual Import**: User-initiated import via URL
- **Batch Import**: Import multiple models from collections or user profiles
- **Scheduled Sync**: Periodic updates for imported models
- **Real-time Webhooks**: Immediate updates when available

## 3D Technologies

### Spline

[Spline](https://spline.design/) - це інструмент для створення та публікації 3D-дизайнів для веб. Він дозволяє створювати інтерактивні 3D-сцени без написання коду.

**Переваги:**

- Простий інтерфейс для дизайнерів
- Можливість створення складних анімацій
- Легка інтеграція з React

**Недоліки:**

- Обмежена кастомізація через код
- Більший розмір бандла порівняно з чистим Three.js

### Three.js

[Three.js](https://threejs.org/) - це JavaScript-бібліотека для створення та відображення 3D-графіки у веб-браузері.

**Переваги:**

- Повний контроль над 3D-сценою
- Широкі можливості кастомізації
- Велика спільнота та багато прикладів

**Недоліки:**

- Складніший у використанні порівняно зі Spline
- Вимагає більше коду для базових функцій

### React Three Fiber

[React Three Fiber](https://github.com/pmndrs/react-three-fiber) - це React-рендерер для Three.js, який дозволяє використовувати Three.js у декларативному стилі React.

**Переваги:**

- Інтеграція з React-екосистемою
- Декларативний підхід до створення 3D-сцен
- Легше управління станом

**Недоліки:**

- Додатковий рівень абстракції над Three.js
- Може бути складним для розробників, які не знайомі з Three.js

## Компоненти для 3D-інтеграції

### SplineScene

Компонент для відображення 3D-сцен, створених у Spline.

**Імпорт:**

```tsx
import { SplineScene, SCENE_PRESETS } from "@/components/ui/splite";
```

**Пропси:**

- `scene` (string) - URL до Spline-сцени
- `className` (string) - CSS-класи для контейнера
- `preset` (string) - Пресет налаштувань (PRODUCT_VIEWER, INTERACTIVE_SHOWCASE, GALLERY_ITEM, PRINT_SIMULATION)
- `config` (object) - Кастомні налаштування
- `loadingText` (string) - Текст під час завантаження
- `customLoader` (ReactNode) - Кастомний компонент для відображення під час завантаження
- `onLoad` (function) - Колбек після завантаження
- `onProgress` (function) - Колбек під час завантаження
- `onMouseDown`, `onMouseHover`, `onMouseUp`, `onKeyDown`, `onKeyUp` (function) - Колбеки для взаємодії

**Приклад:**

```tsx
<SplineScene
  scene="https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode"
  preset="PRODUCT_VIEWER"
  onLoad={() => console.log("Сцена завантажена")}
/>
```

**Пресети:**

```tsx
export const SCENE_PRESETS = {
  PRODUCT_VIEWER: {
    backgroundColor: 'transparent',
    autoRotate: true,
    enableZoom: true,
    enablePan: false,
  },
  INTERACTIVE_SHOWCASE: {
    backgroundColor: '#000000',
    autoRotate: false,
    enableZoom: true,
    enablePan: true,
  },
  GALLERY_ITEM: {
    backgroundColor: 'transparent',
    autoRotate: true,
    enableZoom: false,
    enablePan: false,
  },
  PRINT_SIMULATION: {
    backgroundColor: '#1a1a1a',
    autoRotate: false,
    enableZoom: true,
    enablePan: true,
  }
};
```

### ModelViewer

Компонент для відображення 3D-моделей з можливістю взаємодії.

**Імпорт:**

```tsx
import { ModelViewer } from "@/components/model-viewer/model-viewer";
```

**Пропси:**

- `model` (object) - Об'єкт з даними моделі
- `autoRotate` (boolean) - Автоматичне обертання моделі
- `showControls` (boolean) - Відображення елементів керування
- `backgroundColor` (string) - Колір фону
- `onLoad` (function) - Колбек після завантаження

**Приклад:**

```tsx
<ModelViewer
  model={model}
  autoRotate={true}
  showControls={true}
/>
```

### CategoryExplorer

Компонент для відображення категорій у 3D-просторі.

**Імпорт:**

```tsx
import CategoryExplorer from "@/components/model-viewer/category-explorer";
```

**Пропси:**

- `splineSceneUrl` (string) - URL до Spline-сцени
- `categories` (array) - Масив категорій
- `onCategorySelect` (function) - Колбек при виборі категорії

**Приклад:**

```tsx
<CategoryExplorer
  splineSceneUrl="https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode"
  categories={categories}
  onCategorySelect={handleCategorySelect}
/>
```

## Формати 3D-файлів

### Підтримувані формати

Маркетплейс підтримує наступні формати 3D-файлів:

- **STL** (.stl) - Стандартний формат для 3D-друку
- **OBJ** (.obj) - Універсальний формат 3D-моделей
- **GLTF/GLB** (.gltf, .glb) - Ефективний формат для веб
- **FBX** (.fbx) - Формат з підтримкою анімацій
- **3DS** (.3ds) - Формат 3D Studio Max
- **DAE** (.dae) - Формат Collada

### Рекомендації щодо оптимізації

Для оптимальної продуктивності рекомендується:

1. **Розмір файлу** - Не більше 15 МБ для веб-перегляду
2. **Кількість полігонів** - Не більше 100,000 полігонів
3. **Текстури** - Оптимізовані текстури розміром не більше 2048x2048
4. **Формат** - GLTF/GLB для веб-перегляду, STL для 3D-друку

## Взаємодія з 3D-моделями

### Обертання

Обертання моделі можливе за допомогою:

- Перетягування мишею (ліва кнопка)
- Сенсорного екрану (один палець)
- Програмного обертання через API

```tsx
// Приклад програмного обертання
const handleRotate = (direction) => {
  if (splineApp) {
    splineApp.rotate(direction, 45); // обертання на 45 градусів
  }
};
```

### Масштабування

Масштабування моделі можливе за допомогою:

- Колеса миші
- Сенсорного екрану (два пальці)
- Програмного масштабування через API

```tsx
// Приклад програмного масштабування
const handleZoom = (factor) => {
  if (splineApp) {
    splineApp.zoom(factor); // масштабування з коефіцієнтом
  }
};
```

### Панорамування

Панорамування моделі можливе за допомогою:

- Перетягування мишею (права кнопка)
- Сенсорного екрану (два пальці)
- Програмного панорамування через API

```tsx
// Приклад програмного панорамування
const handlePan = (x, y) => {
  if (splineApp) {
    splineApp.pan(x, y); // панорамування по осях X та Y
  }
};
```

### Зміна освітлення

Зміна освітлення можлива через API:

```tsx
// Приклад зміни освітлення
const handleLightChange = (intensity) => {
  if (splineApp) {
    splineApp.setLightIntensity(intensity); // зміна інтенсивності світла
  }
};
```

## Налаштування 3D-сцени

### Освітлення

Типи освітлення, які можна використовувати:

- Ambient Light - загальне освітлення сцени
- Directional Light - спрямоване світло (як сонце)
- Point Light - точкове світло (як лампочка)
- Spot Light - спрямоване конусоподібне світло

```tsx
// Приклад налаштування освітлення в Three.js
<ambientLight intensity={0.5} />
<spotLight position={[10, 10, 10]} angle={0.15} penumbra={1} />
<pointLight position={[-10, -10, -10]} />
```

### Фон

Типи фону, які можна використовувати:

- Solid Color - суцільний колір
- Gradient - градієнт
- Environment Map - карта оточення
- HDRI - високодинамічне зображення

```tsx
// Приклад налаштування фону в Three.js
<color attach="background" args={['#f0f0f0']} />
<Environment preset="sunset" />
```

### Камера

Типи камер, які можна використовувати:

- Perspective Camera - перспективна камера (як людське око)
- Orthographic Camera - ортографічна камера (без перспективи)

```tsx
// Приклад налаштування камери в Three.js
<PerspectiveCamera makeDefault position={[0, 0, 5]} />
```

## Продуктивність

### Оптимізація моделей

Для оптимізації 3D-моделей рекомендується:

1. Зменшити кількість полігонів
2. Оптимізувати текстури
3. Використовувати компресію
4. Видалити невидимі частини моделі

### Ліниве завантаження

Для покращення продуктивності використовується ліниве завантаження:

```tsx
// Приклад лінивого завантаження
const Spline = lazy(() => import('@splinetool/react-spline'));

// Використання з Suspense
<Suspense fallback={<LoadingIndicator />}>
  <Spline scene="url-to-scene" />
</Suspense>
```

### Рівні деталізації (LOD)

Для великих моделей рекомендується використовувати рівні деталізації:

```tsx
// Приклад використання LOD в Three.js
<LOD>
  <mesh position={[0, 0, 0]} geometry={highDetailGeometry} visible={false} distance={0} />
  <mesh position={[0, 0, 0]} geometry={mediumDetailGeometry} visible={false} distance={10} />
  <mesh position={[0, 0, 0]} geometry={lowDetailGeometry} visible={false} distance={20} />
</LOD>
```

## Приклади використання

### Перегляд моделі

```tsx
<div className="h-[500px] relative">
  <ModelViewer
    model={{
      id: '1',
      name: 'Articulated Dragon',
      splineSceneUrl: 'https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode',
    }}
    autoRotate={true}
    showControls={true}
  />
</div>
```

### Категорії в 3D

```tsx
<CategoryExplorer
  splineSceneUrl="https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode"
  categories={[
    { id: 1, name: 'Art', count: 245 },
    { id: 2, name: 'Gadgets', count: 183 },
    { id: 3, name: 'Home', count: 127 },
  ]}
  onCategorySelect={(category) => console.log(`Selected category: ${category.name}`)}
/>
```

### Симуляція друку

```tsx
<PrintSimulation
  model={{
    id: '1',
    name: 'Articulated Dragon',
    splineSceneUrl: 'https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode',
  }}
  settings={{
    material: 'PLA',
    layerHeight: '0.2mm',
    infill: '20%',
    supports: 'Minimal',
  }}
/>
```

## Рекомендації для розробників

1. **Оптимізуйте моделі** - Зменшуйте кількість полігонів та розмір текстур
2. **Використовуйте GLTF/GLB** - Це найбільш ефективний формат для веб
3. **Додавайте індикатори завантаження** - 3D-моделі можуть завантажуватися довго
4. **Тестуйте на різних пристроях** - Продуктивність може сильно відрізнятися
5. **Використовуйте ліниве завантаження** - Завантажуйте моделі тільки коли вони потрібні
6. **Додавайте альтернативний контент** - Для пристроїв, які не підтримують WebGL
7. **Оптимізуйте освітлення** - Використовуйте мінімальну кількість джерел світла
8. **Використовуйте компресію** - Стискайте 3D-файли для швидшого завантаження
