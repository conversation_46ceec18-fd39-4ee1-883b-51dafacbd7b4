/**
 * Утиліти для роботи з Cloudflare D1 базою даних
 */

// Типи для D1
type D1Result<T = unknown> = {
  results: T[];
  success: boolean;
  meta: {
    duration: number;
    rows_read: number;
    rows_written: number;
  };
};

type D1ExecResult = {
  success: boolean;
  meta: {
    duration: number;
    rows_written: number;
  };
};

// Інтерфейс для D1Database
export interface D1Database {
  prepare: (query: string) => D1PreparedStatement;
  dump: () => Promise<ArrayBuffer>;
  batch: (statements: D1PreparedStatement[]) => Promise<D1Result[]>;
  exec: (query: string) => Promise<D1ExecResult>;
}

// Інтерфейс для D1PreparedStatement
export interface D1PreparedStatement {
  bind: (...values: any[]) => D1PreparedStatement;
  first: <T = unknown>(colName?: string) => Promise<T | null>;
  run: () => Promise<D1ExecResult>;
  all: <T = unknown>() => Promise<D1Result<T>>;
  raw: <T = unknown>() => Promise<T[]>;
}

// Глобальна змінна для зберігання екземпляра бази даних
let db: D1Database | null = null;

/**
 * Отримує екземпляр бази даних D1
 * @returns Екземпляр бази даних D1 або null, якщо база даних недоступна
 */
export function getDb(): D1Database | null {
  if (!db) {
    // В середовищі Cloudflare Pages, D1 доступна через env.DB
    if (typeof process !== 'undefined' && process.env && process.env.DB) {
      db = process.env.DB as unknown as D1Database;
    } else {
      return null;
    }
  }
  return db;
}

/**
 * Виконує SQL-запит до бази даних
 * @param sql SQL-запит
 * @param params Параметри запиту
 * @returns Результат запиту або порожній масив, якщо база даних недоступна
 */
export async function query<T = unknown>(sql: string, params: any[] = []): Promise<T[]> {
  const db = getDb();
  if (!db) {
    console.warn('База даних недоступна. Повертається порожній масив.');
    return [];
  }
  const stmt = db.prepare(sql);
  const result = await stmt.bind(...params).all<T>();
  return result.results;
}

/**
 * Отримує один запис з бази даних
 * @param sql SQL-запит
 * @param params Параметри запиту
 * @returns Перший запис або null
 */
export async function queryOne<T = unknown>(sql: string, params: any[] = []): Promise<T | null> {
  const db = getDb();
  if (!db) {
    console.warn('База даних недоступна. Повертається null.');
    return null;
  }
  const stmt = db.prepare(sql);
  return await stmt.bind(...params).first<T>();
}

/**
 * Виконує SQL-запит без повернення результатів
 * @param sql SQL-запит
 * @param params Параметри запиту
 * @returns Результат виконання або заглушка, якщо база даних недоступна
 */
export async function execute(sql: string, params: any[] = []): Promise<D1ExecResult> {
  const db = getDb();
  if (!db) {
    console.warn('База даних недоступна. Повертається заглушка.');
    return {
      success: false,
      meta: {
        duration: 0,
        rows_written: 0
      }
    };
  }
  const stmt = db.prepare(sql);
  return await stmt.bind(...params).run();
}

/**
 * Виконує пакет SQL-запитів
 * @param statements Масив підготовлених запитів
 * @returns Результати виконання або порожній масив, якщо база даних недоступна
 */
export async function batch(statements: D1PreparedStatement[]): Promise<D1Result[]> {
  const db = getDb();
  if (!db) {
    console.warn('База даних недоступна. Повертається порожній масив.');
    return [];
  }
  return await db.batch(statements);
}

/**
 * Генерує унікальний ідентифікатор
 * @returns Унікальний ідентифікатор
 */
export function generateId(): string {
  return crypto.randomUUID();
}

// Тип для скрапованих моделей (щоб уникнути циклічного імпорту)
interface ScrapedModel {
  id: string;
  name: string;
  description: string;
  thumbnail_url: string;
  model_url: string;
  category: string;
  tags: string[];
  author_name: string;
  author_avatar: string;
  download_count: number;
  like_count: number;
  view_count: number;
  is_free: boolean;
  price: number;
  file_size: number;
  file_formats: string[];
  print_settings: any;
  license: string;
  created_at: string;
  source_url: string;
  source_platform: string;
}

// Тимчасова база даних в пам'яті для розробки
interface MockDatabase {
  models: any[];
  users: any[];
  orders: any[];
}

// Тестові дані для демонстрації
const testModels = [
  {
    id: 'printables_1286204_terminator_t800',
    name: 'Terminator T800 Armor',
    description: 'Detailed armor pieces for cosplay and display. High-quality STL files with perfect details.',
    thumbnail_url: 'https://images.unsplash.com/photo-1581235720704-06d3acfcb36f?q=80&w=1935&auto=format&fit=crop',
    model_url: 'https://example.com/models/terminator-t800.stl',
    category: 'Cosplay',
    tags: ['terminator', 'armor', 'cosplay', 'sci-fi'],
    author_name: 'CyberCraft3D',
    author_avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    download_count: 12543,
    like_count: 3254,
    view_count: 45678,
    is_free: true,
    price: 0,
    file_size: 15.2,
    file_formats: ['STL'],
    print_settings: {
      material: 'PLA',
      layerHeight: '0.2mm',
      infill: '20%',
      supports: 'Minimal'
    },
    license: 'Creative Commons - Attribution',
    created_at: '2023-05-15T10:30:00Z',
    source_url: 'https://printables.com/model/1286204',
    source_platform: 'Printables'
  },
  {
    id: 'model_dragon_articulated',
    name: 'Articulated Dragon',
    description: 'A fully articulated dragon model with movable joints. Perfect for testing your printer capabilities.',
    thumbnail_url: 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?q=80&w=2070&auto=format&fit=crop',
    model_url: 'https://example.com/models/dragon.stl',
    category: 'Toys',
    tags: ['dragon', 'articulated', 'fantasy', 'toy'],
    author_name: 'DragonMaster3D',
    author_avatar: 'https://randomuser.me/api/portraits/men/42.jpg',
    download_count: 8765,
    like_count: 2134,
    view_count: 23456,
    is_free: true,
    price: 0,
    file_size: 8.7,
    file_formats: ['STL'],
    print_settings: {
      material: 'PLA',
      layerHeight: '0.15mm',
      infill: '15%',
      supports: 'None'
    },
    license: 'Creative Commons - Attribution',
    created_at: '2023-06-01T14:20:00Z',
    source_url: 'https://example.com/dragon',
    source_platform: 'Custom'
  },
  {
    id: 'model_smartphone_stand',
    name: 'Smartphone Stand',
    description: 'Adjustable smartphone stand for desk use. Compatible with most phone sizes.',
    thumbnail_url: 'https://images.unsplash.com/photo-1517420704952-d9f39e95b43e?q=80&w=1854&auto=format&fit=crop',
    model_url: 'https://example.com/models/phone-stand.stl',
    category: 'Accessories',
    tags: ['phone', 'stand', 'desk', 'utility'],
    author_name: 'TechPrints',
    author_avatar: 'https://randomuser.me/api/portraits/women/24.jpg',
    download_count: 15432,
    like_count: 4567,
    view_count: 34567,
    is_free: true,
    price: 0,
    file_size: 2.3,
    file_formats: ['STL'],
    print_settings: {
      material: 'PETG',
      layerHeight: '0.2mm',
      infill: '25%',
      supports: 'None'
    },
    license: 'Creative Commons - Attribution',
    created_at: '2023-06-10T09:15:00Z',
    source_url: 'https://example.com/phone-stand',
    source_platform: 'Custom'
  }
];

const mockDatabase: MockDatabase = {
  models: [...testModels],
  users: [],
  orders: []
};

/**
 * Функція для збереження скрапованих моделей в базу даних
 * @param models Масив скрапованих моделей
 */
export async function saveModelsToDatabase(models: ScrapedModel[]): Promise<void> {
  try {
    console.log(`💾 Збереження ${models.length} моделей в базу даних...`);

    // Конвертуємо скраповані моделі в формат нашої бази даних
    const dbModels = models.map(model => ({
      id: model.id,
      name: model.name,
      description: model.description,
      thumbnail_url: model.thumbnail_url,
      model_url: model.model_url,
      category: model.category,
      tags: model.tags,
      author_name: model.author_name,
      author_avatar: model.author_avatar,
      download_count: model.download_count,
      like_count: model.like_count,
      view_count: model.view_count,
      is_free: model.is_free,
      price: model.price,
      file_size: model.file_size,
      file_formats: model.file_formats,
      print_settings: model.print_settings,
      license: model.license,
      created_at: model.created_at,
      source_url: model.source_url,
      source_platform: model.source_platform
    }));

    // Додаємо моделі до існуючих
    mockDatabase.models.push(...dbModels);

    // Видаляємо дублікати за ID
    const uniqueModels = mockDatabase.models.filter((model, index, self) =>
      index === self.findIndex(m => m.id === model.id)
    );

    mockDatabase.models = uniqueModels;

    console.log(`✅ Збережено ${dbModels.length} моделей. Загалом в базі: ${mockDatabase.models.length}`);
  } catch (error) {
    console.error('❌ Помилка збереження в базу даних:', error);
    throw error;
  }
}

/**
 * Отримує всі моделі з бази даних
 * @returns Масив моделей
 */
export async function getAllModels(): Promise<any[]> {
  return mockDatabase.models;
}

/**
 * Отримує модель за ID
 * @param id ID моделі
 * @returns Модель або null
 */
export async function getModelById(id: string): Promise<any | null> {
  return mockDatabase.models.find(model => model.id === id) || null;
}

/**
 * Пошук моделей за запитом
 * @param searchQuery Пошуковий запит
 * @returns Масив знайдених моделей
 */
export async function searchModels(searchQuery: string): Promise<any[]> {
  const query = searchQuery.toLowerCase();
  return mockDatabase.models.filter(model =>
    model.name.toLowerCase().includes(query) ||
    model.description.toLowerCase().includes(query) ||
    model.tags.some((tag: string) => tag.toLowerCase().includes(query)) ||
    model.category.toLowerCase().includes(query)
  );
}

/**
 * Отримує моделі за категорією
 * @param category Категорія
 * @returns Масив моделей категорії
 */
export async function getModelsByCategory(category: string): Promise<any[]> {
  return mockDatabase.models.filter(model =>
    model.category.toLowerCase() === category.toLowerCase()
  );
}

/**
 * Отримує популярні моделі
 * @param limit Кількість моделей
 * @returns Масив популярних моделей
 */
export async function getPopularModels(limit: number = 20): Promise<any[]> {
  return mockDatabase.models
    .sort((a, b) => (b.download_count + b.like_count) - (a.download_count + a.like_count))
    .slice(0, limit);
}

/**
 * Отримує нові моделі
 * @param limit Кількість моделей
 * @returns Масив нових моделей
 */
export async function getNewModels(limit: number = 20): Promise<any[]> {
  return mockDatabase.models
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    .slice(0, limit);
}

/**
 * Отримує безкоштовні моделі
 * @param limit Кількість моделей
 * @returns Масив безкоштовних моделей
 */
export async function getFreeModels(limit: number = 20): Promise<any[]> {
  return mockDatabase.models
    .filter(model => model.is_free)
    .slice(0, limit);
}
