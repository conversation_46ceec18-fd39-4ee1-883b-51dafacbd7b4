'use client';

import ModelShowcaseExample from '@/components/model-viewer/spline-showcase';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

export default function SplineShowcasePage() {
  return (
    <div className="container mx-auto py-10">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold mb-4 text-center">Spline 3D Showcase</h1>
        <p className="text-muted-foreground text-center mb-10">
          Explore interactive 3D models created with <PERSON><PERSON><PERSON> and integrated into our marketplace
        </p>
        
        <ModelShowcaseExample />
        
        <div className="mt-16 space-y-8">
          <div className="bg-muted/50 rounded-lg p-6">
            <h2 className="text-2xl font-semibold mb-4">Creating Custom Spline Scenes</h2>
            <p className="mb-4">
              Spline is a powerful tool for creating interactive 3D scenes that can significantly enhance your 3D marketplace experience. 
              Here's how to create and integrate your own custom scenes:
            </p>
            
            <ol className="list-decimal pl-5 space-y-2">
              <li>
                <strong>Sign up for Spline:</strong> Visit <a href="https://spline.design" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">spline.design</a> and create an account
              </li>
              <li>
                <strong>Create a new project:</strong> Use the Spline editor to create a 3D scene
              </li>
              <li>
                <strong>Design your scene:</strong> Import 3D models, add lighting, materials, and animations
              </li>
              <li>
                <strong>Add interactivity:</strong> Create clickable objects, hover effects, and animations
              </li>
              <li>
                <strong>Export your scene:</strong> Publish your scene to get a unique scene URL
              </li>
              <li>
                <strong>Integrate with our components:</strong> Use the scene URL with our SplineScene component
              </li>
            </ol>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-muted/50 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-3">Scene Ideas for Models</h3>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <span className="inline-block w-1.5 h-1.5 rounded-full bg-primary mt-2 mr-2"></span>
                  <span>Interactive model viewers with exploded views</span>
                </li>
                <li className="flex items-start">
                  <span className="inline-block w-1.5 h-1.5 rounded-full bg-primary mt-2 mr-2"></span>
                  <span>Animated assembly instructions</span>
                </li>
                <li className="flex items-start">
                  <span className="inline-block w-1.5 h-1.5 rounded-full bg-primary mt-2 mr-2"></span>
                  <span>Physics simulations to demonstrate functionality</span>
                </li>
                <li className="flex items-start">
                  <span className="inline-block w-1.5 h-1.5 rounded-full bg-primary mt-2 mr-2"></span>
                  <span>Interactive measurement tools</span>
                </li>
              </ul>
            </div>
            
            <div className="bg-muted/50 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-3">Scene Ideas for Categories</h3>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <span className="inline-block w-1.5 h-1.5 rounded-full bg-primary mt-2 mr-2"></span>
                  <span>3D environments representing different categories</span>
                </li>
                <li className="flex items-start">
                  <span className="inline-block w-1.5 h-1.5 rounded-full bg-primary mt-2 mr-2"></span>
                  <span>Interactive category maps with visual navigation</span>
                </li>
                <li className="flex items-start">
                  <span className="inline-block w-1.5 h-1.5 rounded-full bg-primary mt-2 mr-2"></span>
                  <span>Themed showcases for seasonal collections</span>
                </li>
                <li className="flex items-start">
                  <span className="inline-block w-1.5 h-1.5 rounded-full bg-primary mt-2 mr-2"></span>
                  <span>Virtual galleries with curated model displays</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
        
        <div className="mt-12 text-center">
          <p className="text-muted-foreground mb-4">
            Ready to explore more interactive 3D experiences?
          </p>
          <div className="flex justify-center gap-4">
            <Link href="/spline-integration">
              <Button variant="outline">View More Examples</Button>
            </Link>
            <Link href="/">
              <Button>Back to Home</Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
