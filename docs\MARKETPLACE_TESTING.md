# Тестування маркетплейсу 3D-моделей

## Зміст

1. [Вступ](#вступ)
2. [Типи тестування](#типи-тестування)
3. [Налаштування середовища тестування](#налаштування-середовища-тестування)
4. [Модульне тестування](#модульне-тестування)
5. [Інтеграційне тестування](#інтеграційне-тестування)
6. [Тестування інтерфейсу користувача](#тестування-інтерфейсу-користувача)
7. [Тестування API](#тестування-api)
8. [Тестування продуктивності](#тестування-продуктивності)
9. [Тестування безпеки](#тестування-безпеки)
10. [Тестування платежів](#тестування-платежів)
11. [Тестування 3D-візуалізації](#тестування-3d-візуалізації)
12. [Автоматизація тестування](#автоматизація-тестування)
13. [Безперервна інтеграція](#безперервна-інтеграція)

## Вступ

Цей документ описує підходи та методи тестування маркетплейсу 3D-моделей. Тестування є критично важливим для забезпечення якості, надійності та безпеки платформи.

## Типи тестування

Для забезпечення повного покриття функціональності маркетплейсу використовуються наступні типи тестування:

1. **Модульне тестування** - тестування окремих компонентів та функцій
2. **Інтеграційне тестування** - тестування взаємодії між компонентами
3. **Тестування інтерфейсу користувача** - тестування UI компонентів та взаємодії користувача
4. **Тестування API** - тестування API маршрутів та ендпоінтів
5. **Тестування продуктивності** - тестування швидкодії та масштабованості
6. **Тестування безпеки** - тестування на вразливості та захист даних
7. **Тестування платежів** - тестування інтеграції з платіжними системами
8. **Тестування 3D-візуалізації** - тестування відображення та взаємодії з 3D-моделями

## Налаштування середовища тестування

### Встановлення залежностей

```bash
npm install --save-dev jest @testing-library/react @testing-library/jest-dom @testing-library/user-event jest-environment-jsdom msw cypress
```

### Налаштування Jest

Створіть файл `jest.config.js` в кореневій директорії проекту:

```javascript
// jest.config.js
const nextJest = require('next/jest');

const createJestConfig = nextJest({
  dir: './',
});

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapper: {
    '^@/components/(.*)$': '<rootDir>/src/components/$1',
    '^@/lib/(.*)$': '<rootDir>/src/lib/$1',
    '^@/app/(.*)$': '<rootDir>/src/app/$1',
  },
  testEnvironment: 'jest-environment-jsdom',
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{js,jsx,ts,tsx}',
    '!src/pages/_app.tsx',
    '!src/pages/_document.tsx',
  ],
};

module.exports = createJestConfig(customJestConfig);
```

Створіть файл `jest.setup.js`:

```javascript
// jest.setup.js
import '@testing-library/jest-dom';

// Мокування глобальних об'єктів
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Мокування Three.js
jest.mock('three', () => {
  return {
    WebGLRenderer: jest.fn().mockImplementation(() => ({
      setSize: jest.fn(),
      render: jest.fn(),
      shadowMap: {},
      domElement: document.createElement('canvas'),
    })),
    Scene: jest.fn(),
    PerspectiveCamera: jest.fn(),
    // Інші необхідні класи та функції
  };
});

// Мокування React Three Fiber
jest.mock('@react-three/fiber', () => ({
  Canvas: ({ children }) => <div>{children}</div>,
  useThree: () => ({
    camera: {},
    scene: {},
    gl: {
      render: jest.fn(),
      domElement: document.createElement('canvas'),
    },
  }),
  useLoader: jest.fn(),
}));
```

### Налаштування Cypress

Створіть файл `cypress.config.js`:

```javascript
// cypress.config.js
const { defineConfig } = require('cypress');

module.exports = defineConfig({
  e2e: {
    baseUrl: 'http://localhost:3000',
    setupNodeEvents(on, config) {
      // Налаштування подій
    },
  },
  component: {
    devServer: {
      framework: 'next',
      bundler: 'webpack',
    },
  },
});
```

## Модульне тестування

Модульне тестування використовується для перевірки окремих компонентів та функцій.

### Тестування утиліт

```javascript
// src/lib/utils.test.ts
import { formatPrice, truncateText } from './utils';

describe('Utils', () => {
  describe('formatPrice', () => {
    it('should format price correctly', () => {
      expect(formatPrice(10)).toBe('$10.00');
      expect(formatPrice(10.5)).toBe('$10.50');
      expect(formatPrice(0)).toBe('Free');
    });
  });

  describe('truncateText', () => {
    it('should truncate text if it exceeds max length', () => {
      expect(truncateText('Hello World', 5)).toBe('Hello...');
    });

    it('should not truncate text if it does not exceed max length', () => {
      expect(truncateText('Hello', 5)).toBe('Hello');
    });
  });
});
```

### Тестування хуків

```javascript
// src/hooks/useCart.test.ts
import { renderHook, act } from '@testing-library/react';
import { useCart } from './useCart';

describe('useCart', () => {
  it('should add item to cart', () => {
    const { result } = renderHook(() => useCart());

    act(() => {
      result.current.addToCart({
        id: '1',
        title: 'Test Model',
        price: 10,
      });
    });

    expect(result.current.cart).toHaveLength(1);
    expect(result.current.cart[0].id).toBe('1');
  });

  it('should remove item from cart', () => {
    const { result } = renderHook(() => useCart());

    act(() => {
      result.current.addToCart({
        id: '1',
        title: 'Test Model',
        price: 10,
      });
    });

    act(() => {
      result.current.removeFromCart('1');
    });

    expect(result.current.cart).toHaveLength(0);
  });
});
```

## Інтеграційне тестування

Інтеграційне тестування перевіряє взаємодію між різними компонентами системи.

### Тестування взаємодії компонентів

```javascript
// src/components/marketplace/ModelCard.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import ModelCard from './ModelCard';
import { useCart } from '@/hooks/useCart';

// Мокування хука useCart
jest.mock('@/hooks/useCart', () => ({
  useCart: jest.fn(),
}));

describe('ModelCard', () => {
  const mockModel = {
    id: '1',
    title: 'Test Model',
    thumbnail: 'https://example.com/thumbnail.jpg',
    designer: {
      name: 'Test Designer',
    },
    price: 10,
    likes: 5,
    downloads: 100,
  };

  const mockAddToCart = jest.fn();

  beforeEach(() => {
    (useCart as jest.Mock).mockReturnValue({
      addToCart: mockAddToCart,
    });
  });

  it('should render model information correctly', () => {
    render(<ModelCard model={mockModel} />);

    expect(screen.getByText('Test Model')).toBeInTheDocument();
    expect(screen.getByText('by Test Designer')).toBeInTheDocument();
    expect(screen.getByText('$10.00')).toBeInTheDocument();
  });

  it('should call addToCart when add to cart button is clicked', () => {
    render(<ModelCard model={mockModel} />);

    const addToCartButton = screen.getByRole('button', { name: /add to cart/i });
    fireEvent.click(addToCartButton);

    expect(mockAddToCart).toHaveBeenCalledWith(mockModel);
  });
});
```

## Тестування інтерфейсу користувача

Тестування UI перевіряє правильність відображення та взаємодії з інтерфейсом користувача.

### Тестування компонентів UI

```javascript
// src/components/ui/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import Button from './Button';

describe('Button', () => {
  it('should render button with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument();
  });

  it('should call onClick when clicked', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByRole('button', { name: /click me/i }));
    
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('should be disabled when disabled prop is true', () => {
    render(<Button disabled>Click me</Button>);
    expect(screen.getByRole('button', { name: /click me/i })).toBeDisabled();
  });

  it('should render with different variants', () => {
    const { rerender } = render(<Button variant="primary">Primary</Button>);
    expect(screen.getByRole('button', { name: /primary/i })).toHaveClass('bg-blue-500');
    
    rerender(<Button variant="secondary">Secondary</Button>);
    expect(screen.getByRole('button', { name: /secondary/i })).toHaveClass('bg-gray-500');
  });
});
```

### Тестування сторінок

```javascript
// src/app/marketplace/page.test.tsx
import { render, screen, waitFor } from '@testing-library/react';
import MarketplacePage from './page';
import { getModels } from '@/lib/api/models';

// Мокування API
jest.mock('@/lib/api/models', () => ({
  getModels: jest.fn(),
}));

describe('MarketplacePage', () => {
  beforeEach(() => {
    (getModels as jest.Mock).mockResolvedValue({
      data: [
        {
          id: '1',
          title: 'Test Model 1',
          thumbnail: 'https://example.com/thumbnail1.jpg',
          designer: {
            name: 'Designer 1',
          },
          price: 10,
          likes: 5,
          downloads: 100,
        },
        {
          id: '2',
          title: 'Test Model 2',
          thumbnail: 'https://example.com/thumbnail2.jpg',
          designer: {
            name: 'Designer 2',
          },
          price: 0,
          likes: 10,
          downloads: 200,
        },
      ],
    });
  });

  it('should render marketplace page with models', async () => {
    render(<MarketplacePage />);
    
    await waitFor(() => {
      expect(screen.getByText('Test Model 1')).toBeInTheDocument();
      expect(screen.getByText('Test Model 2')).toBeInTheDocument();
      expect(screen.getByText('by Designer 1')).toBeInTheDocument();
      expect(screen.getByText('by Designer 2')).toBeInTheDocument();
      expect(screen.getByText('$10.00')).toBeInTheDocument();
      expect(screen.getByText('Free')).toBeInTheDocument();
    });
  });
});
```

## Тестування API

Тестування API перевіряє правильність роботи API маршрутів та ендпоінтів.

### Мокування API запитів

```javascript
// src/mocks/handlers.ts
import { rest } from 'msw';

export const handlers = [
  rest.get('/api/models', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        data: [
          {
            id: '1',
            title: 'Test Model 1',
            thumbnail: 'https://example.com/thumbnail1.jpg',
            designer: {
              name: 'Designer 1',
            },
            price: 10,
            likes: 5,
            downloads: 100,
          },
          {
            id: '2',
            title: 'Test Model 2',
            thumbnail: 'https://example.com/thumbnail2.jpg',
            designer: {
              name: 'Designer 2',
            },
            price: 0,
            likes: 10,
            downloads: 200,
          },
        ],
      })
    );
  }),
  
  rest.get('/api/models/:id', (req, res, ctx) => {
    const { id } = req.params;
    
    return res(
      ctx.status(200),
      ctx.json({
        data: {
          id,
          title: `Test Model ${id}`,
          description: 'Test description',
          thumbnail: `https://example.com/thumbnail${id}.jpg`,
          designer: {
            name: `Designer ${id}`,
          },
          price: id === '1' ? 10 : 0,
          likes: 5,
          downloads: 100,
        },
      })
    );
  }),
  
  rest.post('/api/models', (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        data: {
          id: '3',
          ...req.body,
        },
      })
    );
  }),
];
```

### Налаштування MSW для тестування

```javascript
// src/mocks/server.ts
import { setupServer } from 'msw/node';
import { handlers } from './handlers';

export const server = setupServer(...handlers);
```

Додайте налаштування MSW до `jest.setup.js`:

```javascript
// jest.setup.js
import { server } from './src/mocks/server';

// Запуск сервера перед усіма тестами
beforeAll(() => server.listen());

// Скидання обробників між тестами
afterEach(() => server.resetHandlers());

// Закриття сервера після усіх тестів
afterAll(() => server.close());
```

### Тестування API клієнтів

```javascript
// src/lib/api/models.test.ts
import { getModels, getModel, createModel } from './models';
import { server } from '@/mocks/server';
import { rest } from 'msw';

describe('Models API', () => {
  it('should fetch models', async () => {
    const response = await getModels();
    
    expect(response.data).toHaveLength(2);
    expect(response.data[0].title).toBe('Test Model 1');
    expect(response.data[1].title).toBe('Test Model 2');
  });
  
  it('should fetch a single model', async () => {
    const response = await getModel('1');
    
    expect(response.data.id).toBe('1');
    expect(response.data.title).toBe('Test Model 1');
  });
  
  it('should create a model', async () => {
    const modelData = {
      title: 'New Model',
      description: 'New model description',
      price: 15,
      category: 'Art',
    };
    
    const response = await createModel(modelData);
    
    expect(response.data.id).toBe('3');
    expect(response.data.title).toBe('New Model');
  });
  
  it('should handle errors', async () => {
    // Перевизначення обробника для симуляції помилки
    server.use(
      rest.get('/api/models', (req, res, ctx) => {
        return res(ctx.status(500), ctx.json({ error: 'Server error' }));
      })
    );
    
    await expect(getModels()).rejects.toThrow('Failed to fetch models');
  });
});
```

## Тестування продуктивності

Тестування продуктивності перевіряє швидкодію та масштабованість маркетплейсу.

### Lighthouse

Використовуйте Lighthouse для тестування продуктивності веб-сторінок:

```javascript
// cypress/e2e/performance.cy.js
describe('Performance Testing', () => {
  it('should have good performance metrics', () => {
    cy.visit('/marketplace');
    
    cy.lighthouse({
      performance: 90,
      accessibility: 90,
      'best-practices': 90,
      seo: 90,
    });
  });
});
```

### Тестування завантаження 3D-моделей

```javascript
// cypress/e2e/model-loading.cy.js
describe('3D Model Loading', () => {
  it('should load 3D model within acceptable time', () => {
    cy.visit('/marketplace/1');
    
    // Перевірка часу завантаження 3D-моделі
    cy.get('[data-testid="model-viewer"]', { timeout: 10000 }).should('be.visible');
    
    // Перевірка, що індикатор завантаження зникає
    cy.get('[data-testid="loading-indicator"]').should('not.exist');
  });
});
```

## Тестування безпеки

Тестування безпеки перевіряє захищеність маркетплейсу від різних типів атак.

### CSRF захист

```javascript
// cypress/e2e/security/csrf.cy.js
describe('CSRF Protection', () => {
  it('should include CSRF token in forms', () => {
    cy.visit('/auth/login');
    
    cy.get('form').should('have.attr', 'action', '/api/auth/callback/credentials');
    cy.get('input[name="csrfToken"]').should('exist');
  });
});
```

### XSS захист

```javascript
// cypress/e2e/security/xss.cy.js
describe('XSS Protection', () => {
  it('should sanitize user input', () => {
    // Спроба XSS атаки через пошуковий запит
    cy.visit('/marketplace?search=<script>alert("XSS")</script>');
    
    // Перевірка, що скрипт не виконується
    cy.on('window:alert', (text) => {
      throw new Error('Alert should not be triggered');
    });
    
    // Перевірка, що пошуковий запит відображається як текст
    cy.get('[data-testid="search-query"]').should('contain', '<script>alert("XSS")</script>');
  });
});
```

## Тестування платежів

Тестування платежів перевіряє правильність роботи інтеграції з платіжними системами.

### Тестування Stripe інтеграції

```javascript
// cypress/e2e/payments/stripe.cy.js
describe('Stripe Integration', () => {
  beforeEach(() => {
    // Логін користувача
    cy.login('<EMAIL>', 'password');
  });
  
  it('should complete checkout process', () => {
    // Перехід на сторінку моделі
    cy.visit('/marketplace/1');
    
    // Додавання моделі до кошика
    cy.get('[data-testid="add-to-cart-button"]').click();
    
    // Перехід до оформлення замовлення
    cy.get('[data-testid="checkout-button"]').click();
    
    // Заповнення форми оплати
    cy.get('input[name="cardNumber"]').type('****************');
    cy.get('input[name="cardExpiry"]').type('1230');
    cy.get('input[name="cardCvc"]').type('123');
    
    // Підтвердження оплати
    cy.get('[data-testid="submit-payment-button"]').click();
    
    // Перевірка успішного завершення оплати
    cy.url().should('include', '/marketplace/purchase/success');
    cy.get('[data-testid="success-message"]').should('contain', 'Payment successful');
  });
  
  it('should handle payment errors', () => {
    // Перехід на сторінку моделі
    cy.visit('/marketplace/1');
    
    // Додавання моделі до кошика
    cy.get('[data-testid="add-to-cart-button"]').click();
    
    // Перехід до оформлення замовлення
    cy.get('[data-testid="checkout-button"]').click();
    
    // Заповнення форми оплати з недійсною карткою
    cy.get('input[name="cardNumber"]').type('****************'); // Картка, яка буде відхилена
    cy.get('input[name="cardExpiry"]').type('1230');
    cy.get('input[name="cardCvc"]').type('123');
    
    // Підтвердження оплати
    cy.get('[data-testid="submit-payment-button"]').click();
    
    // Перевірка відображення помилки
    cy.get('[data-testid="payment-error"]').should('contain', 'Your card was declined');
  });
});
```

## Тестування 3D-візуалізації

Тестування 3D-візуалізації перевіряє правильність відображення та взаємодії з 3D-моделями.

### Тестування компонента ModelViewer

```javascript
// src/components/3d-viewer/ModelViewer.test.tsx
import { render, screen } from '@testing-library/react';
import ModelViewer from './ModelViewer';

// Мокування Three.js та React Three Fiber вже налаштовано в jest.setup.js

describe('ModelViewer', () => {
  it('should render without crashing', () => {
    render(
      <ModelViewer
        modelUrl="/models/example.glb"
        modelFormat="glb"
      />
    );
    
    // Перевірка, що компонент відображається
    expect(screen.getByTestId('model-viewer')).toBeInTheDocument();
  });
  
  it('should show loading indicator while loading', () => {
    // Мокування стану завантаження
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [false, jest.fn()]);
    
    render(
      <ModelViewer
        modelUrl="/models/example.glb"
        modelFormat="glb"
      />
    );
    
    // Перевірка, що індикатор завантаження відображається
    expect(screen.getByTestId('loading-indicator')).toBeInTheDocument();
  });
});
```

## Автоматизація тестування

Автоматизація тестування дозволяє запускати тести автоматично при кожній зміні коду.

### Налаштування скриптів npm

Додайте наступні скрипти до `package.json`:

```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:e2e": "cypress run",
    "test:e2e:open": "cypress open"
  }
}
```

## Безперервна інтеграція

Налаштуйте безперервну інтеграцію для автоматичного запуску тестів при кожному пуші до репозиторію.

### GitHub Actions

Створіть файл `.github/workflows/test.yml`:

```yaml
name: Test

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run tests
        run: npm test
        
      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        
  e2e:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build
        run: npm run build
        
      - name: Run Cypress tests
        uses: cypress-io/github-action@v5
        with:
          start: npm start
          wait-on: 'http://localhost:3000'
```
