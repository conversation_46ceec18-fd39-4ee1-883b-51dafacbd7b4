'use client';

import { useSession, signOut } from 'next-auth/react';
import Link from 'next/link';
import { useState } from 'react';
import { User, LogOut, Settings, ShoppingCart } from 'lucide-react';

/**
 * Компонент для відображення стану автентифікації користувача
 * @returns Компонент стану автентифікації
 */
export default function UserAuthStatus() {
  const { data: session, status } = useSession();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  
  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };
  
  const handleSignOut = async () => {
    await signOut({ redirect: false });
    setIsMenuOpen(false);
  };
  
  if (status === 'loading') {
    return (
      <div className="h-10 w-10 rounded-full bg-gray-200 animate-pulse"></div>
    );
  }
  
  if (!session) {
    return (
      <div className="flex space-x-4">
        <Link
          href="/auth/signin"
          className="text-sm font-medium text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md"
          aria-label="Увійти"
          tabIndex={0}
        >
          Увійти
        </Link>
        <Link
          href="/auth/signup"
          className="text-sm font-medium bg-blue-600 text-white hover:bg-blue-700 px-3 py-2 rounded-md"
          aria-label="Зареєструватися"
          tabIndex={0}
        >
          Зареєструватися
        </Link>
      </div>
    );
  }
  
  return (
    <div className="relative">
      <button
        className="flex items-center space-x-2 focus:outline-none"
        onClick={toggleMenu}
        aria-expanded={isMenuOpen}
        aria-haspopup="true"
        aria-label="Меню користувача"
        tabIndex={0}
      >
        <div className="h-10 w-10 rounded-full bg-blue-600 flex items-center justify-center text-white">
          {session.user?.image ? (
            <img
              src={session.user.image}
              alt={session.user.name || 'Аватар користувача'}
              className="h-10 w-10 rounded-full"
            />
          ) : (
            <User className="h-5 w-5" />
          )}
        </div>
        <span className="hidden md:inline-block text-sm font-medium text-gray-700">
          {session.user?.name || 'Користувач'}
        </span>
      </button>
      
      {isMenuOpen && (
        <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 ring-1 ring-black ring-opacity-5">
          <div className="px-4 py-2 border-b border-gray-100">
            <p className="text-sm font-medium text-gray-900">{session.user?.name}</p>
            <p className="text-xs text-gray-500 truncate">{session.user?.email}</p>
          </div>
          
          <Link
            href="/profile"
            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
            onClick={() => setIsMenuOpen(false)}
            aria-label="Профіль"
            tabIndex={0}
          >
            <User className="h-4 w-4 mr-2" />
            Профіль
          </Link>
          
          <Link
            href="/orders"
            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
            onClick={() => setIsMenuOpen(false)}
            aria-label="Замовлення"
            tabIndex={0}
          >
            <ShoppingCart className="h-4 w-4 mr-2" />
            Замовлення
          </Link>
          
          <Link
            href="/settings"
            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
            onClick={() => setIsMenuOpen(false)}
            aria-label="Налаштування"
            tabIndex={0}
          >
            <Settings className="h-4 w-4 mr-2" />
            Налаштування
          </Link>
          
          <button
            className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100 flex items-center"
            onClick={handleSignOut}
            aria-label="Вийти"
            tabIndex={0}
          >
            <LogOut className="h-4 w-4 mr-2" />
            Вийти
          </button>
        </div>
      )}
    </div>
  );
}
