export interface Env {
  RESUMABLE_DOWNLOAD_MANAGER: DurableObjectNamespace;
  DB: D1Database;
  R2_BUCKET: R2Bucket;
  CACHE_KV: KVNamespace;
}

export interface ResumableDownloadSession {
  id: string;
  modelId: string;
  userId: string;
  status: 'pending' | 'downloading' | 'paused' | 'completed' | 'failed';
  totalSize: number;
  downloadedChunks: Set<number>;
  failedChunks: Set<number>;
  chunkSize: number;
  totalChunks: number;
  resumeToken: string;
  downloadUrl?: string;
  expiresAt: number;
  createdAt: number;
  lastActivity: number;
  retryCount: number;
  maxRetries: number;
  checksums: Map<number, string>; // chunk checksums for integrity
}

export interface ChunkDownloadResult {
  chunkId: number;
  success: boolean;
  data?: ArrayBuffer;
  checksum?: string;
  error?: string;
}

export class ResumableDownloadManager {
  private state: DurableObjectState;
  private env: Env;
  private sessions: Map<string, ResumableDownloadSession> = new Map();
  private downloadQueues: Map<string, Promise<void>> = new Map();

  constructor(state: DurableObjectState, env: Env) {
    this.state = state;
    this.env = env;
    
    // Відновлюємо стан з persistent storage
    this.state.blockConcurrencyWhile(async () => {
      const stored = await this.state.storage.get('sessions');
      if (stored) {
        const sessionsArray = stored as Array<[string, any]>;
        for (const [sessionId, sessionData] of sessionsArray) {
          // Відновлюємо Set та Map об'єкти
          sessionData.downloadedChunks = new Set(sessionData.downloadedChunks || []);
          sessionData.failedChunks = new Set(sessionData.failedChunks || []);
          sessionData.checksums = new Map(sessionData.checksums || []);
          this.sessions.set(sessionId, sessionData);
        }
      }
    });

    // Очищуємо застарілі сесії кожні 10 хвилин
    setInterval(() => this.cleanupExpiredSessions(), 10 * 60 * 1000);
  }

  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const path = url.pathname;

    try {
      switch (path) {
        case '/create-resumable-session':
          return await this.createResumableSession(request);
        case '/start-download':
          return await this.startDownload(request);
        case '/pause-download':
          return await this.pauseDownload(request);
        case '/resume-download':
          return await this.resumeDownload(request);
        case '/get-resume-info':
          return await this.getResumeInfo(request);
        case '/verify-chunks':
          return await this.verifyChunks(request);
        case '/download-missing-chunks':
          return await this.downloadMissingChunks(request);
        case '/assemble-file':
          return await this.assembleFile(request);
        default:
          return new Response('Not Found', { status: 404 });
      }
    } catch (error) {
      console.error('ResumableDownloadManager error:', error);
      return new Response('Internal Server Error', { status: 500 });
    }
  }

  private async createResumableSession(request: Request): Promise<Response> {
    const { modelId, userId, chunkSize = 2 * 1024 * 1024 } = await request.json(); // Default 2MB chunks
    
    // Перевіряємо доступ до моделі
    const hasAccess = await this.checkModelAccess(modelId, userId);
    if (!hasAccess) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Access denied'
      }), { status: 403 });
    }

    // Отримуємо інформацію про файл
    const fileInfo = await this.getFileInfo(modelId);
    if (!fileInfo) {
      return new Response(JSON.stringify({
        success: false,
        error: 'File not found'
      }), { status: 404 });
    }

    const sessionId = crypto.randomUUID();
    const resumeToken = crypto.randomUUID();
    const totalChunks = Math.ceil(fileInfo.size / chunkSize);
    
    const session: ResumableDownloadSession = {
      id: sessionId,
      modelId,
      userId,
      status: 'pending',
      totalSize: fileInfo.size,
      downloadedChunks: new Set(),
      failedChunks: new Set(),
      chunkSize,
      totalChunks,
      resumeToken,
      expiresAt: Date.now() + (7 * 24 * 60 * 60 * 1000), // 7 днів
      createdAt: Date.now(),
      lastActivity: Date.now(),
      retryCount: 0,
      maxRetries: 3,
      checksums: new Map()
    };

    this.sessions.set(sessionId, session);
    await this.persistSessions();

    return new Response(JSON.stringify({
      success: true,
      sessionId,
      resumeToken,
      totalSize: fileInfo.size,
      totalChunks,
      chunkSize,
      expiresAt: session.expiresAt
    }));
  }

  private async startDownload(request: Request): Promise<Response> {
    const { sessionId, resumeToken } = await request.json();
    const session = this.sessions.get(sessionId);

    if (!session || session.resumeToken !== resumeToken) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid session or resume token'
      }), { status: 401 });
    }

    session.status = 'downloading';
    session.lastActivity = Date.now();
    await this.persistSessions();

    // Запускаємо завантаження в фоні
    this.downloadQueues.set(sessionId, this.downloadAllChunks(session));

    return new Response(JSON.stringify({
      success: true,
      message: 'Download started',
      downloadedChunks: session.downloadedChunks.size,
      totalChunks: session.totalChunks
    }));
  }

  private async downloadAllChunks(session: ResumableDownloadSession): Promise<void> {
    const concurrentDownloads = 3; // Одночасно завантажуємо 3 чанки
    const downloadPromises: Promise<ChunkDownloadResult>[] = [];

    for (let chunkId = 0; chunkId < session.totalChunks; chunkId++) {
      if (session.downloadedChunks.has(chunkId)) {
        continue; // Чанк вже завантажено
      }

      const downloadPromise = this.downloadChunk(session, chunkId);
      downloadPromises.push(downloadPromise);

      // Обмежуємо кількість одночасних завантажень
      if (downloadPromises.length >= concurrentDownloads) {
        const results = await Promise.allSettled(downloadPromises);
        await this.processChunkResults(session, results);
        downloadPromises.length = 0;
      }
    }

    // Обробляємо останні чанки
    if (downloadPromises.length > 0) {
      const results = await Promise.allSettled(downloadPromises);
      await this.processChunkResults(session, results);
    }

    // Перевіряємо, чи всі чанки завантажено
    if (session.downloadedChunks.size === session.totalChunks) {
      session.status = 'completed';
    } else if (session.failedChunks.size > 0 && session.retryCount < session.maxRetries) {
      // Повторюємо завантаження невдалих чанків
      session.retryCount++;
      await this.retryFailedChunks(session);
    } else {
      session.status = 'failed';
    }

    await this.persistSessions();
  }

  private async downloadChunk(session: ResumableDownloadSession, chunkId: number): Promise<ChunkDownloadResult> {
    try {
      const startByte = chunkId * session.chunkSize;
      const endByte = Math.min(startByte + session.chunkSize - 1, session.totalSize - 1);

      const fileObject = await this.env.R2_BUCKET.get(session.modelId, {
        range: { offset: startByte, length: endByte - startByte + 1 }
      });

      if (!fileObject) {
        throw new Error('Chunk not found in storage');
      }

      const chunkData = await fileObject.arrayBuffer();
      const checksum = await this.calculateChecksum(chunkData);

      // Зберігаємо чанк в тимчасовому сховищі
      await this.storeChunk(session.id, chunkId, chunkData);

      return {
        chunkId,
        success: true,
        data: chunkData,
        checksum
      };
    } catch (error) {
      return {
        chunkId,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async processChunkResults(session: ResumableDownloadSession, results: PromiseSettledResult<ChunkDownloadResult>[]): Promise<void> {
    for (const result of results) {
      if (result.status === 'fulfilled') {
        const chunkResult = result.value;
        if (chunkResult.success) {
          session.downloadedChunks.add(chunkResult.chunkId);
          if (chunkResult.checksum) {
            session.checksums.set(chunkResult.chunkId, chunkResult.checksum);
          }
          session.failedChunks.delete(chunkResult.chunkId);
        } else {
          session.failedChunks.add(chunkResult.chunkId);
        }
      }
    }

    session.lastActivity = Date.now();
    await this.persistSessions();
  }

  private async retryFailedChunks(session: ResumableDownloadSession): Promise<void> {
    const failedChunks = Array.from(session.failedChunks);
    session.failedChunks.clear();

    for (const chunkId of failedChunks) {
      const result = await this.downloadChunk(session, chunkId);
      if (result.success) {
        session.downloadedChunks.add(chunkId);
        if (result.checksum) {
          session.checksums.set(chunkId, result.checksum);
        }
      } else {
        session.failedChunks.add(chunkId);
      }
    }
  }

  private async pauseDownload(request: Request): Promise<Response> {
    const { sessionId, resumeToken } = await request.json();
    const session = this.sessions.get(sessionId);

    if (!session || session.resumeToken !== resumeToken) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid session or resume token'
      }), { status: 401 });
    }

    session.status = 'paused';
    session.lastActivity = Date.now();
    await this.persistSessions();

    return new Response(JSON.stringify({
      success: true,
      downloadedChunks: session.downloadedChunks.size,
      totalChunks: session.totalChunks,
      progress: (session.downloadedChunks.size / session.totalChunks) * 100
    }));
  }

  private async resumeDownload(request: Request): Promise<Response> {
    const { sessionId, resumeToken } = await request.json();
    const session = this.sessions.get(sessionId);

    if (!session || session.resumeToken !== resumeToken) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid session or resume token'
      }), { status: 401 });
    }

    if (session.status === 'completed') {
      return new Response(JSON.stringify({
        success: true,
        message: 'Download already completed',
        downloadUrl: await this.getAssembledFileUrl(session)
      }));
    }

    session.status = 'downloading';
    session.lastActivity = Date.now();
    await this.persistSessions();

    // Продовжуємо завантаження
    this.downloadQueues.set(sessionId, this.downloadAllChunks(session));

    return new Response(JSON.stringify({
      success: true,
      message: 'Download resumed',
      downloadedChunks: session.downloadedChunks.size,
      remainingChunks: session.totalChunks - session.downloadedChunks.size
    }));
  }

  private async getResumeInfo(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const sessionId = url.searchParams.get('sessionId');
    const resumeToken = url.searchParams.get('resumeToken');

    if (!sessionId || !resumeToken) {
      return new Response('Session ID and resume token required', { status: 400 });
    }

    const session = this.sessions.get(sessionId);
    if (!session || session.resumeToken !== resumeToken) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid session or resume token'
      }), { status: 401 });
    }

    return new Response(JSON.stringify({
      success: true,
      sessionInfo: {
        id: session.id,
        status: session.status,
        totalSize: session.totalSize,
        downloadedChunks: session.downloadedChunks.size,
        totalChunks: session.totalChunks,
        progress: (session.downloadedChunks.size / session.totalChunks) * 100,
        failedChunks: session.failedChunks.size,
        lastActivity: session.lastActivity,
        expiresAt: session.expiresAt
      }
    }));
  }

  private async verifyChunks(request: Request): Promise<Response> {
    const { sessionId, resumeToken } = await request.json();
    const session = this.sessions.get(sessionId);

    if (!session || session.resumeToken !== resumeToken) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid session or resume token'
      }), { status: 401 });
    }

    const corruptedChunks: number[] = [];

    // Перевіряємо цілісність завантажених чанків
    for (const chunkId of session.downloadedChunks) {
      const storedChecksum = session.checksums.get(chunkId);
      if (storedChecksum) {
        const chunkData = await this.getStoredChunk(session.id, chunkId);
        if (chunkData) {
          const actualChecksum = await this.calculateChecksum(chunkData);
          if (actualChecksum !== storedChecksum) {
            corruptedChunks.push(chunkId);
            session.downloadedChunks.delete(chunkId);
            session.failedChunks.add(chunkId);
          }
        }
      }
    }

    await this.persistSessions();

    return new Response(JSON.stringify({
      success: true,
      corruptedChunks,
      validChunks: session.downloadedChunks.size,
      needsRedownload: corruptedChunks.length > 0
    }));
  }

  private async assembleFile(request: Request): Promise<Response> {
    const { sessionId, resumeToken } = await request.json();
    const session = this.sessions.get(sessionId);

    if (!session || session.resumeToken !== resumeToken) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid session or resume token'
      }), { status: 401 });
    }

    if (session.downloadedChunks.size !== session.totalChunks) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Not all chunks downloaded'
      }), { status: 400 });
    }

    // Збираємо файл з чанків
    const assembledFile = new Uint8Array(session.totalSize);
    let offset = 0;

    for (let chunkId = 0; chunkId < session.totalChunks; chunkId++) {
      const chunkData = await this.getStoredChunk(session.id, chunkId);
      if (chunkData) {
        assembledFile.set(new Uint8Array(chunkData), offset);
        offset += chunkData.byteLength;
      }
    }

    // Зберігаємо зібраний файл
    const assembledFileKey = `assembled/${session.id}`;
    await this.env.R2_BUCKET.put(assembledFileKey, assembledFile);

    // Генеруємо підписане URL
    const downloadUrl = await this.generateSignedUrl(assembledFileKey);

    session.downloadUrl = downloadUrl;
    session.status = 'completed';
    await this.persistSessions();

    // Очищуємо тимчасові чанки
    await this.cleanupChunks(session.id);

    return new Response(JSON.stringify({
      success: true,
      downloadUrl,
      fileSize: session.totalSize
    }));
  }

  // Допоміжні методи
  private async checkModelAccess(modelId: string, userId: string): Promise<boolean> {
    // Реалізація перевірки доступу (аналогічно до DownloadManager)
    return true; // Спрощено для прикладу
  }

  private async getFileInfo(modelId: string): Promise<{ size: number; type: string } | null> {
    try {
      const fileObject = await this.env.R2_BUCKET.head(modelId);
      return fileObject ? {
        size: fileObject.size,
        type: fileObject.httpMetadata?.contentType || 'application/octet-stream'
      } : null;
    } catch (error) {
      return null;
    }
  }

  private async calculateChecksum(data: ArrayBuffer): Promise<string> {
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  private async storeChunk(sessionId: string, chunkId: number, data: ArrayBuffer): Promise<void> {
    const chunkKey = `chunks/${sessionId}/${chunkId}`;
    await this.env.R2_BUCKET.put(chunkKey, data);
  }

  private async getStoredChunk(sessionId: string, chunkId: number): Promise<ArrayBuffer | null> {
    try {
      const chunkKey = `chunks/${sessionId}/${chunkId}`;
      const chunkObject = await this.env.R2_BUCKET.get(chunkKey);
      return chunkObject ? await chunkObject.arrayBuffer() : null;
    } catch (error) {
      return null;
    }
  }

  private async cleanupChunks(sessionId: string): Promise<void> {
    // Видаляємо всі тимчасові чанки для сесії
    const chunkPrefix = `chunks/${sessionId}/`;
    const objects = await this.env.R2_BUCKET.list({ prefix: chunkPrefix });
    
    for (const object of objects.objects) {
      await this.env.R2_BUCKET.delete(object.key);
    }
  }

  private async generateSignedUrl(objectKey: string): Promise<string> {
    // Генеруємо підписане URL для завантаження (спрощено)
    return `https://your-r2-domain.com/${objectKey}?expires=${Date.now() + 3600000}`;
  }

  private async getAssembledFileUrl(session: ResumableDownloadSession): Promise<string | null> {
    if (session.downloadUrl) {
      return session.downloadUrl;
    }
    
    const assembledFileKey = `assembled/${session.id}`;
    const exists = await this.env.R2_BUCKET.head(assembledFileKey);
    
    return exists ? await this.generateSignedUrl(assembledFileKey) : null;
  }

  private async cleanupExpiredSessions(): Promise<void> {
    const now = Date.now();
    
    for (const [sessionId, session] of this.sessions.entries()) {
      if (now > session.expiresAt) {
        await this.cleanupChunks(sessionId);
        this.sessions.delete(sessionId);
      }
    }
    
    await this.persistSessions();
  }

  private async persistSessions(): Promise<void> {
    const sessionsArray = Array.from(this.sessions.entries()).map(([sessionId, session]) => [
      sessionId,
      {
        ...session,
        downloadedChunks: Array.from(session.downloadedChunks),
        failedChunks: Array.from(session.failedChunks),
        checksums: Array.from(session.checksums.entries())
      }
    ]);
    
    await this.state.storage.put('sessions', sessionsArray);
  }
}
