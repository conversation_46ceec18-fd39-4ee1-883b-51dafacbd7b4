'use client';

import { useState } from 'react';

interface ModelViewerProps {
  sceneUrl: string;
  height?: string;
  className?: string;
}

export default function ModelViewer({ 
  sceneUrl = "https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode", 
  height = '400px',
  className = ''
}: ModelViewerProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleLoad = () => {
    setIsLoading(false);
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  return (
    <div className={`relative w-full overflow-hidden rounded-lg ${className}`} style={{ height }}>
      <iframe
        src={sceneUrl}
        width="100%"
        height="100%"
        style={{ 
          border: 'none',
          display: hasError ? 'none' : 'block'
        }}
        allowFullScreen
        title="3D Model Viewer"
        onLoad={handleLoad}
        onError={handleError}
      />
      
      {/* Показуємо індикатор завантаження */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      )}
      
      {/* Показуємо повідомлення про помилку */}
      {hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <p className="text-lg font-medium">3D сцена недоступна. Спробуйте пізніше.</p>
        </div>
      )}
    </div>
  );
}
