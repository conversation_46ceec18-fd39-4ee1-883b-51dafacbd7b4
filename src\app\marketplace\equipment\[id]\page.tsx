'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import { useCart } from '@/context/cart-context';
import { ArrowLeft, ShieldCheck, Star, Truck } from 'lucide-react';
import Link from 'next/link';
import React, { useState } from 'react';

import { EQUIPMENT_DATA, RECOMMENDED_EQUIPMENT } from '@/app/constants/equipment-data';

// Використовуємо дані з константного файлу
const MOCK_EQUIPMENT = EQUIPMENT_DATA;

export default function EquipmentDetailPage(props: {
  params: Promise<{ id: string }>
}) {
  // Access params using React.use() as recommended by Next.js
  const resolvedParams = React.use(props.params);
  const id = resolvedParams.id;
  const [activeImage, setActiveImage] = useState(0);
  const [activeTab, setActiveTab] = useState('overview');

  // Знаходимо обладнання за ID
  const equipment = MOCK_EQUIPMENT.find(item => item.id === id) || MOCK_EQUIPMENT[0];

  const { addToCart } = useCart();
  const { toast } = useToast();

  // Додавання товару до кошика
  const handleAddToCart = () => {
    addToCart({
      id: equipment.id,
      title: equipment.title,
      price: equipment.price,
      thumbnail: equipment.thumbnail,
    });

    toast({
      title: "Додано до кошика",
      description: `${equipment.title} додано до вашого кошика.`,
    });
  };

  return (
    <main className="container mx-auto px-4 py-8">
      {/* Навігація */}
      <div className="mb-6">
        <Link href="/marketplace/equipment" className="flex items-center text-muted-foreground hover:text-foreground transition-colors">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Назад до списку обладнання
        </Link>
      </div>

      {/* Основна інформація про обладнання */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Галерея зображень */}
        <div className="lg:col-span-2 space-y-4">
          <div className="relative aspect-video bg-muted rounded-lg overflow-hidden">
            <img
              src={equipment.images[activeImage] || equipment.thumbnail}
              alt={equipment.title}
              className="w-full h-full object-contain"
            />
          </div>
          <div className="flex space-x-2 overflow-x-auto pb-2">
            {equipment.images.map((image, index) => (
              <button
                key={index}
                className={`relative w-20 h-20 rounded-md overflow-hidden flex-shrink-0 ${
                  activeImage === index ? 'ring-2 ring-primary' : ''
                }`}
                onClick={() => setActiveImage(index)}
              >
                <img src={image} alt={`${equipment.title} view ${index + 1}`} className="w-full h-full object-cover" />
              </button>
            ))}
          </div>
        </div>

        {/* Інформація про товар */}
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold">{equipment.title}</h1>
            <div className="flex items-center mt-2">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-5 w-5 ${
                      i < Math.floor(equipment.rating)
                        ? 'text-yellow-400 fill-yellow-400'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
              <span className="ml-2 text-muted-foreground">
                {equipment.rating} ({equipment.reviewCount} відгуків)
              </span>
            </div>
            <div className="flex items-center mt-2">
              <Avatar className="h-6 w-6 mr-2">
                <AvatarImage src={equipment.manufacturerLogo} alt={equipment.manufacturer} />
                <AvatarFallback>{equipment.manufacturer.substring(0, 2)}</AvatarFallback>
              </Avatar>
              <span className="text-muted-foreground">{equipment.manufacturer}</span>
            </div>
          </div>

          <div className="text-3xl font-bold">${equipment.price}</div>

          <div className="space-y-4">
            <div className="flex items-center text-sm">
              <ShieldCheck className="h-5 w-5 mr-2 text-green-500" />
              <span>Гарантія {equipment.warranty}</span>
            </div>
            <div className="flex items-center text-sm">
              <Truck className="h-5 w-5 mr-2 text-blue-500" />
              <span>{equipment.shipping}</span>
            </div>
            <Badge className={equipment.inStock ? 'bg-green-500' : 'bg-red-500'}>
              {equipment.inStock ? 'В наявності' : 'Немає в наявності'}
            </Badge>
          </div>

          <Button className="w-full" size="lg" onClick={handleAddToCart} disabled={!equipment.inStock}>
            Додати до кошика
          </Button>

          <div className="flex flex-wrap gap-2 mt-4">
            {equipment.tags.map((tag) => (
              <Badge key={tag} variant="outline">
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      </div>

      {/* Вкладки з детальною інформацією */}
      <div className="mt-12">
        <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="w-full border-b justify-start">
            <TabsTrigger value="overview">Огляд</TabsTrigger>
            <TabsTrigger value="specifications">Характеристики</TabsTrigger>
            <TabsTrigger value="reviews">Відгуки ({equipment.reviewCount})</TabsTrigger>
          </TabsList>
          <TabsContent value="overview" className="py-6">
            <div className="prose max-w-none dark:prose-invert">
              <h2 className="text-2xl font-bold mb-4">Опис</h2>
              <p className="mb-6">{equipment.description}</p>

              <h3 className="text-xl font-semibold mb-3">Особливості</h3>
              <ul className="space-y-2 mb-6">
                {equipment.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          </TabsContent>
          <TabsContent value="specifications" className="py-6">
            <div className="prose max-w-none dark:prose-invert">
              <h2 className="text-2xl font-bold mb-4">Технічні характеристики</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(equipment.specifications).map(([key, value]) => (
                  <div key={key} className="border-b pb-2">
                    <span className="font-medium">{key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}: </span>
                    <span className="text-muted-foreground">{value}</span>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>
          <TabsContent value="reviews" className="py-6">
            <div className="space-y-6">
              <h2 className="text-2xl font-bold mb-4">Відгуки покупців</h2>

              <div className="flex items-center mb-6">
                <div className="flex items-center mr-4">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-6 w-6 ${
                        i < Math.floor(equipment.rating)
                          ? 'text-yellow-400 fill-yellow-400'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <span className="text-xl font-semibold">{equipment.rating} з 5</span>
                <span className="ml-2 text-muted-foreground">
                  на основі {equipment.reviewCount} відгуків
                </span>
              </div>

              <div className="space-y-4">
                <p className="text-muted-foreground">Відгуки від покупців будуть доступні після перших продажів.</p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Рекомендовані товари */}
      <div className="mt-12">
        <h2 className="text-2xl font-bold mb-6">Вам також може сподобатися</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {RECOMMENDED_EQUIPMENT.map((recEquipment) => (
            <Link href={`/marketplace/equipment/${recEquipment.id}`} key={recEquipment.id}>
              <Card className="overflow-hidden hover:shadow-md transition-shadow h-full">
                <div className="aspect-square relative">
                  <img
                    src={recEquipment.thumbnail}
                    alt={recEquipment.title}
                    className="w-full h-full object-cover"
                  />
                  <Badge className="absolute top-2 left-2 bg-blue-500">${recEquipment.price}</Badge>
                </div>
                <div className="p-4">
                  <h3 className="font-semibold text-lg mb-1">{recEquipment.title}</h3>
                  <p className="text-muted-foreground text-sm">
                    {recEquipment.manufacturer}
                  </p>
                </div>
              </Card>
            </Link>
          ))}
        </div>
      </div>
    </main>
  );
}
