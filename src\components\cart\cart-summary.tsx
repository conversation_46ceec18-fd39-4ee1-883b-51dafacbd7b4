'use client';

import React from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useCart } from '@/context/cart-context';
import { formatPrice } from '@/lib/utils';

export const CartSummary: React.FC = () => {
  const { totalItems, totalPrice } = useCart();
  
  // Calculate tax (e.g., 10%)
  const taxRate = 0.1;
  const taxAmount = totalPrice * taxRate;
  
  // Calculate total with tax
  const totalWithTax = totalPrice + taxAmount;

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Order Summary</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex justify-between">
          <span className="text-muted-foreground">Subtotal ({totalItems} items)</span>
          <span>{formatPrice(totalPrice)}</span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-muted-foreground">Tax (10%)</span>
          <span>{formatPrice(taxAmount)}</span>
        </div>
        
        <Separator />
        
        <div className="flex justify-between font-medium">
          <span>Total</span>
          <span>{formatPrice(totalWithTax)}</span>
        </div>
      </CardContent>
      <CardFooter>
        <Button className="w-full" size="lg" asChild>
          <Link href="/checkout">
            Proceed to Checkout
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
};
