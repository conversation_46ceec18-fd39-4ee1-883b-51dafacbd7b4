import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Форматує число як ціну з символом валюти
 * @param price - Ціна для форматування
 * @param currency - Валюта (за замовчуванням USD)
 * @returns Відформатована ціна з символом валюти
 */
export function formatPrice(price: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency,
    minimumFractionDigits: price % 1 === 0 ? 0 : 2,
    maximumFractionDigits: 2,
  }).format(price);
}
