'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Users } from 'lucide-react';

interface Collection {
  id: number;
  title: string;
  image: string;
  modelCount: number;
  followers: number;
  slug?: string;
}

interface CollectionsSectionProps {
  collections: Collection[];
}

const CollectionsSection: React.FC<CollectionsSectionProps> = ({ collections }) => {
  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-end mb-10">
          <div>
            <Badge className="mb-2">Колекції</Badge>
            <h2 className="text-3xl font-bold mb-2">Тематичні колекції</h2>
            <p className="text-muted-foreground max-w-2xl">
              Перегляньте наші куровані колекції 3D-моделей, об'єднані за темами, стилями та призначенням.
            </p>
          </div>
          <Button variant="outline" asChild>
            <Link href="/marketplace">Перейти до маркетплейсу</Link>
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {collections.map((collection) => (
            <Link
              key={collection.id}
              href={`/marketplace${collection.slug ? `?collection=${encodeURIComponent(collection.slug)}` : ''}`}
              className="group block"
            >
              <div className="relative overflow-hidden rounded-lg aspect-[4/3]">
                <Image
                  src={collection.image}
                  alt={collection.title}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex flex-col justify-end p-6">
                  <h3 className="text-white text-xl font-semibold mb-2">{collection.title}</h3>
                  <div className="flex items-center gap-4">
                    <span className="text-white/90 text-sm">{collection.modelCount} моделей</span>
                    <div className="flex items-center gap-1 text-white/90 text-sm">
                      <Users className="h-4 w-4" />
                      <span>{collection.followers}</span>
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
};

export default CollectionsSection;
