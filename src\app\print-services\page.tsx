'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { 
  Printer, 
  Package, 
  Truck, 
  Clock, 
  CreditCard, 
  CheckCircle, 
  ChevronRight,
  FileText,
  Settings,
  Users
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// Тимчасові дані для демонстрації
const PRINT_SERVICES = [
  {
    id: '1',
    title: 'Стандартний друк',
    description: 'Базовий 3D-друк з використанням PLA або ABS пластику',
    price: 'від 200 грн',
    turnaround: '3-5 днів',
    materials: ['PLA', 'ABS', 'PETG'],
    image: 'https://placehold.co/600x400/png?text=Standard+Printing',
    features: [
      'Роздільна здатність 0.2мм',
      'Доступні різні кольори',
      'Підходить для більшості моделей'
    ]
  },
  {
    id: '2',
    title: 'Преміум друк',
    description: 'Високоякісний друк з підвищеною деталізацією',
    price: 'від 350 грн',
    turnaround: '5-7 днів',
    materials: ['PLA+', 'PETG', 'TPU', 'Nylon'],
    image: 'https://placehold.co/600x400/png?text=Premium+Printing',
    features: [
      'Роздільна здатність 0.1мм',
      'Покращена якість поверхні',
      'Ідеально для деталізованих моделей'
    ]
  },
  {
    id: '3',
    title: 'Промисловий друк',
    description: 'Професійний друк для комерційних проектів',
    price: 'від 500 грн',
    turnaround: '7-10 днів',
    materials: ['Resin', 'Carbon Fiber', 'Metal-infused'],
    image: 'https://placehold.co/600x400/png?text=Industrial+Printing',
    features: [
      'Промислова якість',
      'Висока міцність',
      'Підходить для функціональних деталей'
    ]
  }
];

const MATERIALS = [
  {
    name: 'PLA',
    description: 'Найпопулярніший матеріал для 3D-друку, біорозкладний та простий у використанні',
    properties: 'Середня міцність, низька температура плавлення',
    useCases: 'Декоративні моделі, прототипи, іграшки',
    price: 'від 150 грн за 100г'
  },
  {
    name: 'ABS',
    description: 'Міцний та довговічний матеріал з хорошою термостійкістю',
    properties: 'Висока міцність, стійкість до ударів, висока температура плавлення',
    useCases: 'Функціональні деталі, автомобільні компоненти, побутові предмети',
    price: 'від 180 грн за 100г'
  },
  {
    name: 'PETG',
    description: 'Поєднує простоту друку PLA з міцністю ABS',
    properties: 'Хороша міцність, гнучкість, водостійкість',
    useCases: 'Контейнери для їжі, механічні деталі, водонепроникні вироби',
    price: 'від 200 грн за 100г'
  },
  {
    name: 'TPU',
    description: 'Гнучкий матеріал для еластичних виробів',
    properties: 'Висока еластичність, стійкість до зносу',
    useCases: 'Чохли для телефонів, ущільнювачі, протектори',
    price: 'від 250 грн за 100г'
  },
  {
    name: 'Resin',
    description: 'Фотополімерна смола для високоточного друку',
    properties: 'Надзвичайно висока деталізація, гладка поверхня',
    useCases: 'Ювелірні вироби, стоматологічні моделі, мініатюри',
    price: 'від 400 грн за 100г'
  }
];

export default function PrintServicesPage() {
  const [activeTab, setActiveTab] = useState('services');
  const [selectedMaterial, setSelectedMaterial] = useState('PLA');
  const [selectedColor, setSelectedColor] = useState('white');
  const [selectedQuality, setSelectedQuality] = useState('standard');

  return (
    <main className="bg-background min-h-screen">
      <div className="container mx-auto px-4 py-8">
        {/* Заголовок сторінки */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">Послуги 3D-друку</h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Перетворіть ваші 3D-моделі в реальні об'єкти з нашими професійними послугами друку
          </p>
        </div>

        {/* Вкладки */}
        <Tabs defaultValue="services" value={activeTab} onValueChange={setActiveTab} className="mb-12">
          <TabsList className="grid grid-cols-4 max-w-2xl mx-auto">
            <TabsTrigger value="services">Послуги</TabsTrigger>
            <TabsTrigger value="materials">Матеріали</TabsTrigger>
            <TabsTrigger value="order">Замовити друк</TabsTrigger>
            <TabsTrigger value="faq">FAQ</TabsTrigger>
          </TabsList>

          {/* Вкладка "Послуги" */}
          <TabsContent value="services" className="mt-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {PRINT_SERVICES.map((service) => (
                <Card key={service.id} className="overflow-hidden">
                  <div className="aspect-video w-full overflow-hidden">
                    <img 
                      src={service.image} 
                      alt={service.title} 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <CardHeader>
                    <CardTitle>{service.title}</CardTitle>
                    <CardDescription>{service.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Ціна:</span>
                        <span className="font-medium">{service.price}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Термін виконання:</span>
                        <span className="font-medium">{service.turnaround}</span>
                      </div>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {service.materials.map((material) => (
                          <Badge key={material} variant="secondary">{material}</Badge>
                        ))}
                      </div>
                      <ul className="space-y-2 mt-4">
                        {service.features.map((feature, index) => (
                          <li key={index} className="flex items-start">
                            <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button className="w-full" onClick={() => setActiveTab('order')}>
                      Замовити
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>

            {/* Процес друку */}
            <div className="mt-16">
              <h2 className="text-3xl font-bold text-center mb-8">Як працює наш процес друку</h2>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div className="text-center">
                  <div className="bg-primary/10 rounded-full p-6 w-24 h-24 flex items-center justify-center mx-auto mb-4">
                    <FileText className="h-10 w-10 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">1. Замовлення</h3>
                  <p className="text-muted-foreground">Завантажте вашу модель та виберіть параметри друку</p>
                </div>
                <div className="text-center">
                  <div className="bg-primary/10 rounded-full p-6 w-24 h-24 flex items-center justify-center mx-auto mb-4">
                    <Settings className="h-10 w-10 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">2. Підготовка</h3>
                  <p className="text-muted-foreground">Наші спеціалісти підготують вашу модель до друку</p>
                </div>
                <div className="text-center">
                  <div className="bg-primary/10 rounded-full p-6 w-24 h-24 flex items-center justify-center mx-auto mb-4">
                    <Printer className="h-10 w-10 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">3. Друк</h3>
                  <p className="text-muted-foreground">Виготовлення вашої моделі на професійному обладнанні</p>
                </div>
                <div className="text-center">
                  <div className="bg-primary/10 rounded-full p-6 w-24 h-24 flex items-center justify-center mx-auto mb-4">
                    <Truck className="h-10 w-10 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">4. Доставка</h3>
                  <p className="text-muted-foreground">Відправка готового виробу на вказану адресу</p>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Вкладка "Матеріали" */}
          <TabsContent value="materials" className="mt-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {MATERIALS.map((material) => (
                <Card key={material.name}>
                  <CardHeader>
                    <CardTitle>{material.name}</CardTitle>
                    <CardDescription>{material.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-semibold mb-1">Властивості:</h4>
                        <p className="text-muted-foreground">{material.properties}</p>
                      </div>
                      <div>
                        <h4 className="font-semibold mb-1">Застосування:</h4>
                        <p className="text-muted-foreground">{material.useCases}</p>
                      </div>
                      <div>
                        <h4 className="font-semibold mb-1">Ціна:</h4>
                        <p className="text-muted-foreground">{material.price}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Вкладка "Замовити друк" */}
          <TabsContent value="order" className="mt-8">
            <Card className="max-w-3xl mx-auto">
              <CardHeader>
                <CardTitle>Замовлення 3D-друку</CardTitle>
                <CardDescription>Заповніть форму нижче, щоб замовити друк вашої моделі</CardDescription>
              </CardHeader>
              <CardContent>
                <form className="space-y-6">
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">Ім'я</Label>
                        <Input id="name" placeholder="Введіть ваше ім'я" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">Email</Label>
                        <Input id="email" type="email" placeholder="Введіть ваш email" />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="model">3D-модель</Label>
                      <div className="flex items-center gap-2">
                        <Input id="model" type="file" accept=".stl,.obj,.3mf" />
                        <Button variant="outline" type="button">
                          Вибрати з бібліотеки
                        </Button>
                      </div>
                      <p className="text-xs text-muted-foreground">Підтримувані формати: STL, OBJ, 3MF. Максимальний розмір: 50MB</p>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="material">Матеріал</Label>
                        <Select defaultValue={selectedMaterial} onValueChange={setSelectedMaterial}>
                          <SelectTrigger>
                            <SelectValue placeholder="Виберіть матеріал" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="PLA">PLA</SelectItem>
                            <SelectItem value="ABS">ABS</SelectItem>
                            <SelectItem value="PETG">PETG</SelectItem>
                            <SelectItem value="TPU">TPU</SelectItem>
                            <SelectItem value="Resin">Resin</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="color">Колір</Label>
                        <Select defaultValue={selectedColor} onValueChange={setSelectedColor}>
                          <SelectTrigger>
                            <SelectValue placeholder="Виберіть колір" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="white">Білий</SelectItem>
                            <SelectItem value="black">Чорний</SelectItem>
                            <SelectItem value="gray">Сірий</SelectItem>
                            <SelectItem value="red">Червоний</SelectItem>
                            <SelectItem value="blue">Синій</SelectItem>
                            <SelectItem value="green">Зелений</SelectItem>
                            <SelectItem value="yellow">Жовтий</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="quality">Якість друку</Label>
                        <Select defaultValue={selectedQuality} onValueChange={setSelectedQuality}>
                          <SelectTrigger>
                            <SelectValue placeholder="Виберіть якість" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="draft">Чорновий (0.3мм)</SelectItem>
                            <SelectItem value="standard">Стандартний (0.2мм)</SelectItem>
                            <SelectItem value="high">Високий (0.1мм)</SelectItem>
                            <SelectItem value="ultra">Ультра (0.05мм)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="notes">Додаткові примітки</Label>
                      <Textarea id="notes" placeholder="Вкажіть будь-які особливі вимоги або побажання" />
                    </div>
                  </div>
                </form>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={() => setActiveTab('services')}>
                  Назад до послуг
                </Button>
                <Button>
                  Розрахувати вартість
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          {/* Вкладка "FAQ" */}
          <TabsContent value="faq" className="mt-8">
            <div className="max-w-3xl mx-auto space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Часті запитання</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Які формати файлів ви приймаєте?</h3>
                    <p className="text-muted-foreground">Ми приймаємо найпоширеніші формати 3D-моделей: STL, OBJ, 3MF. Якщо у вас є модель в іншому форматі, зв'яжіться з нами для уточнення.</p>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Скільки часу займає друк?</h3>
                    <p className="text-muted-foreground">Час друку залежить від розміру моделі, обраної якості та завантаженості обладнання. Зазвичай це займає від 3 до 10 робочих днів.</p>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Чи можна замовити кілька копій однієї моделі?</h3>
                    <p className="text-muted-foreground">Так, ви можете замовити будь-яку кількість копій. Вкажіть це в примітках до замовлення.</p>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Як розрахувати вартість друку?</h3>
                    <p className="text-muted-foreground">Вартість залежить від об'єму матеріалу, часу друку та складності моделі. Ви можете отримати попередній розрахунок, завантаживши модель через форму замовлення.</p>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Які способи оплати ви приймаєте?</h3>
                    <p className="text-muted-foreground">Ми приймаємо оплату банківськими картами, через електронні платіжні системи та банківським переказом.</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* Партнери та обладнання */}
        <div className="mt-16">
          <h2 className="text-3xl font-bold text-center mb-8">Наше обладнання</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>FDM принтери</CardTitle>
                <CardDescription>Для друку пластиком</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    <span>Prusa i3 MK3S+</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    <span>Creality Ender 5 Pro</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    <span>Ultimaker S5</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>SLA/MSLA принтери</CardTitle>
                <CardDescription>Для високоточного друку смолою</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    <span>Formlabs Form 3</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    <span>Elegoo Saturn 2</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    <span>Anycubic Photon M3 Premium</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Промислові принтери</CardTitle>
                <CardDescription>Для комерційних проектів</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    <span>Stratasys F370</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    <span>Markforged Mark Two</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    <span>Zortrax M300 Dual</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Заклик до дії */}
        <div className="mt-16 bg-primary/10 rounded-lg p-8 text-center">
          <h2 className="text-3xl font-bold mb-4">Готові замовити 3D-друк?</h2>
          <p className="text-xl text-muted-foreground mb-6 max-w-2xl mx-auto">
            Перетворіть ваші ідеї в реальність з нашими професійними послугами 3D-друку
          </p>
          <Button size="lg" onClick={() => setActiveTab('order')}>
            Замовити зараз
          </Button>
        </div>
      </div>
    </main>
  );
}
