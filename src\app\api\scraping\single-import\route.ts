import { NextRequest, NextResponse } from 'next/server';

/**
 * POST /api/scraping/single-import
 * Імпортує одну модель з URL
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { url, options = {} } = body;

    // Валідація URL
    if (!url || typeof url !== 'string') {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: 'URL is required and must be a string',
            code: 'INVALID_URL'
          }
        },
        { status: 400 }
      );
    }

    let parsedUrl: URL;
    try {
      parsedUrl = new URL(url);
    } catch {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: 'Invalid URL format',
            code: 'MALFORMED_URL'
          }
        },
        { status: 400 }
      );
    }

    // Перевірка підтримуваних платформ
    const supportedPlatforms = [
      'thingiverse.com',
      'myminifactory.com',
      'printables.com',
      'thangs.com',
      'makerworld.com'
    ];

    const isSupported = supportedPlatforms.some(platform => 
      parsedUrl.hostname.includes(platform)
    );

    if (!isSupported) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: `Unsupported platform. Supported platforms: ${supportedPlatforms.join(', ')}`,
            code: 'UNSUPPORTED_PLATFORM',
            details: { supportedPlatforms }
          }
        },
        { status: 400 }
      );
    }

    // Симуляція імпорту для тестування
    await new Promise(resolve => setTimeout(resolve, 1000)); // Затримка 1 секунда

    // Мок-дані для тестування
    const mockModel = {
      id: `mock_${Date.now()}`,
      name: 'Test Model from ' + (url.includes('thingiverse') ? 'Thingiverse' : 'Other Platform'),
      description: 'This is a test model imported from ' + url,
      thumbnail_url: 'https://via.placeholder.com/300x300',
      category: 'Test',
      tags: ['test', 'mock', 'import'],
      author_name: 'Test Author',
      download_count: 100,
      like_count: 50,
      is_free: true,
      price: 0,
      file_formats: ['STL', 'OBJ'],
      source_platform: url.includes('thingiverse') ? 'thingiverse' : 'unknown',
      source_url: url
    };

    return NextResponse.json({
      success: true,
      data: {
        model: mockModel,
        platform: mockModel.source_platform,
        importedAt: new Date().toISOString(),
        message: 'Mock import successful - real scraping will be implemented'
      }
    });

  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: {
          message: error instanceof Error ? error.message : 'Unknown error',
          code: 'IMPORT_ERROR'
        }
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/scraping/single-import/validate?url=xxx
 * Валідує URL перед імпортом
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const url = searchParams.get('url');

    if (!url) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: 'URL parameter is required',
            code: 'MISSING_URL'
          }
        },
        { status: 400 }
      );
    }

    // Валідація URL
    let parsedUrl: URL;
    try {
      parsedUrl = new URL(url);
    } catch {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: 'Invalid URL format',
            code: 'MALFORMED_URL'
          }
        },
        { status: 400 }
      );
    }

    // Визначення платформи
    let platform = 'unknown';
    let supported = false;

    if (parsedUrl.hostname.includes('thingiverse.com')) {
      platform = 'thingiverse';
      supported = parsedUrl.pathname.includes('/thing:');
    } else if (parsedUrl.hostname.includes('myminifactory.com')) {
      platform = 'myminifactory';
      supported = parsedUrl.pathname.includes('/object/3d-print/');
    } else if (parsedUrl.hostname.includes('printables.com')) {
      platform = 'printables';
      supported = parsedUrl.pathname.includes('/model/');
    } else if (parsedUrl.hostname.includes('thangs.com')) {
      platform = 'thangs';
      supported = parsedUrl.pathname.includes('/designer/') || parsedUrl.pathname.includes('/m/');
    } else if (parsedUrl.hostname.includes('makerworld.com')) {
      platform = 'makerworld';
      supported = parsedUrl.pathname.includes('/models/');
    }

    // Додаткові перевірки для специфічних платформ
    let warnings: string[] = [];
    let estimatedTime = 5; // секунди

    if (platform === 'thingiverse' && !parsedUrl.pathname.match(/\/thing:\d+/)) {
      warnings.push('URL should contain a thing ID (e.g., /thing:123456)');
      supported = false;
    }

    if (platform === 'myminifactory' && !parsedUrl.pathname.includes('-')) {
      warnings.push('URL should contain a model slug with ID');
    }

    // Оцінка складності імпорту
    if (platform === 'thingiverse') {
      estimatedTime = 8; // Thingiverse може бути повільнішим
    } else if (platform === 'myminifactory') {
      estimatedTime = 6; // MyMiniFactory зазвичай швидший
    }

    return NextResponse.json({
      success: true,
      data: {
        url,
        platform,
        supported,
        warnings,
        estimatedTime,
        canImport: supported && warnings.length === 0,
        platformInfo: {
          name: platform,
          displayName: platform.charAt(0).toUpperCase() + platform.slice(1),
          features: {
            hasFiles: true,
            hasImages: true,
            hasStats: true,
            hasLicense: platform !== 'unknown',
            hasPricing: platform === 'myminifactory'
          }
        }
      }
    });

  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: {
          message: error instanceof Error ? error.message : 'Unknown error',
          code: 'VALIDATION_ERROR'
        }
      },
      { status: 500 }
    );
  }
}
