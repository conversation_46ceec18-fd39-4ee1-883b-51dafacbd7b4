/**
 * Batch import API endpoint
 * Supports importing multiple models simultaneously
 */

import { NextRequest, NextResponse } from 'next/server';
import { printablesScraper } from '@/lib/api/printables';
import { makerWorldScraper } from '@/lib/api/makerworld';
import { thangsScraper } from '@/lib/api/thangs';
import { platformRateLimiters, RateLimitError } from '@/lib/scraping/rate-limiter';
import { DataNormalizer } from '@/lib/scraping/data-normalizer';
import { ScrapedModel, ModelSource, BatchImportJob } from '@/types/models';

interface BatchImportRequest {
  urls: string[];
  options?: {
    parallel?: number; // Number of concurrent imports (default: 3)
    retryFailed?: boolean;
    includeFiles?: boolean;
    includeImages?: boolean;
    validateLicense?: boolean;
  };
}

interface BatchImportResponse {
  success: boolean;
  data?: {
    batchId: string;
    status: 'processing' | 'completed' | 'failed';
    total: number;
    completed: number;
    failed: number;
    results: Array<{
      url: string;
      status: 'success' | 'failed';
      modelId?: string;
      error?: string;
    }>;
  };
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

// In-memory storage for batch jobs (in production, use Redis or database)
const batchJobs = new Map<string, BatchImportJob>();

/**
 * Detect platform from URL
 */
function detectPlatform(url: string): ModelSource | null {
  if (printablesScraper.validateUrl(url)) return 'printables';
  if (makerWorldScraper.validateUrl(url)) return 'makerworld';
  if (thangsScraper.validateUrl(url)) return 'thangs';
  return null;
}

/**
 * Get appropriate scraper for platform
 */
function getScraper(platform: ModelSource) {
  switch (platform) {
    case 'printables':
      return printablesScraper;
    case 'makerworld':
      return makerWorldScraper;
    case 'thangs':
      return thangsScraper;
    default:
      throw new Error(`Unsupported platform: ${platform}`);
  }
}

/**
 * Process single URL import
 */
async function processSingleImport(url: string): Promise<{ url: string; status: 'success' | 'failed'; modelId?: string; error?: string }> {
  try {
    // Detect platform
    const platform = detectPlatform(url);
    if (!platform) {
      return {
        url,
        status: 'failed',
        error: 'Unsupported platform',
      };
    }

    // Check rate limits
    try {
      await platformRateLimiters.consume(platform, 'batch');
    } catch (error) {
      if (error instanceof RateLimitError) {
        return {
          url,
          status: 'failed',
          error: `Rate limit exceeded for ${platform}`,
        };
      }
      throw error;
    }

    // Get scraper and scrape model
    const scraper = getScraper(platform);
    const scrapedModel = await scraper.scrapeModel(url);

    // Validate and sanitize
    const validation = DataNormalizer.validateScrapedModel(scrapedModel);
    if (!validation.isValid) {
      return {
        url,
        status: 'failed',
        error: `Validation failed: ${validation.errors.join(', ')}`,
      };
    }

    const sanitizedModel = DataNormalizer.sanitizeScrapedModel(scrapedModel);
    const internalModel = DataNormalizer.convertToInternalModel(sanitizedModel);

    // TODO: Save to database
    // await saveModelToDatabase(internalModel);

    return {
      url,
      status: 'success',
      modelId: internalModel.id,
    };

  } catch (error) {
    return {
      url,
      status: 'failed',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Process batch import with concurrency control
 */
async function processBatchImport(batchId: string, urls: string[], options: BatchImportRequest['options'] = {}) {
  const { parallel = 3, retryFailed = false } = options;
  const job = batchJobs.get(batchId);
  if (!job) return;

  // Update job status
  job.status = 'processing';
  batchJobs.set(batchId, job);

  const results: Array<{ url: string; status: 'success' | 'failed'; modelId?: string; error?: string }> = [];
  
  // Process URLs in batches with concurrency control
  for (let i = 0; i < urls.length; i += parallel) {
    const batch = urls.slice(i, i + parallel);
    const batchPromises = batch.map(url => processSingleImport(url));
    
    try {
      const batchResults = await Promise.allSettled(batchPromises);
      
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          results.push({
            url: batch[index],
            status: 'failed',
            error: result.reason?.message || 'Promise rejected',
          });
        }
      });

      // Update job progress
      job.completed = results.filter(r => r.status === 'success').length;
      job.failed = results.filter(r => r.status === 'failed').length;
      job.results = results;
      batchJobs.set(batchId, job);

      // Add delay between batches to respect rate limits
      if (i + parallel < urls.length) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    } catch (error) {
      console.error('Batch processing error:', error);
    }
  }

  // Retry failed imports if requested
  if (retryFailed) {
    const failedUrls = results.filter(r => r.status === 'failed').map(r => r.url);
    if (failedUrls.length > 0) {
      console.log(`Retrying ${failedUrls.length} failed imports...`);
      // TODO: Implement retry logic with exponential backoff
    }
  }

  // Mark job as completed
  job.status = 'completed';
  job.completedAt = new Date().toISOString();
  batchJobs.set(batchId, job);
}

/**
 * POST /api/scraping/batch
 * Start batch import
 */
export async function POST(request: NextRequest): Promise<NextResponse<BatchImportResponse>> {
  try {
    const body: BatchImportRequest = await request.json();
    const { urls, options = {} } = body;

    // Validate input
    if (!urls || !Array.isArray(urls) || urls.length === 0) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'URLs array is required and must not be empty',
        },
      }, { status: 400 });
    }

    if (urls.length > 50) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'TOO_MANY_URLS',
          message: 'Maximum 50 URLs allowed per batch',
        },
      }, { status: 400 });
    }

    // Validate URLs
    const invalidUrls = urls.filter(url => !detectPlatform(url));
    if (invalidUrls.length > 0) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'INVALID_URLS',
          message: 'Some URLs are from unsupported platforms',
          details: { invalidUrls },
        },
      }, { status: 400 });
    }

    // Create batch job
    const batchId = `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const job: BatchImportJob = {
      id: batchId,
      urls,
      status: 'pending',
      total: urls.length,
      completed: 0,
      failed: 0,
      results: [],
      createdAt: new Date().toISOString(),
    };

    batchJobs.set(batchId, job);

    // Start processing asynchronously
    processBatchImport(batchId, urls, options).catch(error => {
      console.error('Batch import error:', error);
      const failedJob = batchJobs.get(batchId);
      if (failedJob) {
        failedJob.status = 'failed';
        batchJobs.set(batchId, failedJob);
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        batchId,
        status: 'processing',
        total: urls.length,
        completed: 0,
        failed: 0,
        results: [],
      },
    });

  } catch (error) {
    console.error('Batch import API error:', error);
    
    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An unexpected error occurred during batch import',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      },
    }, { status: 500 });
  }
}

/**
 * GET /api/scraping/batch?batchId=xxx
 * Get batch import status
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const batchId = searchParams.get('batchId');

    if (!batchId) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'MISSING_BATCH_ID',
          message: 'batchId parameter is required',
        },
      }, { status: 400 });
    }

    const job = batchJobs.get(batchId);
    if (!job) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'BATCH_NOT_FOUND',
          message: 'Batch job not found',
        },
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: {
        batchId: job.id,
        status: job.status,
        total: job.total,
        completed: job.completed,
        failed: job.failed,
        results: job.results,
      },
    });

  } catch (error) {
    console.error('Batch status API error:', error);
    
    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An unexpected error occurred while getting batch status',
      },
    }, { status: 500 });
  }
}
