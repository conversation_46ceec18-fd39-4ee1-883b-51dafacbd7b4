import Link from 'next/link';

export default function AuthNotFound() {
  return (
    <main className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Сторінку не знайдено
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Сторінка, яку ви шукаєте, не існує або була переміщена.
          </p>
        </div>
        
        <div className="mt-8 bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10 text-center">
          <div className="text-red-500 mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">404 - Сторінку не знайдено</h3>
          
          <div className="mt-6 flex justify-center space-x-4">
            <Link href="/auth/signin" className="text-sm font-medium text-blue-600 hover:text-blue-500">
              Увійти
            </Link>
            <span className="text-gray-300">|</span>
            <Link href="/" className="text-sm font-medium text-blue-600 hover:text-blue-500">
              На головну
            </Link>
          </div>
        </div>
      </div>
    </main>
  );
}
