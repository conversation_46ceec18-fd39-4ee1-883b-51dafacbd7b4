# Інтеграція платіжних систем у маркетплейс 3D-моделей

## Зміст

1. [Вступ](#вступ)
2. [Огляд платіжних систем](#огляд-платіжних-систем)
3. [Інтеграція Stripe](#інтеграція-stripe)
4. [Обробка одноразових платежів](#обробка-одноразових-платежів)
5. [Управління підписками](#управління-підписками)
6. [Виплати продавцям](#виплати-продавцям)
7. [Обробка податків](#обробка-податків)
8. [Безпека платежів](#безпека-платежів)
9. [Тестування платежів](#тестування-платежів)
10. [Моніторинг та аналітика](#моніторинг-та-аналітика)
11. [Вирішення проблем](#вирішення-проблем)

## Вступ

Цей документ описує інтеграцію платіжних систем у маркетплейс 3D-моделей. Він охоплює налаштування Stripe для обробки платежів, управління підписками та виплати продавцям.

## Огляд платіжних систем

Маркетплейс 3D-моделей використовує Stripe як основну платіжну систему для обробки наступних типів платежів:

1. **Одноразові платежі** - для покупки окремих 3D-моделей
2. **Підписки** - для регулярних платежів за доступ до колекцій моделей
3. **Виплати продавцям** - для переказу коштів дизайнерам за продані моделі

### Переваги Stripe

- Підтримка багатьох валют та способів оплати
- Вбудована система підписок
- Інструменти для боротьби з шахрайством
- Простий API для інтеграції
- Підтримка Connect для виплат продавцям

## Інтеграція Stripe

### Налаштування облікового запису Stripe

1. Створіть обліковий запис на [stripe.com](https://stripe.com)
2. Отримайте API ключі (публічний та секретний) в панелі керування Stripe
3. Налаштуйте webhook для отримання подій від Stripe

### Встановлення залежностей

```bash
npm install @stripe/stripe-js @stripe/react-stripe-js stripe
# або
yarn add @stripe/stripe-js @stripe/react-stripe-js stripe
```

### Налаштування змінних середовища

Додайте наступні змінні середовища до файлу `.env.local`:

```
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key
STRIPE_SECRET_KEY=sk_test_your_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
```

### Ініціалізація Stripe на сервері

```typescript
// src/lib/stripe.ts
import Stripe from 'stripe';

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16', // Використовуйте останню версію API
});
```

### Ініціалізація Stripe на клієнті

```typescript
// src/lib/stripe-client.ts
import { loadStripe } from '@stripe/stripe-js';

let stripePromise: Promise<any>;

export const getStripe = () => {
  if (!stripePromise) {
    stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);
  }
  return stripePromise;
};
```

## Обробка одноразових платежів

### Створення платіжного наміру

```typescript
// src/app/api/payments/create-intent/route.ts
import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { stripe } from '@/lib/stripe';
import { createClient } from '@/lib/supabase/server';

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  const { modelId } = await request.json();
  
  // Отримання інформації про модель з бази даних
  const supabase = createClient();
  const { data: model, error } = await supabase
    .from('models')
    .select('*')
    .eq('id', modelId)
    .single();
  
  if (error || !model) {
    return NextResponse.json({ error: 'Model not found' }, { status: 404 });
  }
  
  try {
    // Створення платіжного наміру
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(model.price * 100), // Конвертація в центи
      currency: 'usd',
      metadata: {
        modelId: model.id,
        userId: session.user.id,
        modelName: model.title,
      },
      automatic_payment_methods: { enabled: true },
      // Якщо використовуєте Stripe Connect для виплат продавцям
      transfer_data: {
        destination: model.designer_stripe_account_id,
        amount: Math.round(model.price * 100 * 0.8), // 80% продавцю
      },
    });
    
    return NextResponse.json({ clientSecret: paymentIntent.client_secret });
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
```

### Компонент форми оплати

```tsx
// src/components/marketplace/CheckoutForm.tsx
import { useState } from 'react';
import { useStripe, useElements, PaymentElement } from '@stripe/react-stripe-js';

interface CheckoutFormProps {
  modelId: string;
  price: number;
}

export default function CheckoutForm({ modelId, price }: CheckoutFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsLoading(true);
    setErrorMessage(null);

    const { error } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        return_url: `${window.location.origin}/marketplace/purchase/success?model_id=${modelId}`,
      },
    });

    if (error) {
      setErrorMessage(error.message || 'An error occurred');
    }

    setIsLoading(false);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <PaymentElement />
      
      {errorMessage && (
        <div className="text-red-500 text-sm">{errorMessage}</div>
      )}
      
      <button
        type="submit"
        disabled={!stripe || isLoading}
        className="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        {isLoading ? 'Обробка...' : `Оплатити $${price.toFixed(2)}`}
      </button>
    </form>
  );
}
```

### Сторінка оформлення замовлення

```tsx
// src/app/marketplace/purchase/[id]/page.tsx
'use client';

import { useEffect, useState } from 'react';
import { Elements } from '@stripe/react-stripe-js';
import { getStripe } from '@/lib/stripe-client';
import CheckoutForm from '@/components/marketplace/CheckoutForm';

interface PurchasePageProps {
  params: {
    id: string;
  };
}

export default function PurchasePage({ params }: PurchasePageProps) {
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [model, setModel] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPaymentIntent = async () => {
      try {
        // Отримання інформації про модель
        const modelResponse = await fetch(`/api/models/${params.id}`);
        if (!modelResponse.ok) {
          throw new Error('Failed to fetch model');
        }
        const modelData = await modelResponse.json();
        setModel(modelData.data);

        // Створення платіжного наміру
        const response = await fetch('/api/payments/create-intent', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ modelId: params.id }),
        });

        if (!response.ok) {
          throw new Error('Failed to create payment intent');
        }

        const data = await response.json();
        setClientSecret(data.clientSecret);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchPaymentIntent();
  }, [params.id]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error || !model) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error || 'Failed to load model'}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">Оформлення замовлення</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Інформація про замовлення</h2>
          
          <div className="flex items-start gap-4 mb-6">
            <img
              src={model.thumbnail}
              alt={model.title}
              className="w-24 h-24 object-cover rounded"
            />
            <div>
              <h3 className="text-lg font-medium text-gray-800">{model.title}</h3>
              <p className="text-sm text-gray-500">by {model.designer.name}</p>
              <p className="text-blue-500 font-medium mt-1">${model.price.toFixed(2)}</p>
            </div>
          </div>
          
          <div className="border-t border-gray-200 pt-4">
            <div className="flex justify-between mb-2">
              <span className="text-gray-600">Ціна моделі</span>
              <span className="text-gray-800">${model.price.toFixed(2)}</span>
            </div>
            <div className="flex justify-between font-medium">
              <span className="text-gray-800">Загальна сума</span>
              <span className="text-gray-900">${model.price.toFixed(2)}</span>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Оплата</h2>
          
          {clientSecret && (
            <Elements
              stripe={getStripe()}
              options={{
                clientSecret,
                appearance: {
                  theme: 'stripe',
                  variables: {
                    colorPrimary: '#3B82F6',
                  },
                },
              }}
            >
              <CheckoutForm modelId={params.id} price={model.price} />
            </Elements>
          )}
        </div>
      </div>
    </div>
  );
}
```

## Управління підписками

### Створення плану підписки

```typescript
// src/app/api/memberships/create-plan/route.ts
import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { stripe } from '@/lib/stripe';
import { createClient } from '@/lib/supabase/server';

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  const { name, description, price, interval } = await request.json();
  
  try {
    // Створення продукту в Stripe
    const product = await stripe.products.create({
      name,
      description,
      metadata: {
        userId: session.user.id,
      },
    });
    
    // Створення ціни для продукту
    const stripePrice = await stripe.prices.create({
      product: product.id,
      unit_amount: Math.round(price * 100), // Конвертація в центи
      currency: 'usd',
      recurring: {
        interval: interval, // 'month' або 'year'
      },
    });
    
    // Збереження інформації про план підписки в базі даних
    const supabase = createClient();
    const { data, error } = await supabase
      .from('membership_plans')
      .insert({
        user_id: session.user.id,
        name,
        description,
        price,
        interval,
        stripe_product_id: product.id,
        stripe_price_id: stripePrice.id,
      })
      .select()
      .single();
    
    if (error) {
      throw new Error(error.message);
    }
    
    return NextResponse.json({ data }, { status: 201 });
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
```

### Підписка на план

```typescript
// src/app/api/memberships/subscribe/route.ts
import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { stripe } from '@/lib/stripe';
import { createClient } from '@/lib/supabase/server';

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  const { planId } = await request.json();
  
  try {
    // Отримання інформації про план підписки
    const supabase = createClient();
    const { data: plan, error } = await supabase
      .from('membership_plans')
      .select('*')
      .eq('id', planId)
      .single();
    
    if (error || !plan) {
      return NextResponse.json({ error: 'Plan not found' }, { status: 404 });
    }
    
    // Отримання або створення клієнта Stripe
    const { data: user } = await supabase
      .from('users')
      .select('stripe_customer_id')
      .eq('id', session.user.id)
      .single();
    
    let customerId = user?.stripe_customer_id;
    
    if (!customerId) {
      const customer = await stripe.customers.create({
        email: session.user.email,
        metadata: {
          userId: session.user.id,
        },
      });
      
      customerId = customer.id;
      
      await supabase
        .from('users')
        .update({ stripe_customer_id: customerId })
        .eq('id', session.user.id);
    }
    
    // Створення сесії оформлення підписки
    const checkoutSession = await stripe.checkout.sessions.create({
      customer: customerId,
      line_items: [
        {
          price: plan.stripe_price_id,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/memberships/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/memberships`,
      metadata: {
        planId: plan.id,
        userId: session.user.id,
      },
    });
    
    return NextResponse.json({ url: checkoutSession.url });
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
```

### Обробка webhook'ів Stripe

```typescript
// src/app/api/webhooks/stripe/route.ts
import { NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { stripe } from '@/lib/stripe';
import { createClient } from '@/lib/supabase/server';

export async function POST(request: Request) {
  const body = await request.text();
  const signature = headers().get('stripe-signature') as string;
  
  let event;
  
  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
  } catch (error: any) {
    return NextResponse.json({ error: `Webhook Error: ${error.message}` }, { status: 400 });
  }
  
  const supabase = createClient();
  
  // Обробка різних подій Stripe
  switch (event.type) {
    case 'payment_intent.succeeded':
      const paymentIntent = event.data.object;
      
      // Створення запису про покупку
      await supabase
        .from('purchases')
        .insert({
          user_id: paymentIntent.metadata.userId,
          model_id: paymentIntent.metadata.modelId,
          amount: paymentIntent.amount / 100,
          payment_intent_id: paymentIntent.id,
          status: 'completed',
        });
      
      // Надання доступу до моделі
      await supabase
        .from('user_models')
        .insert({
          user_id: paymentIntent.metadata.userId,
          model_id: paymentIntent.metadata.modelId,
        });
      
      break;
      
    case 'checkout.session.completed':
      const session = event.data.object;
      
      if (session.mode === 'subscription') {
        // Створення запису про підписку
        await supabase
          .from('user_memberships')
          .insert({
            user_id: session.metadata.userId,
            plan_id: session.metadata.planId,
            stripe_subscription_id: session.subscription,
            status: 'active',
          });
      }
      
      break;
      
    case 'customer.subscription.updated':
    case 'customer.subscription.deleted':
      const subscription = event.data.object;
      
      // Оновлення статусу підписки
      await supabase
        .from('user_memberships')
        .update({
          status: subscription.status,
          current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
        })
        .eq('stripe_subscription_id', subscription.id);
      
      break;
  }
  
  return NextResponse.json({ received: true });
}
```

## Виплати продавцям

### Налаштування Stripe Connect

1. Активуйте Stripe Connect в панелі керування Stripe
2. Налаштуйте процес онбордингу для продавців
3. Створіть сторінку для підключення облікових записів продавців

### Створення облікового запису Connect

```typescript
// src/app/api/sellers/create-account/route.ts
import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { stripe } from '@/lib/stripe';
import { createClient } from '@/lib/supabase/server';

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
    // Створення облікового запису Connect
    const account = await stripe.accounts.create({
      type: 'express',
      country: 'US', // Змініть на потрібну країну
      email: session.user.email,
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
      metadata: {
        userId: session.user.id,
      },
    });
    
    // Збереження ID облікового запису в базі даних
    const supabase = createClient();
    await supabase
      .from('users')
      .update({ stripe_connect_account_id: account.id })
      .eq('id', session.user.id);
    
    // Створення посилання для онбордингу
    const accountLink = await stripe.accountLinks.create({
      account: account.id,
      refresh_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings`,
      return_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings?onboarding=complete`,
      type: 'account_onboarding',
    });
    
    return NextResponse.json({ url: accountLink.url });
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
```

## Обробка податків

### Налаштування податків у Stripe

1. Активуйте Stripe Tax в панелі керування Stripe
2. Налаштуйте податкові ставки для різних країн та регіонів

### Розрахунок податків

```typescript
// src/app/api/payments/calculate-tax/route.ts
import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { stripe } from '@/lib/stripe';

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  const { amount, country, postalCode } = await request.json();
  
  try {
    // Створення розрахунку податків
    const taxCalculation = await stripe.tax.calculations.create({
      currency: 'usd',
      line_items: [
        {
          amount: Math.round(amount * 100),
          reference: 'digital_product',
          tax_behavior: 'exclusive',
        },
      ],
      customer_details: {
        address: {
          country,
          postal_code: postalCode,
        },
        address_source: 'billing',
      },
    });
    
    return NextResponse.json({
      tax: taxCalculation.tax_breakdown,
      totalAmount: (amount + taxCalculation.tax_amount_exclusive / 100).toFixed(2),
    });
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
```

## Безпека платежів

### Захист від шахрайства

Stripe надає вбудовані інструменти для захисту від шахрайства:

1. Radar - система виявлення шахрайства
2. 3D Secure - додаткова аутентифікація для платежів
3. Перевірка адреси (AVS) та коду безпеки картки (CVC)

### Безпечне зберігання даних

1. Не зберігайте дані платіжних карток на своїх серверах
2. Використовуйте Stripe Elements для безпечного збору платіжної інформації
3. Дотримуйтесь стандартів PCI DSS

## Тестування платежів

### Тестові картки Stripe

| Номер картки | Опис |
|--------------|------|
| 4242 4242 4242 4242 | Успішний платіж |
| 4000 0000 0000 0002 | Відхилений платіж |
| 4000 0025 0000 3155 | Потрібна 3D Secure |
| 4000 0000 0000 9995 | Недостатньо коштів |

### Тестування підписок

Для тестування підписок використовуйте тестові картки та налаштуйте тестові плани з коротшими періодами (наприклад, щоденні замість щомісячних).

## Моніторинг та аналітика

### Панель керування Stripe

Використовуйте панель керування Stripe для моніторингу:

1. Транзакцій та їх статусів
2. Підписок та їх статусів
3. Виплат продавцям
4. Спорів та повернень

### Інтеграція з аналітикою

Відстежуйте ключові метрики:

1. Конверсія (відсоток успішних платежів)
2. Середня вартість замовлення
3. Дохід від підписок
4. Відсоток відмов від підписок

## Вирішення проблем

### Поширені помилки

| Код помилки | Опис | Рішення |
|-------------|------|---------|
| card_declined | Картка відхилена банком | Запропонуйте користувачу використати іншу картку |
| expired_card | Термін дії картки закінчився | Запропонуйте користувачу оновити інформацію про картку |
| incorrect_cvc | Неправильний код безпеки | Запропонуйте користувачу перевірити код безпеки |
| processing_error | Помилка обробки платежу | Запропонуйте користувачу спробувати пізніше |

### Журналювання помилок

Налаштуйте журналювання помилок для швидкого виявлення та вирішення проблем:

```typescript
try {
  // Код для обробки платежу
} catch (error: any) {
  console.error('Payment error:', {
    message: error.message,
    code: error.code,
    type: error.type,
    requestId: error.requestId,
  });
  
  // Відправка помилки в систему моніторингу
  await logError('payment_error', error);
  
  throw error;
}
```
