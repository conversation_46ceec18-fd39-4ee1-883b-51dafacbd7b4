'use client';

import React from 'react';
import Link from 'next/link';
import { ShoppingCart } from 'lucide-react';
import { Button } from '@/components/ui/button';

export const EmptyCart: React.FC = () => {
  return (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <div className="bg-muted rounded-full p-6 mb-6">
        <ShoppingCart className="h-12 w-12 text-muted-foreground" />
      </div>
      <h2 className="text-2xl font-bold mb-2">Your cart is empty</h2>
      <p className="text-muted-foreground mb-6 max-w-md">
        Looks like you haven't added any 3D models to your cart yet. 
        Explore our marketplace to find amazing 3D models.
      </p>
      <Button size="lg" asChild>
        <Link href="/marketplace">
          Browse Marketplace
        </Link>
      </Button>
    </div>
  );
};
