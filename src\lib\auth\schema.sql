-- Схема бази даних для NextAuth з Cloudflare D1
-- Створення таблиць для аутентифікації користувачів

-- Таблиця користувачів
CREATE TABLE IF NOT EXISTS users (
  id TEXT PRIMARY KEY,
  name TEXT,
  email TEXT UNIQUE NOT NULL,
  email_verified TEXT,
  image TEXT,
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now'))
);

-- Індекси для користувачів
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- Таблиця акаунтів (OAuth провайдери)
CREATE TABLE IF NOT EXISTS accounts (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  type TEXT NOT NULL,
  provider TEXT NOT NULL,
  provider_account_id TEXT NOT NULL,
  refresh_token TEXT,
  access_token TEXT,
  expires_at INTEGER,
  token_type TEXT,
  scope TEXT,
  id_token TEXT,
  session_state TEXT,
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Індекси для акаунтів
CREATE INDEX IF NOT EXISTS idx_accounts_user_id ON accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_accounts_provider ON accounts(provider, provider_account_id);

-- Таблиця сесій (для fallback, основні сесії в KV)
CREATE TABLE IF NOT EXISTS sessions (
  id TEXT PRIMARY KEY,
  session_token TEXT UNIQUE NOT NULL,
  user_id TEXT NOT NULL,
  expires TEXT NOT NULL,
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Індекси для сесій
CREATE INDEX IF NOT EXISTS idx_sessions_session_token ON sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_sessions_expires ON sessions(expires);

-- Таблиця профілів користувачів (розширена інформація)
CREATE TABLE IF NOT EXISTS user_profiles (
  id TEXT PRIMARY KEY,
  user_id TEXT UNIQUE NOT NULL,
  bio TEXT,
  website TEXT,
  location TEXT,
  avatar_url TEXT,
  cover_image_url TEXT,
  social_links TEXT, -- JSON string
  preferences TEXT, -- JSON string
  is_verified BOOLEAN DEFAULT FALSE,
  is_seller BOOLEAN DEFAULT FALSE,
  seller_rating REAL DEFAULT 0.0,
  total_sales INTEGER DEFAULT 0,
  total_downloads INTEGER DEFAULT 0,
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Індекси для профілів
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_is_seller ON user_profiles(is_seller);
CREATE INDEX IF NOT EXISTS idx_user_profiles_seller_rating ON user_profiles(seller_rating);

-- Таблиця ролей користувачів
CREATE TABLE IF NOT EXISTS user_roles (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  role TEXT NOT NULL, -- 'user', 'seller', 'admin', 'moderator'
  granted_by TEXT,
  granted_at TEXT NOT NULL DEFAULT (datetime('now')),
  expires_at TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (granted_by) REFERENCES users(id)
);

-- Індекси для ролей
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_role ON user_roles(role);
CREATE INDEX IF NOT EXISTS idx_user_roles_active ON user_roles(is_active);

-- Таблиця підписок користувачів
CREATE TABLE IF NOT EXISTS user_subscriptions (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  plan_type TEXT NOT NULL, -- 'free', 'basic', 'premium', 'enterprise'
  status TEXT NOT NULL, -- 'active', 'cancelled', 'expired', 'pending'
  stripe_subscription_id TEXT,
  stripe_customer_id TEXT,
  current_period_start TEXT,
  current_period_end TEXT,
  cancel_at_period_end BOOLEAN DEFAULT FALSE,
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Індекси для підписок
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_status ON user_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_stripe_customer ON user_subscriptions(stripe_customer_id);

-- Таблиця активності користувачів
CREATE TABLE IF NOT EXISTS user_activity (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  activity_type TEXT NOT NULL, -- 'login', 'logout', 'download', 'upload', 'purchase'
  activity_data TEXT, -- JSON string з додатковими даними
  ip_address TEXT,
  user_agent TEXT,
  country TEXT,
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Індекси для активності
CREATE INDEX IF NOT EXISTS idx_user_activity_user_id ON user_activity(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_type ON user_activity(activity_type);
CREATE INDEX IF NOT EXISTS idx_user_activity_created_at ON user_activity(created_at);

-- Таблиця налаштувань користувачів
CREATE TABLE IF NOT EXISTS user_settings (
  id TEXT PRIMARY KEY,
  user_id TEXT UNIQUE NOT NULL,
  email_notifications BOOLEAN DEFAULT TRUE,
  marketing_emails BOOLEAN DEFAULT FALSE,
  push_notifications BOOLEAN DEFAULT TRUE,
  privacy_level TEXT DEFAULT 'public', -- 'public', 'friends', 'private'
  language TEXT DEFAULT 'uk',
  timezone TEXT DEFAULT 'Europe/Kiev',
  currency TEXT DEFAULT 'UAH',
  theme TEXT DEFAULT 'system', -- 'light', 'dark', 'system'
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Індекси для налаштувань
CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON user_settings(user_id);

-- Тригери для автоматичного оновлення updated_at
CREATE TRIGGER IF NOT EXISTS update_users_updated_at 
  AFTER UPDATE ON users
  BEGIN
    UPDATE users SET updated_at = datetime('now') WHERE id = NEW.id;
  END;

CREATE TRIGGER IF NOT EXISTS update_accounts_updated_at 
  AFTER UPDATE ON accounts
  BEGIN
    UPDATE accounts SET updated_at = datetime('now') WHERE id = NEW.id;
  END;

CREATE TRIGGER IF NOT EXISTS update_user_profiles_updated_at 
  AFTER UPDATE ON user_profiles
  BEGIN
    UPDATE user_profiles SET updated_at = datetime('now') WHERE id = NEW.id;
  END;

CREATE TRIGGER IF NOT EXISTS update_user_subscriptions_updated_at 
  AFTER UPDATE ON user_subscriptions
  BEGIN
    UPDATE user_subscriptions SET updated_at = datetime('now') WHERE id = NEW.id;
  END;

CREATE TRIGGER IF NOT EXISTS update_user_settings_updated_at 
  AFTER UPDATE ON user_settings
  BEGIN
    UPDATE user_settings SET updated_at = datetime('now') WHERE id = NEW.id;
  END;

-- Тригер для створення профілю користувача при реєстрації
CREATE TRIGGER IF NOT EXISTS create_user_profile_on_user_insert
  AFTER INSERT ON users
  BEGIN
    INSERT INTO user_profiles (id, user_id) 
    VALUES (NEW.id || '_profile', NEW.id);
    
    INSERT INTO user_settings (id, user_id) 
    VALUES (NEW.id || '_settings', NEW.id);
    
    INSERT INTO user_roles (id, user_id, role) 
    VALUES (NEW.id || '_role', NEW.id, 'user');
  END;
