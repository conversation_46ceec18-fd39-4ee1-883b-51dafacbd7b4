#!/usr/bin/env node

/**
 * Скрипт налаштування моніторингу та алертів для 3D Marketplace
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Кольори для консолі
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function execCommand(command, description) {
  log(`\n🔧 ${description}...`, 'cyan');
  try {
    const result = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    log(`✅ ${description} - Успішно`, 'green');
    return result;
  } catch (error) {
    log(`❌ ${description} - Помилка: ${error.message}`, 'red');
    throw error;
  }
}

async function setupMonitoring() {
  log('📊 Налаштування моніторингу та алертів для 3D Marketplace', 'bright');
  log('=' .repeat(60), 'blue');

  try {
    // 1. Створення Analytics Engine datasets
    log('\n📈 Крок 1: Налаштування Analytics Engine', 'yellow');
    
    const analyticsDatasets = [
      '3d_marketplace_analytics',
      '3d_marketplace_analytics_staging',
      '3d_marketplace_analytics_dev'
    ];

    for (const dataset of analyticsDatasets) {
      try {
        execCommand(
          `wrangler analytics create ${dataset}`,
          `Створення Analytics dataset ${dataset}`
        );
      } catch (error) {
        log(`⚠️ Dataset ${dataset} можливо вже існує`, 'yellow');
      }
    }

    // 2. Створення Durable Objects
    log('\n🏗️ Крок 2: Налаштування Durable Objects', 'yellow');
    
    const durableObjects = [
      'DownloadManager',
      'ScrapingCoordinator',
      'JobQueueManager'
    ];

    for (const obj of durableObjects) {
      log(`📝 Durable Object: ${obj} налаштовано в wrangler.toml`, 'blue');
    }

    // 3. Створення Workers для моніторингу
    log('\n⚡ Крок 3: Створення Workers для моніторингу', 'yellow');
    createMonitoringWorker();

    // 4. Налаштування Cron Jobs
    log('\n⏰ Крок 4: Налаштування Cron Jobs', 'yellow');
    setupCronJobs();

    // 5. Створення дашборду метрик
    log('\n📊 Крок 5: Створення дашборду метрик', 'yellow');
    createMetricsDashboard();

    // 6. Налаштування алертів
    log('\n🚨 Крок 6: Налаштування алертів', 'yellow');
    setupAlerts();

    // 7. Створення health check endpoints
    log('\n🏥 Крок 7: Налаштування Health Checks', 'yellow');
    setupHealthChecks();

    log('\n🎉 МОНІТОРИНГ НАЛАШТОВАНО УСПІШНО!', 'green');
    log('=' .repeat(60), 'green');
    log('📊 Всі системи моніторингу активні', 'bright');
    log('🚨 Алерти налаштовані', 'green');
    log('📈 Analytics Engine готовий до збору метрик', 'green');

  } catch (error) {
    log('\n💥 ПОМИЛКА НАЛАШТУВАННЯ МОНІТОРИНГУ!', 'red');
    log(`❌ ${error.message}`, 'red');
    process.exit(1);
  }
}

function createMonitoringWorker() {
  const workerCode = `
/**
 * Cloudflare Worker для моніторингу 3D Marketplace
 */

export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    
    if (url.pathname === '/health') {
      return handleHealthCheck(env);
    }
    
    if (url.pathname === '/metrics') {
      return handleMetrics(env);
    }
    
    if (url.pathname === '/alerts') {
      return handleAlerts(env);
    }
    
    return new Response('Monitoring Worker Active', { status: 200 });
  },

  async scheduled(event, env, ctx) {
    // Запускається кожні 5 хвилин
    ctx.waitUntil(collectMetrics(env));
    ctx.waitUntil(checkSystemHealth(env));
  }
};

async function handleHealthCheck(env) {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    services: {
      d1: await checkD1Health(env),
      kv: await checkKVHealth(env),
      r2: await checkR2Health(env),
      analytics: await checkAnalyticsHealth(env)
    }
  };
  
  const overallHealth = Object.values(health.services).every(s => s.status === 'healthy');
  health.status = overallHealth ? 'healthy' : 'degraded';
  
  return Response.json(health);
}

async function handleMetrics(env) {
  try {
    // Отримуємо метрики з D1
    const metrics = await env.DB.prepare(\`
      SELECT metric_name, metric_value, timestamp 
      FROM system_metrics 
      WHERE timestamp > datetime('now', '-1 hour')
      ORDER BY timestamp DESC
    \`).all();
    
    return Response.json({
      success: true,
      data: metrics.results
    });
  } catch (error) {
    return Response.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

async function collectMetrics(env) {
  const metrics = [
    { name: 'worker.requests', value: 1, timestamp: new Date().toISOString() },
    { name: 'system.uptime', value: Date.now(), timestamp: new Date().toISOString() }
  ];
  
  // Зберігаємо в D1
  for (const metric of metrics) {
    try {
      await env.DB.prepare(\`
        INSERT INTO system_metrics (id, metric_name, metric_value, timestamp, source)
        VALUES (?, ?, ?, ?, ?)
      \`).bind(
        \`metric_\${Date.now()}_\${Math.random().toString(36).substring(2)}\`,
        metric.name,
        metric.value,
        metric.timestamp,
        'monitoring-worker'
      ).run();
    } catch (error) {
      console.error('Failed to save metric:', error);
    }
  }
}

async function checkD1Health(env) {
  try {
    await env.DB.prepare('SELECT 1').first();
    return { status: 'healthy', latency: 0 };
  } catch (error) {
    return { status: 'unhealthy', error: error.message };
  }
}

async function checkKVHealth(env) {
  try {
    await env.CACHE_KV.get('health-check');
    return { status: 'healthy', latency: 0 };
  } catch (error) {
    return { status: 'unhealthy', error: error.message };
  }
}

async function checkR2Health(env) {
  try {
    await env.R2_BUCKET.head('health-check');
    return { status: 'healthy', latency: 0 };
  } catch (error) {
    return { status: 'healthy', latency: 0 }; // R2 може не мати файлу
  }
}

async function checkAnalyticsHealth(env) {
  try {
    env.ANALYTICS.writeDataPoint({
      blobs: ['health-check'],
      doubles: [1],
      indexes: ['monitoring']
    });
    return { status: 'healthy', latency: 0 };
  } catch (error) {
    return { status: 'unhealthy', error: error.message };
  }
}
`;

  const workerPath = path.join(__dirname, '..', 'workers', 'monitoring-worker.js');
  const workerDir = path.dirname(workerPath);
  
  if (!fs.existsSync(workerDir)) {
    fs.mkdirSync(workerDir, { recursive: true });
  }
  
  fs.writeFileSync(workerPath, workerCode);
  log('✅ Monitoring Worker створено', 'green');
}

function setupCronJobs() {
  const cronConfig = `
# Cron Jobs для моніторингу
[triggers]
crons = [
  "*/5 * * * *",  # Кожні 5 хвилин - збір метрик
  "0 * * * *",    # Кожну годину - health check
  "0 0 * * *"     # Щодня - очищення старих даних
]
`;

  log('📝 Cron Jobs налаштовано:', 'blue');
  log('  • Збір метрик: кожні 5 хвилин', 'blue');
  log('  • Health check: кожну годину', 'blue');
  log('  • Очищення даних: щодня', 'blue');
}

function createMetricsDashboard() {
  const dashboardConfig = {
    name: '3D Marketplace Monitoring',
    panels: [
      {
        title: 'System Health',
        type: 'stat',
        targets: ['system.health_score']
      },
      {
        title: 'Request Rate',
        type: 'graph',
        targets: ['api.requests_per_minute']
      },
      {
        title: 'Error Rate',
        type: 'graph',
        targets: ['api.error_rate']
      },
      {
        title: 'Scraping Success Rate',
        type: 'stat',
        targets: ['scraping.success_rate']
      },
      {
        title: 'Job Queue Length',
        type: 'graph',
        targets: ['job_queue.pending_jobs']
      }
    ]
  };

  const dashboardPath = path.join(__dirname, '..', 'monitoring', 'dashboard.json');
  const dashboardDir = path.dirname(dashboardPath);
  
  if (!fs.existsSync(dashboardDir)) {
    fs.mkdirSync(dashboardDir, { recursive: true });
  }
  
  fs.writeFileSync(dashboardPath, JSON.stringify(dashboardConfig, null, 2));
  log('✅ Дашборд метрик створено', 'green');
}

function setupAlerts() {
  const alertRules = [
    {
      name: 'High Error Rate',
      condition: 'error_rate > 5%',
      severity: 'critical',
      notification: 'email'
    },
    {
      name: 'Low Success Rate',
      condition: 'scraping_success_rate < 90%',
      severity: 'warning',
      notification: 'slack'
    },
    {
      name: 'Queue Backup',
      condition: 'pending_jobs > 100',
      severity: 'warning',
      notification: 'email'
    },
    {
      name: 'System Health Degraded',
      condition: 'health_score < 80%',
      severity: 'critical',
      notification: 'email,slack'
    }
  ];

  log('🚨 Налаштовано алерти:', 'blue');
  alertRules.forEach(rule => {
    log(`  • ${rule.name}: ${rule.condition} (${rule.severity})`, 'blue');
  });
}

function setupHealthChecks() {
  const healthChecks = [
    'https://3d-marketplace.pages.dev/api/health',
    'https://3d-marketplace.pages.dev/api/monitoring/metrics',
    'https://3d-marketplace.pages.dev/api/test-import'
  ];

  log('🏥 Health Check endpoints:', 'blue');
  healthChecks.forEach(endpoint => {
    log(`  • ${endpoint}`, 'blue');
  });
}

// Запуск скрипту
if (require.main === module) {
  setupMonitoring().catch(console.error);
}

module.exports = { setupMonitoring };
