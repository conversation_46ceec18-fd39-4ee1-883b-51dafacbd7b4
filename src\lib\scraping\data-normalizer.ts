/**
 * Data normalizer for converting scraped data to internal model format
 */

import {
  ScrapedModel,
  Model,
  Designer,
  License,
  ExternalSource,
  PrintSettings,
  ModelSource
} from '@/types/models';

export class DataNormalizer {
  /**
   * Convert scraped model to internal model format
   */
  static convertToInternalModel(scrapedModel: ScrapedModel, internalId?: string): Model {
    const id = internalId || this.generateInternalId(scrapedModel);

    return {
      id,
      title: this.normalizeTitle(scrapedModel.title),
      description: this.normalizeDescription(scrapedModel.description),
      thumbnail: this.normalizeThumbnail(scrapedModel.thumbnail, scrapedModel.images),
      designer: this.normalizeDesigner(scrapedModel.designer),
      price: scrapedModel.price || 0,
      category: this.normalizeCategory(scrapedModel.category),
      likes: scrapedModel.stats.likes || 0,
      downloads: scrapedModel.stats.downloads || 0,
      tags: this.normalizeTags(scrapedModel.tags),
      isFeatured: false, // Will be determined by internal logic
      images: this.normalizeImages(scrapedModel.images),
      fileFormats: this.normalizeFileFormats(scrapedModel.fileFormats),
      fileSize: this.normalizeFileSize(scrapedModel.totalSize),
      printSettings: this.normalizePrintSettings(scrapedModel.printSettings),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      downloadUrl: this.getDownloadUrl(scrapedModel.files),
      reviewCount: scrapedModel.stats.comments || 0,
      source: scrapedModel.platform,
      externalSource: this.createExternalSource(scrapedModel),
      license: this.normalizeLicense(scrapedModel.license),
      isFree: scrapedModel.isFree,
      originalPrice: scrapedModel.price,
    };
  }

  /**
   * Generate internal model ID
   */
  private static generateInternalId(scrapedModel: ScrapedModel): string {
    const timestamp = Date.now();
    const platformPrefix = scrapedModel.platform;
    const originalId = scrapedModel.originalId;

    return `${platformPrefix}_${originalId}_${timestamp}`;
  }

  /**
   * Normalize title
   */
  private static normalizeTitle(title: string): string {
    return title
      .trim()
      .replace(/\s+/g, ' ')
      .substring(0, 255); // Limit length
  }

  /**
   * Normalize description
   */
  private static normalizeDescription(description: string): string {
    if (!description) return '';

    return description
      .trim()
      .replace(/\s+/g, ' ')
      .substring(0, 5000); // Limit length
  }

  /**
   * Normalize thumbnail URL
   */
  private static normalizeThumbnail(thumbnail: string, images: ScrapedModel['images']): string {
    if (thumbnail && this.isValidUrl(thumbnail)) {
      return thumbnail;
    }

    // Fallback to first image if thumbnail is invalid
    if (images && images.length > 0) {
      return images[0].url;
    }

    return '';
  }

  /**
   * Normalize designer information
   */
  private static normalizeDesigner(scrapedDesigner: ScrapedModel['designer']): Designer {
    return {
      id: scrapedDesigner.id || 'unknown',
      name: scrapedDesigner.name || 'Unknown Designer',
      avatar: scrapedDesigner.avatar && this.isValidUrl(scrapedDesigner.avatar)
        ? scrapedDesigner.avatar
        : undefined,
    };
  }

  /**
   * Normalize category
   */
  private static normalizeCategory(category: string): string {
    if (!category) return 'Other';

    // Map common categories to standardized names
    const categoryMap: Record<string, string> = {
      'art': 'Art & Design',
      'gadgets': 'Gadgets & Electronics',
      'home': 'Home & Garden',
      'toys': 'Toys & Games',
      'tools': 'Tools & Hardware',
      'automotive': 'Automotive',
      'fashion': 'Fashion & Accessories',
      'miniatures': 'Miniatures & Models',
      'costumes': 'Costumes & Cosplay',
      'jewelry': 'Jewelry',
      'household': 'Household Items',
      'outdoor': 'Outdoor & Sports',
    };

    const normalizedKey = category.toLowerCase().trim();
    return categoryMap[normalizedKey] || this.capitalizeWords(category);
  }

  /**
   * Normalize tags
   */
  private static normalizeTags(tags: string[]): string[] {
    if (!tags || !Array.isArray(tags)) return [];

    return [...new Set(tags
      .map(tag => tag.trim().toLowerCase())
      .filter(tag => tag.length > 0 && tag.length <= 50))]
      .slice(0, 20); // Limit number of tags
  }

  /**
   * Normalize images
   */
  private static normalizeImages(images: ScrapedModel['images']): string[] {
    if (!images || !Array.isArray(images)) return [];

    return images
      .map(img => img.url)
      .filter(url => this.isValidUrl(url))
      .slice(0, 10); // Limit number of images
  }

  /**
   * Normalize file formats
   */
  private static normalizeFileFormats(formats: string[]): string[] {
    if (!formats || !Array.isArray(formats)) return ['STL'];

    const validFormats = ['STL', 'OBJ', 'GLTF', 'GLB', 'FBX', '3DS', 'DAE', 'PLY'];

    return formats
      .map(format => format.toUpperCase().trim())
      .filter(format => validFormats.includes(format))
      .slice(0, 5); // Limit number of formats
  }

  /**
   * Normalize file size
   */
  private static normalizeFileSize(totalSize: number): string {
    if (!totalSize || totalSize <= 0) return 'Unknown';

    const units = ['B', 'KB', 'MB', 'GB'];
    let size = totalSize;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)}${units[unitIndex]}`;
  }

  /**
   * Normalize print settings
   */
  private static normalizePrintSettings(printSettings?: ScrapedModel['printSettings']): PrintSettings {
    if (!printSettings) {
      return {
        material: 'PLA',
        layerHeight: '0.2mm',
        infill: '20%',
        supports: 'No',
        rafts: 'No',
        printTime: 'Unknown',
      };
    }

    return {
      material: printSettings.material || 'PLA',
      layerHeight: printSettings.layerHeight || '0.2mm',
      infill: printSettings.infill || '20%',
      supports: printSettings.supports || 'No',
      rafts: printSettings.rafts || 'No',
      printTime: printSettings.printTime || 'Unknown',
    };
  }

  /**
   * Create external source information
   */
  private static createExternalSource(scrapedModel: ScrapedModel): ExternalSource {
    return {
      platform: scrapedModel.platform,
      originalId: scrapedModel.originalId,
      originalUrl: scrapedModel.originalUrl,
      importedAt: scrapedModel.scrapedAt,
    };
  }

  /**
   * Normalize license information
   */
  private static normalizeLicense(scrapedLicense: ScrapedModel['license']): License {
    return {
      type: scrapedLicense.type,
      name: scrapedLicense.name,
      url: scrapedLicense.url,
      description: scrapedLicense.description,
      allowCommercialUse: scrapedLicense.allowCommercialUse,
      requireAttribution: scrapedLicense.requireAttribution,
      allowDerivatives: scrapedLicense.allowDerivatives,
    };
  }

  /**
   * Get primary download URL
   */
  private static getDownloadUrl(files: ScrapedModel['files']): string | undefined {
    if (!files || files.length === 0) return undefined;

    // Prefer STL files
    const stlFile = files.find(file =>
      file.format.toUpperCase() === 'STL' ||
      file.name.toLowerCase().includes('.stl')
    );

    if (stlFile) return stlFile.downloadUrl;

    // Fallback to first file
    return files[0].downloadUrl;
  }

  /**
   * Validate URL
   */
  private static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Capitalize words
   */
  private static capitalizeWords(str: string): string {
    return str
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }

  /**
   * Validate scraped model data
   */
  static validateScrapedModel(scrapedModel: ScrapedModel): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Required fields
    if (!scrapedModel.title || scrapedModel.title.trim().length === 0) {
      errors.push('Title is required');
    }

    if (!scrapedModel.platform) {
      errors.push('Platform is required');
    }

    if (!scrapedModel.originalId) {
      errors.push('Original ID is required');
    }

    if (!scrapedModel.originalUrl || !this.isValidUrl(scrapedModel.originalUrl)) {
      errors.push('Valid original URL is required');
    }

    if (!scrapedModel.designer || !scrapedModel.designer.name) {
      errors.push('Designer information is required');
    }

    // Optional but recommended fields
    if (!scrapedModel.description || scrapedModel.description.trim().length === 0) {
      errors.push('Description is recommended');
    }

    if (!scrapedModel.images || scrapedModel.images.length === 0) {
      errors.push('At least one image is recommended');
    }

    if (!scrapedModel.files || scrapedModel.files.length === 0) {
      errors.push('At least one file is recommended');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Clean and sanitize scraped data
   */
  static sanitizeScrapedModel(scrapedModel: ScrapedModel): ScrapedModel {
    return {
      ...scrapedModel,
      title: this.sanitizeString(scrapedModel.title),
      description: this.sanitizeString(scrapedModel.description),
      tags: scrapedModel.tags.map(tag => this.sanitizeString(tag)),
      designer: {
        ...scrapedModel.designer,
        name: this.sanitizeString(scrapedModel.designer.name),
      },
    };
  }

  /**
   * Sanitize string content
   */
  private static sanitizeString(str: string): string {
    if (!str) return '';

    return str
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove scripts
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/[^\w\s\-.,!?()]/g, '') // Remove special characters except basic punctuation
      .trim();
  }
}
