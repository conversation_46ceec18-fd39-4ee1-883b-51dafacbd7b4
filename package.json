{"name": "3d-marketplace", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "pages:build": "npx @cloudflare/next-on-pages", "pages:dev": "npm run build && node scripts/prepare-pages.js && npx serve dist -p 8788", "pages:dev-cf": "npm run build && node scripts/prepare-pages.js && wrangler pages dev dist --port 8788", "preview": "opennextjs-cloudflare build && opennextjs-cloudflare preview", "deploy": "opennextjs-cloudflare build && wrangler pages deploy --project-name=3d-marketplace --commit-dirty=true", "cf-typegen": "wrangler types --env-interface CloudflareEnv cloudflare-env.d.ts", "worker": "wrangler dev --config wrangler-worker.toml", "worker:create": "wrangler init marketplace-worker --type=module", "worker:build": "vite build --config vite.worker.config.js", "worker:deploy": "wrangler deploy --config wrangler-worker.toml", "db:setup": "node scripts/setup-local-db.js", "db:create": "wrangler d1 create marketplace_db", "db:execute": "wrangler d1 execute marketplace_db --file=schema.sql", "db:local": "wrangler d1 execute marketplace_db --local --file=schema.sql", "dev:d1": "wrangler pages dev .next --compatibility-date=2023-10-30 --compatibility-flag=nodejs_compat --d1=marketplace_db", "dev:cloudflare": "wrangler pages dev .next --compatibility-date=2023-10-30 --compatibility-flag=nodejs_compat --d1=marketplace_db --r2=marketplace-storage --kv=marketplace-kv", "dev:full": "wrangler pages dev .next --compatibility-date=2023-10-30 --compatibility-flag=nodejs_compat --d1=marketplace_db --r2=marketplace-storage --kv=marketplace-kv", "r2:create": "wrangler r2 bucket create marketplace-storage", "r2:list": "wrangler r2 bucket list", "d1:info": "wrangler d1 info marketplace_db", "kv:create": "wrangler kv:namespace create marketplace-kv", "kv:list": "wrangler kv:namespace list", "kv:put": "wrangler kv:key put --namespace-id=9711cfa19bd04ce4afbd8b28bd051f7b", "kv:get": "wrangler kv:key get --namespace-id=9711cfa19bd04ce4afbd8b28bd051f7b", "hyperdrive:create": "wrangler hyperdrive create marketplace-hyperdrive", "analyze": "cross-env ANALYZE=true next build", "analyze:server": "cross-env ANALYZE=true BUNDLE_ANALYZE=server next build", "analyze:browser": "cross-env ANALYZE=true BUNDLE_ANALYZE=browser next build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:scraping": "jest --testPathPattern=scraping", "test:e2e": "jest --testPathPattern=e2e", "test:system": "node scripts/test-scraping-system.js", "clean": "rm -rf .next node_modules/.cache", "vercel:deploy": "vercel --prod", "cloudflare:deploy": "node scripts/deploy-cloudflare.js", "cloudflare:build": "npm run build && wrangler pages deploy .next --project-name=3d-marketplace", "cloudflare:setup": "wrangler d1 create marketplace-db && wrangler r2 bucket create marketplace-storage", "auth:init": "node scripts/init-auth-db.js", "auth:setup": "npm run auth:init && echo 'Додайте секрети: wrangler secret put GOOGLE_CLIENT_SECRET && wrangler secret put GITHUB_CLIENT_SECRET && wrangler secret put NEXTAUTH_SECRET'", "deploy:quick": "npm run build && npx @opennextjs/cloudflare@latest build && wrangler pages deploy .vercel/output/static --project-name=3d-marketplace", "import-popular": "tsx src/scripts/run-import.ts", "import-popular:all": "tsx src/scripts/run-import.ts all", "import-popular:sample": "tsx src/scripts/run-import.ts sample", "import-popular:thingiverse": "tsx src/scripts/run-import.ts platform thingiverse", "import-popular:myminifactory": "tsx src/scripts/run-import.ts platform myminifactory", "deploy:cloudflare": "node scripts/quick-deploy.js", "deploy:full": "node scripts/deploy-cloudflare-production.js", "setup:monitoring": "node scripts/setup-monitoring.js", "type-check": "tsc --noEmit"}, "dependencies": {"@cloudflare/next-on-pages": "^1.13.12", "@cloudflare/workers-types": "^4.20250514.0", "@hookform/resolvers": "^5.0.1", "@opennextjs/cloudflare": "^1.0.4", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.2.6", "@react-three/drei": "^10.0.7", "@react-three/fiber": "^9.1.2", "@splinetool/react-spline": "^4.0.0", "@splinetool/runtime": "^1.9.94", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@supabase/supabase-js": "^2.49.4", "@types/bcryptjs": "^2.4.6", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.11.0", "ioredis": "^5.6.1", "lucide-react": "^0.510.0", "next": "^15.3.2", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "puppeteer": "^24.9.0", "rate-limiter-flexible": "^7.1.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.3", "stripe": "^18.1.1", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "three": "^0.176.0", "zod": "^3.24.4", "zustand": "^5.0.4"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.2", "@shadcn/ui": "^0.0.4", "@types/cheerio": "^0.22.35", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/puppeteer": "^5.4.7", "@types/react": "^19", "@types/react-dom": "^19", "@types/three": "^0.176.0", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.3", "ts-jest": "^29.3.4", "tsx": "^4.19.4", "typescript": "^5", "wrangler": "^4.16.1"}}