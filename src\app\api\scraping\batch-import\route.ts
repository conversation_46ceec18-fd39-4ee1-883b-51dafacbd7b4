import { NextRequest, NextResponse } from 'next/server';
import { ScraperManager } from '@/lib/scrapers/scraper-manager';
import { jobQueueManager } from '@/lib/queue/job-queue-manager';
import { errorHandler } from '@/lib/error-handling/enhanced-error-handler';

/**
 * POST /api/scraping/batch-import
 * Додає завдання для пакетного імпорту моделей до черги
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { urls, options = {}, userId } = body;

    // Валідація
    if (!urls || !Array.isArray(urls) || urls.length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: 'URLs array is required and must not be empty',
            code: 'INVALID_INPUT'
          }
        },
        { status: 400 }
      );
    }

    if (urls.length > 100) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: 'Maximum 100 URLs allowed per batch',
            code: 'TOO_MANY_URLS'
          }
        },
        { status: 400 }
      );
    }

    // Валідація URL
    const invalidUrls = urls.filter((url: string) => {
      try {
        new URL(url);
        return !url.includes('thingiverse.com') && 
               !url.includes('myminifactory.com') && 
               !url.includes('printables.com') && 
               !url.includes('thangs.com') &&
               !url.includes('makerworld.com');
      } catch {
        return true;
      }
    });

    if (invalidUrls.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: `Invalid URLs found: ${invalidUrls.slice(0, 3).join(', ')}${invalidUrls.length > 3 ? '...' : ''}`,
            code: 'INVALID_URLS',
            details: { invalidUrls: invalidUrls.slice(0, 10) }
          }
        },
        { status: 400 }
      );
    }

    // Створюємо завдання в черзі
    const scraperManager = new ScraperManager();
    const jobId = await scraperManager.addBatchImportJob(urls, userId);

    return NextResponse.json({
      success: true,
      data: {
        jobId,
        urlCount: urls.length,
        estimatedDuration: urls.length * 5, // 5 секунд на URL
        message: 'Batch import job created successfully'
      }
    });

  } catch (error) {
    const enhancedError = await errorHandler.handleError(error as Error, {
      url: request.url,
      userAgent: request.headers.get('user-agent') || undefined
    });

    return NextResponse.json(
      { 
        success: false, 
        error: { 
          message: enhancedError.userMessage,
          code: enhancedError.code,
          category: enhancedError.category
        }
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/scraping/batch-import?jobId=xxx
 * Отримує статус завдання пакетного імпорту
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');

    if (!jobId) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: 'Job ID is required',
            code: 'MISSING_JOB_ID'
          }
        },
        { status: 400 }
      );
    }

    const job = jobQueueManager.getJob(jobId);

    if (!job) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: 'Job not found',
            code: 'JOB_NOT_FOUND'
          }
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: job.id,
        status: job.status,
        progress: job.progress,
        result: job.result,
        createdAt: job.createdAt,
        startedAt: job.startedAt,
        completedAt: job.completedAt,
        retryCount: job.retryCount,
        maxRetries: job.maxRetries
      }
    });

  } catch (error) {
    const enhancedError = await errorHandler.handleError(error as Error, {
      url: request.url,
      userAgent: request.headers.get('user-agent') || undefined
    });

    return NextResponse.json(
      { 
        success: false, 
        error: { 
          message: enhancedError.userMessage,
          code: enhancedError.code,
          category: enhancedError.category
        }
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/scraping/batch-import?jobId=xxx
 * Скасовує завдання пакетного імпорту
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');

    if (!jobId) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: 'Job ID is required',
            code: 'MISSING_JOB_ID'
          }
        },
        { status: 400 }
      );
    }

    const cancelled = await jobQueueManager.cancelJob(jobId);

    if (!cancelled) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: 'Job not found or cannot be cancelled',
            code: 'CANNOT_CANCEL_JOB'
          }
        },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        message: 'Job cancelled successfully'
      }
    });

  } catch (error) {
    const enhancedError = await errorHandler.handleError(error as Error, {
      url: request.url,
      userAgent: request.headers.get('user-agent') || undefined
    });

    return NextResponse.json(
      { 
        success: false, 
        error: { 
          message: enhancedError.userMessage,
          code: enhancedError.code,
          category: enhancedError.category
        }
      },
      { status: 500 }
    );
  }
}
