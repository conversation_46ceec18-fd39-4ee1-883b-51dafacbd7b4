// Спрощений скрапер для продакшну (без Puppeteer)
import { generateId } from '@/lib/db';

interface ScrapedModel {
  id: string;
  name: string;
  description: string;
  thumbnail_url: string;
  model_url: string;
  category: string;
  tags: string[];
  author_name: string;
  author_avatar: string;
  download_count: number;
  like_count: number;
  view_count: number;
  is_free: boolean;
  price: number;
  file_size: number;
  file_formats: string[];
  print_settings: any;
  license: string;
  created_at: string;
  source_url: string;
  source_platform: string;
}

export class SimpleScraper {
  
  // Генерація високоякісних тестових даних
  async generateFakeData(count: number = 100): Promise<ScrapedModel[]> {
    console.log(`🎭 Генерація ${count} високоякісних моделей...`);
    
    const models: ScrapedModel[] = [];
    
    const categories = [
      'Іграшки', 'Ігри', 'Мініатюри', 'Побутові предмети', 
      'Інструменти', 'Гаджети', 'Мистецтво', 'Прикраси',
      'Автомобільні', 'Електроніка', 'Мода', 'Дім', 'Сад'
    ];
    
    const modelNames = [
      'Дракон-мініатюра', 'Функціональна ваза', 'Іграшковий робот', 'Декоративна фігурка',
      'Корисний гаджет', 'Настільна гра', 'Прикраса для дому', 'Інструмент для кухні',
      'Автомобільна деталь', 'Електронний корпус', 'Модний аксесуар', 'Садовий декор',
      'Освітня модель', 'Запасна частина', 'Художня скульптура', 'Практичний держак',
      'Механічна іграшка', 'Архітектурна модель', 'Персонаж з гри', 'Транспортний засіб',
      'Кухонний помічник', 'Офісний органайзер', 'Спортивний аксесуар', 'Музичний інструмент',
      'Технічний прототип', 'Дизайнерський об\'єкт', 'Функціональний гаджет', 'Творча робота'
    ];
    
    const authors = [
      'TechDesigner', 'CreativeArtist', 'MakerPro', 'DigitalCrafter',
      'ModelMaster', 'PrintExpert', 'DesignGuru', 'ArtisticMind',
      'InnovateMaker', 'CraftingPro', 'DesignWizard', 'CreativeTech',
      'MasterBuilder', 'ArtisticGenius', 'TechArtist', 'DesignMaestro'
    ];
    
    const descriptions = [
      'Високоякісна 3D модель, ідеально підходить для 3D друку',
      'Детально опрацьована модель з професійною якістю',
      'Готова до друку модель з оптимізованою геометрією',
      'Унікальний дизайн з увагою до деталей',
      'Функціональна модель для практичного використання',
      'Художня робота з вишуканим дизайном',
      'Технічно досконала модель для професіоналів',
      'Креативне рішення для ваших проектів'
    ];
    
    const tags = [
      '3d-print', 'stl', 'model', 'design', 'art', 'functional', 'decorative',
      'miniature', 'toy', 'gadget', 'tool', 'home', 'office', 'creative',
      'professional', 'hobby', 'educational', 'prototype', 'artistic'
    ];
    
    for (let i = 0; i < count; i++) {
      const category = categories[Math.floor(Math.random() * categories.length)];
      const baseName = modelNames[Math.floor(Math.random() * modelNames.length)];
      const name = `${baseName} #${i + 1}`;
      const author = authors[Math.floor(Math.random() * authors.length)];
      const description = descriptions[Math.floor(Math.random() * descriptions.length)];
      
      // Генеруємо реалістичні статистики
      const downloadCount = Math.floor(Math.random() * 10000) + 100;
      const likeCount = Math.floor(downloadCount * (0.1 + Math.random() * 0.3)); // 10-40% від завантажень
      const viewCount = Math.floor(downloadCount * (3 + Math.random() * 7)); // 3-10x від завантажень
      
      // 70% безкоштовних моделей
      const isFree = Math.random() > 0.3;
      const price = isFree ? 0 : Math.floor(Math.random() * 50) + 5;
      
      // Реалістичні розміри файлів (1-100MB)
      const fileSize = Math.floor(Math.random() * 99000000) + 1000000;
      
      // Випадкові теги (2-5 тегів)
      const modelTags = [];
      const tagCount = Math.floor(Math.random() * 4) + 2;
      for (let j = 0; j < tagCount; j++) {
        const tag = tags[Math.floor(Math.random() * tags.length)];
        if (!modelTags.includes(tag)) {
          modelTags.push(tag);
        }
      }
      modelTags.push(category.toLowerCase());
      
      // Реалістичні налаштування друку
      const layerHeights = ['0.1mm', '0.15mm', '0.2mm', '0.25mm', '0.3mm'];
      const infills = ['10%', '15%', '20%', '25%', '30%'];
      const printTime = `${Math.floor(Math.random() * 24) + 1}h ${Math.floor(Math.random() * 60)}m`;
      
      const model: ScrapedModel = {
        id: generateId(),
        name,
        description: `${description}. ${name} - це ${description.toLowerCase()} з унікальними характеристиками та професійною якістю виконання.`,
        thumbnail_url: `https://picsum.photos/400/300?random=${i}&blur=1`,
        model_url: `https://example.com/model/${generateId()}`,
        category,
        tags: modelTags,
        author_name: author,
        author_avatar: `https://picsum.photos/100/100?random=${i + 1000}`,
        download_count: downloadCount,
        like_count: likeCount,
        view_count: viewCount,
        is_free: isFree,
        price,
        file_size: fileSize,
        file_formats: ['STL', 'OBJ', 'PLY'],
        print_settings: {
          layer_height: layerHeights[Math.floor(Math.random() * layerHeights.length)],
          infill: infills[Math.floor(Math.random() * infills.length)],
          supports: Math.random() > 0.5,
          print_time: printTime,
          nozzle_temperature: Math.floor(Math.random() * 50) + 200, // 200-250°C
          bed_temperature: Math.floor(Math.random() * 40) + 50, // 50-90°C
          print_speed: Math.floor(Math.random() * 30) + 40 // 40-70 mm/s
        },
        license: Math.random() > 0.5 ? 'Creative Commons' : 'Personal Use',
        created_at: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
        source_url: `https://example.com/model/${generateId()}`,
        source_platform: 'Generated'
      };
      
      models.push(model);
    }
    
    console.log(`✅ Згенеровано ${models.length} високоякісних моделей!`);
    return models;
  }
  
  // Симуляція скрапінгу з Printables
  async simulatePrintablesScraping(count: number = 20): Promise<ScrapedModel[]> {
    console.log(`📦 Симуляція скрапінгу з Printables (${count} моделей)...`);
    
    const models = await this.generateFakeData(count);
    
    // Налаштовуємо моделі під стиль Printables
    return models.map(model => ({
      ...model,
      is_free: true, // Printables переважно безкоштовні
      price: 0,
      source_platform: 'Printables (Simulated)',
      license: 'Creative Commons',
      source_url: `https://www.printables.com/model/${Math.floor(Math.random() * 100000)}`
    }));
  }
  
  // Симуляція скрапінгу з Thangs
  async simulateThangsScraping(count: number = 20): Promise<ScrapedModel[]> {
    console.log(`🎯 Симуляція скрапінгу з Thangs (${count} моделей)...`);
    
    const models = await this.generateFakeData(count);
    
    // Налаштовуємо моделі під стиль Thangs
    return models.map(model => ({
      ...model,
      is_free: Math.random() > 0.4, // 60% безкоштовних
      price: Math.random() > 0.4 ? 0 : Math.floor(Math.random() * 100) + 10,
      source_platform: 'Thangs (Simulated)',
      license: 'Various',
      source_url: `https://thangs.com/model/${Math.floor(Math.random() * 100000)}`
    }));
  }
  
  // Симуляція скрапінгу з MakerWorld
  async simulateMakerWorldScraping(count: number = 20): Promise<ScrapedModel[]> {
    console.log(`🌍 Симуляція скрапінгу з MakerWorld (${count} моделей)...`);
    
    const models = await this.generateFakeData(count);
    
    // Налаштовуємо моделі під стиль MakerWorld
    return models.map(model => ({
      ...model,
      is_free: Math.random() > 0.2, // 80% безкоштовних
      price: Math.random() > 0.2 ? 0 : Math.floor(Math.random() * 30) + 5,
      source_platform: 'MakerWorld (Simulated)',
      license: 'MakerWorld License',
      source_url: `https://makerworld.com/model/${Math.floor(Math.random() * 100000)}`
    }));
  }
  
  // Комбінований скрапінг з усіх платформ
  async simulateAllPlatforms(modelsPerPlatform: number = 15): Promise<ScrapedModel[]> {
    console.log(`🚀 Симуляція скрапінгу з усіх платформ (${modelsPerPlatform * 3} моделей)...`);
    
    const allModels: ScrapedModel[] = [];
    
    try {
      // Printables
      const printablesModels = await this.simulatePrintablesScraping(modelsPerPlatform);
      allModels.push(...printablesModels);
      
      // Thangs
      const thangsModels = await this.simulateThangsScraping(modelsPerPlatform);
      allModels.push(...thangsModels);
      
      // MakerWorld
      const makerWorldModels = await this.simulateMakerWorldScraping(modelsPerPlatform);
      allModels.push(...makerWorldModels);
      
      console.log(`✅ Загалом згенеровано ${allModels.length} моделей з усіх платформ!`);
      
    } catch (error) {
      console.error('❌ Помилка симуляції скрапінгу:', error);
    }
    
    return allModels;
  }
}
