'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Download, Check, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';

interface DownloadButtonProps {
  className?: string;
  variant?: 'ghost' | 'default' | 'outline';
  size?: 'sm' | 'default' | 'lg' | 'icon';
}

const DownloadButton: React.FC<DownloadButtonProps> = ({ 
  className = "", 
  variant = "ghost",
  size = "icon"
}) => {
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadComplete, setDownloadComplete] = useState(false);

  const handleDownload = async (type: string) => {
    setIsDownloading(true);
    setDownloadComplete(false);
    
    try {
      // Симуляція завантаження
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Тут буде логіка завантаження файлів
      console.log(`Завантаження ${type}...`);
      
      setDownloadComplete(true);
      setTimeout(() => {
        setDownloadComplete(false);
      }, 2000);
    } catch (error) {
      console.error('Помилка завантаження:', error);
    } finally {
      setIsDownloading(false);
    }
  };

  const downloadOptions = [
    { label: 'Популярні моделі', value: 'popular', description: 'Топ 50 моделей' },
    { label: 'Нові моделі', value: 'new', description: 'Останні додані' },
    { label: 'Безкоштовні моделі', value: 'free', description: 'Всі безкоштовні' },
    { label: 'Мої завантаження', value: 'my-downloads', description: 'Раніше завантажені' },
  ];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
          <Button
            variant={variant}
            size={size}
            className={`hover:bg-accent/50 transition-colors ${className}`}
            title="Завантажити моделі"
          >
            <AnimatePresence mode="wait">
              {downloadComplete ? (
                <motion.div
                  key="success"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.2 }}
                >
                  <Check className="h-5 w-5 text-green-500" />
                </motion.div>
              ) : isDownloading ? (
                <motion.div
                  key="loading"
                  initial={{ opacity: 0, rotate: 0 }}
                  animate={{ opacity: 1, rotate: 360 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="animate-spin"
                >
                  <Download className="h-5 w-5" />
                </motion.div>
              ) : (
                <motion.div
                  key="download"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <Download className="h-5 w-5" />
                </motion.div>
              )}
            </AnimatePresence>
            <span className="sr-only">Завантажити</span>
          </Button>
        </motion.div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>Швидке завантаження</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {downloadOptions.map((option) => (
          <DropdownMenuItem
            key={option.value}
            onClick={() => handleDownload(option.value)}
            disabled={isDownloading}
            className="flex flex-col items-start gap-1 cursor-pointer"
          >
            <div className="flex items-center gap-2 w-full">
              <Download className="h-4 w-4" />
              <span className="font-medium">{option.label}</span>
            </div>
            <span className="text-xs text-muted-foreground ml-6">
              {option.description}
            </span>
          </DropdownMenuItem>
        ))}
        <DropdownMenuSeparator />
        <DropdownMenuItem className="text-xs text-muted-foreground">
          <AlertCircle className="h-3 w-3 mr-2" />
          Завантаження може зайняти час
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default DownloadButton;
