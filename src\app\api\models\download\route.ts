import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { queryOne, execute, generateId } from '@/lib/db';

// POST handler for creating download session using Durable Objects
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json() as { modelId: string };
    const { modelId } = body;

    if (!modelId) {
      return NextResponse.json(
        { success: false, error: 'Model ID is required' },
        { status: 400 }
      );
    }

    // Отримуємо Durable Object для управління завантаженнями
    const durableObjectId = (globalThis as any).DOWNLOAD_MANAGER?.idFromName(`download-${modelId}`);

    if (!durableObjectId) {
      // Fallback до старої логіки, якщо Durable Objects недоступні
      return await handleDownloadFallback(request, session, modelId);
    }

    const durableObject = (globalThis as any).DOWNLOAD_MANAGER?.get(durableObjectId);

    // Створюємо запит на завантаження
    const downloadRequest = {
      modelId,
      userId: session.user.id,
      userEmail: session.user.email!,
      timestamp: Date.now(),
      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
      userAgent: request.headers.get('user-agent')
    };

    // Відправляємо запит до Durable Object
    const response = await durableObject.fetch('https://download-manager/create-session', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(downloadRequest)
    });

    const result = await response.json();

    if (!result.success) {
      return NextResponse.json(result, { status: response.status });
    }

    return NextResponse.json({
      success: true,
      sessionId: result.sessionId,
      expiresAt: result.expiresAt,
      message: 'Download session created successfully'
    });

  } catch (error) {
    console.error('Error creating download session:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create download session' },
      { status: 500 }
    );
  }
}

// GET handler for getting download session status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');
    const modelId = searchParams.get('modelId');

    if (!sessionId || !modelId) {
      return NextResponse.json(
        { success: false, error: 'Session ID and Model ID are required' },
        { status: 400 }
      );
    }

    const durableObjectId = (globalThis as any).DOWNLOAD_MANAGER?.idFromName(`download-${modelId}`);

    if (!durableObjectId) {
      return NextResponse.json(
        { success: false, error: 'Download service unavailable' },
        { status: 503 }
      );
    }

    const durableObject = (globalThis as any).DOWNLOAD_MANAGER?.get(durableObjectId);

    const response = await durableObject.fetch(`https://download-manager/get-session?sessionId=${sessionId}`);
    const result = await response.json();

    return NextResponse.json(result, { status: response.status });

  } catch (error) {
    console.error('Error getting download session:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to get download session' },
      { status: 500 }
    );
  }
}

// Fallback функція для випадків, коли Durable Objects недоступні
async function handleDownloadFallback(request: NextRequest, session: any, modelId: string) {
  try {
    // Get model details
    const model = await queryOne(
      `SELECT m.*, u.name as author_name
       FROM models m
       JOIN users u ON m.user_id = u.id
       WHERE m.id = ?`,
      [modelId]
    );

    if (!model) {
      return NextResponse.json(
        { success: false, error: 'Model not found' },
        { status: 404 }
      );
    }

    // Check if user owns the model
    const isOwner = (model as any).user_id === session.user.id;

    // Check if model is free
    const isFree = (model as any).is_free;

    // Check if user has already purchased this model
    const existingPurchase = await queryOne(
      'SELECT * FROM user_models WHERE user_id = ? AND model_id = ?',
      [session.user.id, modelId]
    );

    // Allow download if:
    // 1. User owns the model
    // 2. Model is free
    // 3. User has purchased the model
    if (!isOwner && !isFree && !existingPurchase) {
      return NextResponse.json(
        { success: false, error: 'Access denied. Purchase required.' },
        { status: 403 }
      );
    }

    // For free models, add to user's models if not already there
    if (isFree && !existingPurchase && !isOwner) {
      const userModelId = generateId();
      await execute(
        'INSERT INTO user_models (id, user_id, model_id, created_at) VALUES (?, ?, ?, CURRENT_TIMESTAMP)',
        [userModelId, session.user.id, modelId]
      );
    }

    // Increment download count
    await execute(
      'UPDATE models SET download_count = download_count + 1 WHERE id = ?',
      [modelId]
    );

    // Return download information
    const downloadData = {
      modelId: (model as any).id,
      name: (model as any).name,
      modelUrl: (model as any).model_url,
      additionalFiles: (model as any).additional_files ? JSON.parse((model as any).additional_files) : [],
      downloadedAt: new Date().toISOString(),
    };

    return NextResponse.json({
      success: true,
      data: downloadData,
      message: isFree ? 'Free model downloaded successfully' : 'Model downloaded successfully'
    });

  } catch (error) {
    console.error('Error downloading model:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to download model' },
      { status: 500 }
    );
  }
}
