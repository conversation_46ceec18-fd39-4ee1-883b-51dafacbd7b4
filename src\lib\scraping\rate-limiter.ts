/**
 * Rate limiting system for web scraping operations
 */

import { ModelSource } from '@/types/models';

export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

export interface RateLimitInfo {
  totalHits: number;
  totalHitsPerWindow: number;
  remainingPoints: number;
  msBeforeNext: number;
  isFirstInDuration: boolean;
}

/**
 * In-memory rate limiter for development/testing
 * In production, this should be replaced with Redis-based implementation
 */
export class MemoryRateLimiter {
  private requests: Map<string, number[]> = new Map();
  private config: RateLimitConfig;

  constructor(config: RateLimitConfig) {
    this.config = config;
  }

  async consume(key: string, points: number = 1): Promise<RateLimitInfo> {
    const now = Date.now();
    const windowStart = now - this.config.windowMs;

    // Get existing requests for this key
    let keyRequests = this.requests.get(key) || [];

    // Remove old requests outside the window
    keyRequests = keyRequests.filter(timestamp => timestamp > windowStart);

    // Check if we can make the request
    const totalHitsPerWindow = keyRequests.length;
    const remainingPoints = Math.max(0, this.config.maxRequests - totalHitsPerWindow);

    // Check if adding points would exceed limit
    if (totalHitsPerWindow + points > this.config.maxRequests) {
      const oldestRequest = keyRequests.length > 0 ? Math.min(...keyRequests) : now;
      const msBeforeNext = Math.max(0, oldestRequest + this.config.windowMs - now);

      throw new RateLimitError({
        totalHits: totalHitsPerWindow + points,
        totalHitsPerWindow,
        remainingPoints: 0,
        msBeforeNext,
        isFirstInDuration: false,
      });
    }

    // Add current request
    for (let i = 0; i < points; i++) {
      keyRequests.push(now);
    }

    // Update the map
    this.requests.set(key, keyRequests);

    return {
      totalHits: totalHitsPerWindow + points,
      totalHitsPerWindow: totalHitsPerWindow + points,
      remainingPoints: Math.max(0, remainingPoints - points),
      msBeforeNext: 0,
      isFirstInDuration: totalHitsPerWindow === 0,
    };
  }

  async reset(key: string): Promise<void> {
    this.requests.delete(key);
  }

  async getRemainingPoints(key: string): Promise<number> {
    const now = Date.now();
    const windowStart = now - this.config.windowMs;

    let keyRequests = this.requests.get(key) || [];
    keyRequests = keyRequests.filter(timestamp => timestamp > windowStart);

    return Math.max(0, this.config.maxRequests - keyRequests.length);
  }
}

/**
 * Rate limit error
 */
export class RateLimitError extends Error {
  public rateLimitInfo: RateLimitInfo;

  constructor(rateLimitInfo: RateLimitInfo) {
    const message = `Rate limit exceeded. Try again in ${Math.ceil(rateLimitInfo.msBeforeNext / 1000)} seconds.`;
    super(message);
    this.name = 'RateLimitError';
    this.rateLimitInfo = rateLimitInfo;
  }
}

/**
 * Platform-specific rate limiters
 */
export class PlatformRateLimiters {
  private limiters: Map<ModelSource, MemoryRateLimiter> = new Map();

  constructor() {
    // Initialize rate limiters for each platform
    this.limiters.set('printables', new MemoryRateLimiter({
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 10,
    }));

    this.limiters.set('makerworld', new MemoryRateLimiter({
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 15,
    }));

    this.limiters.set('thangs', new MemoryRateLimiter({
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 12,
    }));

    this.limiters.set('thingiverse', new MemoryRateLimiter({
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 8,
    }));

    this.limiters.set('myminifactory', new MemoryRateLimiter({
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 10,
    }));
  }

  async consume(platform: ModelSource, identifier: string = 'default', points: number = 1): Promise<RateLimitInfo> {
    const limiter = this.limiters.get(platform);
    if (!limiter) {
      throw new Error(`No rate limiter configured for platform: ${platform}`);
    }

    const key = `${platform}:${identifier}`;
    return await limiter.consume(key, points);
  }

  async getRemainingPoints(platform: ModelSource, identifier: string = 'default'): Promise<number> {
    const limiter = this.limiters.get(platform);
    if (!limiter) {
      return 0;
    }

    const key = `${platform}:${identifier}`;
    return await limiter.getRemainingPoints(key);
  }

  async reset(platform: ModelSource, identifier: string = 'default'): Promise<void> {
    const limiter = this.limiters.get(platform);
    if (!limiter) {
      return;
    }

    const key = `${platform}:${identifier}`;
    await limiter.reset(key);
  }

  async getAllRemainingPoints(): Promise<Record<ModelSource, number>> {
    const results: Partial<Record<ModelSource, number>> = {};

    for (const [platform, limiter] of this.limiters) {
      try {
        results[platform] = await limiter.getRemainingPoints(`${platform}:default`);
      } catch (error) {
        results[platform] = 0;
      }
    }

    return results as Record<ModelSource, number>;
  }
}

/**
 * Global rate limiter instance
 */
export const platformRateLimiters = new PlatformRateLimiters();

/**
 * Rate limiting middleware for API routes
 */
export async function withRateLimit<T>(
  platform: ModelSource,
  identifier: string,
  operation: () => Promise<T>
): Promise<T> {
  try {
    await platformRateLimiters.consume(platform, identifier);
    return await operation();
  } catch (error) {
    if (error instanceof RateLimitError) {
      throw error;
    }
    throw error;
  }
}

/**
 * Utility to wait for rate limit reset
 */
export async function waitForRateLimit(platform: ModelSource, identifier: string = 'default'): Promise<void> {
  try {
    await platformRateLimiters.consume(platform, identifier, 0); // Check without consuming
  } catch (error) {
    if (error instanceof RateLimitError) {
      const waitTime = error.rateLimitInfo.msBeforeNext;
      if (waitTime > 0) {
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
  }
}

/**
 * Get rate limit status for all platforms
 */
export async function getRateLimitStatus(): Promise<Record<ModelSource, {
  remaining: number;
  limit: number;
  resetTime?: number;
}>> {
  const remaining = await platformRateLimiters.getAllRemainingPoints();

  const limits: Record<ModelSource, number> = {
    local: 0,
    printables: 10,
    makerworld: 15,
    thangs: 12,
    thingiverse: 8,
    myminifactory: 10,
  };

  const result: Partial<Record<ModelSource, { remaining: number; limit: number; resetTime?: number }>> = {};

  for (const platform of Object.keys(limits) as ModelSource[]) {
    result[platform] = {
      remaining: remaining[platform] || 0,
      limit: limits[platform],
    };
  }

  return result as Record<ModelSource, { remaining: number; limit: number; resetTime?: number }>;
}
