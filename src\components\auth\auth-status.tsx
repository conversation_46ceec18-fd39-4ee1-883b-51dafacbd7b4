/**
 * Компонент для відображення статусу аутентифікації
 * Інтеграція з Cloudflare Analytics
 */

'use client';

import { useSession, signOut } from 'next-auth/react';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { User, Settings, LogOut, ShoppingBag, Heart, Upload } from 'lucide-react';
import Link from 'next/link';
import { useCloudflareAnalytics } from '@/hooks/useCloudflareAnalytics';

export function AuthStatus() {
  const { data: session, status } = useSession();
  const [isLoading, setIsLoading] = useState(false);
  const { trackUserAction, trackEvent } = useCloudflareAnalytics();

  useEffect(() => {
    if (session?.user) {
      // Трекінг активної сесії
      trackEvent('session_active', session.user.id, {
        provider: session.user.provider,
        email: session.user.email,
        timestamp: new Date().toISOString()
      });
    }
  }, [session, trackEvent]);

  const handleSignOut = async () => {
    setIsLoading(true);
    
    try {
      // Трекінг виходу
      if (session?.user) {
        await trackUserAction('signout', 'header_menu', session.user.id);
      }
      
      await signOut({ callbackUrl: '/' });
    } catch (error) {
      console.error('Error signing out:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleMenuClick = async (action: string, target: string) => {
    if (session?.user) {
      await trackUserAction(action, target, session.user.id);
    }
  };

  if (status === 'loading') {
    return (
      <div className="flex items-center space-x-2">
        <div className="animate-pulse bg-muted rounded-full h-8 w-8"></div>
        <div className="animate-pulse bg-muted rounded h-4 w-20"></div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    return (
      <div className="flex items-center space-x-2">
        <Button variant="ghost" asChild>
          <Link 
            href="/auth/signin"
            onClick={() => trackUserAction('signin_click', 'header_button')}
          >
            Увійти
          </Link>
        </Button>
        <Button asChild>
          <Link 
            href="/auth/signin"
            onClick={() => trackUserAction('signup_click', 'header_button')}
          >
            Реєстрація
          </Link>
        </Button>
      </div>
    );
  }

  if (!session?.user) {
    return null;
  }

  const user = session.user;
  const initials = user.name
    ? user.name.split(' ').map(n => n[0]).join('').toUpperCase()
    : user.email?.[0]?.toUpperCase() || 'U';

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative h-8 w-8 rounded-full">
          <Avatar className="h-8 w-8">
            <AvatarImage src={user.image || ''} alt={user.name || 'User'} />
            <AvatarFallback>{initials}</AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">{user.name}</p>
            <p className="text-xs leading-none text-muted-foreground">
              {user.email}
            </p>
            {user.provider && (
              <p className="text-xs leading-none text-muted-foreground">
                Через {user.provider === 'google' ? 'Google' : 'GitHub'}
              </p>
            )}
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        <DropdownMenuItem asChild>
          <Link 
            href="/profile" 
            className="flex items-center"
            onClick={() => handleMenuClick('profile_click', 'dropdown_menu')}
          >
            <User className="mr-2 h-4 w-4" />
            <span>Профіль</span>
          </Link>
        </DropdownMenuItem>
        
        <DropdownMenuItem asChild>
          <Link 
            href="/profile/purchases" 
            className="flex items-center"
            onClick={() => handleMenuClick('purchases_click', 'dropdown_menu')}
          >
            <ShoppingBag className="mr-2 h-4 w-4" />
            <span>Мої покупки</span>
          </Link>
        </DropdownMenuItem>
        
        <DropdownMenuItem asChild>
          <Link 
            href="/profile/favorites" 
            className="flex items-center"
            onClick={() => handleMenuClick('favorites_click', 'dropdown_menu')}
          >
            <Heart className="mr-2 h-4 w-4" />
            <span>Улюблені</span>
          </Link>
        </DropdownMenuItem>
        
        <DropdownMenuItem asChild>
          <Link 
            href="/upload" 
            className="flex items-center"
            onClick={() => handleMenuClick('upload_click', 'dropdown_menu')}
          >
            <Upload className="mr-2 h-4 w-4" />
            <span>Завантажити модель</span>
          </Link>
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem asChild>
          <Link 
            href="/settings" 
            className="flex items-center"
            onClick={() => handleMenuClick('settings_click', 'dropdown_menu')}
          >
            <Settings className="mr-2 h-4 w-4" />
            <span>Налаштування</span>
          </Link>
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem 
          onClick={handleSignOut}
          disabled={isLoading}
          className="flex items-center text-red-600 focus:text-red-600"
        >
          <LogOut className="mr-2 h-4 w-4" />
          <span>{isLoading ? 'Вихід...' : 'Вийти'}</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

/**
 * Компонент для відображення інформації про користувача
 */
export function UserInfo() {
  const { data: session, status } = useSession();

  if (status === 'loading') {
    return <div className="animate-pulse bg-muted rounded h-4 w-32"></div>;
  }

  if (status === 'unauthenticated' || !session?.user) {
    return <span className="text-muted-foreground">Гість</span>;
  }

  return (
    <div className="flex items-center space-x-2">
      <Avatar className="h-6 w-6">
        <AvatarImage src={session.user.image || ''} alt={session.user.name || 'User'} />
        <AvatarFallback className="text-xs">
          {session.user.name?.[0]?.toUpperCase() || 'U'}
        </AvatarFallback>
      </Avatar>
      <span className="text-sm font-medium">{session.user.name}</span>
    </div>
  );
}

/**
 * Hook для отримання інформації про поточного користувача
 */
export function useCurrentUser() {
  const { data: session, status } = useSession();
  
  return {
    user: session?.user || null,
    isLoading: status === 'loading',
    isAuthenticated: status === 'authenticated',
    isUnauthenticated: status === 'unauthenticated'
  };
}
