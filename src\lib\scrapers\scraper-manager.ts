import { PrintablesScraper, ScrapedModel } from './printables-scraper';
import { ThangsScraper } from './thangs-scraper';
import { thingiverseScraper } from '@/lib/api/thingiverse';
import { myMiniFactoryScraper } from '@/lib/api/myminifactory';
import { saveModelsToDatabase } from '@/lib/db';
import { jobQueueManager } from '@/lib/queue/job-queue-manager';
import { errorHandler } from '@/lib/error-handling/enhanced-error-handler';

export class ScraperManager {
  private printablesScraper: PrintablesScraper;
  private thangsScraper: ThangsScraper;

  constructor() {
    this.printablesScraper = new PrintablesScraper();
    this.thangsScraper = new ThangsScraper();
  }

  async scrapeAllPlatforms(modelsPerPlatform: number = 25): Promise<ScrapedModel[]> {
    console.log('🚀 Початок скрапінгу з усіх платформ...');

    const allModels: ScrapedModel[] = [];

    try {
      // Скрапимо з Printables
      console.log('📦 Скрапінг з Printables...');
      try {
        const printablesModels = await this.printablesScraper.scrapePopularModels(modelsPerPlatform);
        allModels.push(...printablesModels);
        console.log(`✅ Отримано ${printablesModels.length} моделей з Printables`);
      } catch (error) {
        console.error('❌ Помилка скрапінгу Printables:', error);
      }

      // Невелика затримка між платформами
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Скрапимо з Thangs
      console.log('🎯 Скрапінг з Thangs...');
      try {
        const thangsModels = await this.thangsScraper.scrapePopularModels(modelsPerPlatform);
        allModels.push(...thangsModels);
        console.log(`✅ Отримано ${thangsModels.length} моделей з Thangs`);
      } catch (error) {
        console.error('❌ Помилка скрапінгу Thangs:', error);
      }

      // Невелика затримка між платформами
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Скрапимо з Thingiverse
      console.log('🔧 Скрапінг з Thingiverse...');
      try {
        const thingiverseModels = await this.scrapeThingiversePopular(modelsPerPlatform);
        allModels.push(...thingiverseModels);
        console.log(`✅ Отримано ${thingiverseModels.length} моделей з Thingiverse`);
      } catch (error) {
        console.error('❌ Помилка скрапінгу Thingiverse:', error);
      }

      // Невелика затримка між платформами
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Скрапимо з MyMiniFactory
      console.log('🏭 Скрапінг з MyMiniFactory...');
      try {
        const myMiniFactoryModels = await this.scrapeMyMiniFactoryPopular(modelsPerPlatform);
        allModels.push(...myMiniFactoryModels);
        console.log(`✅ Отримано ${myMiniFactoryModels.length} моделей з MyMiniFactory`);
      } catch (error) {
        console.error('❌ Помилка скрапінгу MyMiniFactory:', error);
      }

    } catch (error) {
      console.error('❌ Помилка під час скрапінгу:', error);
    } finally {
      // Закриваємо браузери
      await this.printablesScraper.close();
      await this.thangsScraper.close();
    }

    console.log(`🎉 Загалом отримано ${allModels.length} моделей`);
    return allModels;
  }

  async scrapeByCategory(category: string, modelsPerPlatform: number = 15): Promise<ScrapedModel[]> {
    console.log(`🔍 Скрапінг категорії "${category}"...`);

    const allModels: ScrapedModel[] = [];

    try {
      // Скрапимо з Printables
      const printablesModels = await this.printablesScraper.scrapeByCategory(category, modelsPerPlatform);
      allModels.push(...printablesModels);

      await new Promise(resolve => setTimeout(resolve, 2000));

      // Скрапимо з Thangs
      const thangsModels = await this.thangsScraper.scrapeByCategory(category, modelsPerPlatform);
      allModels.push(...thangsModels);

    } catch (error) {
      console.error(`❌ Помилка під час скрапінгу категорії ${category}:`, error);
    } finally {
      await this.printablesScraper.close();
      await this.thangsScraper.close();
    }

    return allModels;
  }

  async searchModels(query: string, modelsPerPlatform: number = 10): Promise<ScrapedModel[]> {
    console.log(`🔎 Пошук моделей за запитом "${query}"...`);

    const allModels: ScrapedModel[] = [];

    try {
      // Пошук на Printables
      const printablesModels = await this.printablesScraper.searchModels(query, modelsPerPlatform);
      allModels.push(...printablesModels);

      await new Promise(resolve => setTimeout(resolve, 2000));

      // Пошук на Thangs
      const thangsModels = await this.thangsScraper.searchModels(query, modelsPerPlatform);
      allModels.push(...thangsModels);

    } catch (error) {
      console.error(`❌ Помилка під час пошуку "${query}":`, error);
    } finally {
      await this.printablesScraper.close();
      await this.thangsScraper.close();
    }

    return allModels;
  }

  async populateDatabase(modelsPerPlatform: number = 50): Promise<void> {
    console.log('🗄️ Наповнення бази даних...');

    try {
      // Скрапимо моделі
      const models = await this.scrapeAllPlatforms(modelsPerPlatform);

      if (models.length === 0) {
        console.log('⚠️ Не вдалося отримати моделі для збереження');
        return;
      }

      // Збережемо в базу даних
      console.log(`💾 Збереження ${models.length} моделей в базу даних...`);
      await saveModelsToDatabase(models);

      console.log('✅ База даних успішно наповнена!');
    } catch (error) {
      console.error('❌ Помилка під час наповнення бази даних:', error);
      throw error;
    }
  }

  async populateDatabaseByCategories(): Promise<void> {
    const categories = [
      'toys',
      'games',
      'miniatures',
      'household',
      'tools',
      'gadgets',
      'art',
      'jewelry',
      'automotive',
      'electronics'
    ];

    console.log('🗂️ Наповнення бази даних за категоріями...');

    for (const category of categories) {
      try {
        console.log(`📂 Обробка категорії: ${category}`);
        const models = await this.scrapeByCategory(category, 10);

        if (models.length > 0) {
          await saveModelsToDatabase(models);
          console.log(`✅ Збережено ${models.length} моделей для категорії ${category}`);
        }

        // Затримка між категоріями
        await new Promise(resolve => setTimeout(resolve, 3000));
      } catch (error) {
        console.error(`❌ Помилка для категорії ${category}:`, error);
      }
    }

    console.log('🎉 Наповнення за категоріями завершено!');
  }

  /**
   * Скрапінг популярних моделей з Thingiverse
   */
  private async scrapeThingiversePopular(count: number): Promise<ScrapedModel[]> {
    const models: ScrapedModel[] = [];
    const popularUrls = [
      'https://www.thingiverse.com/thing:3495390', // Популярна модель
      'https://www.thingiverse.com/thing:2975429',
      'https://www.thingiverse.com/thing:3148793',
      'https://www.thingiverse.com/thing:2187167',
      'https://www.thingiverse.com/thing:3364987'
    ];

    for (let i = 0; i < Math.min(count, popularUrls.length); i++) {
      try {
        const result = await thingiverseScraper.scrapeModel(popularUrls[i]);
        models.push(this.convertToScrapedModel(result));

        // Затримка між запитами
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        await errorHandler.handleError(error as Error, {
          platform: 'thingiverse',
          url: popularUrls[i]
        });
      }
    }

    return models;
  }

  /**
   * Скрапінг популярних моделей з MyMiniFactory
   */
  private async scrapeMyMiniFactoryPopular(count: number): Promise<ScrapedModel[]> {
    const models: ScrapedModel[] = [];
    const popularUrls = [
      'https://www.myminifactory.com/object/3d-print-articulated-dragon-mcgybeer-738',
      'https://www.myminifactory.com/object/3d-print-baby-groot-865',
      'https://www.myminifactory.com/object/3d-print-flexi-rex-with-stronger-links-12026',
      'https://www.myminifactory.com/object/3d-print-octopus-1953',
      'https://www.myminifactory.com/object/3d-print-low-poly-pokemon-bulbasaur-13127'
    ];

    for (let i = 0; i < Math.min(count, popularUrls.length); i++) {
      try {
        const result = await myMiniFactoryScraper.scrapeModel(popularUrls[i]);
        models.push(this.convertToScrapedModel(result));

        // Затримка між запитами
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        await errorHandler.handleError(error as Error, {
          platform: 'myminifactory',
          url: popularUrls[i]
        });
      }
    }

    return models;
  }

  /**
   * Конвертація ScrapedModel з нових скраперів до старого формату
   */
  private convertToScrapedModel(model: any): ScrapedModel {
    return {
      id: model.originalId || `${model.platform}_${Date.now()}`,
      name: model.title,
      description: model.description,
      thumbnail_url: model.thumbnail,
      model_url: model.originalUrl,
      category: model.category,
      tags: model.tags,
      author_name: model.designer.name,
      author_avatar: model.designer.avatarUrl,
      download_count: model.stats.downloads,
      like_count: model.stats.likes,
      view_count: model.stats.views,
      is_free: model.isFree || false,
      price: model.price || 0,
      file_size: model.totalSize,
      file_formats: model.fileFormats,
      print_settings: {
        layer_height: '0.2mm',
        infill: '20%',
        supports: false,
        print_time: '2h 30m'
      },
      license: model.license?.name || 'Unknown',
      created_at: model.scrapedAt,
      source_url: model.originalUrl,
      source_platform: model.platform
    };
  }

  /**
   * Додавання завдання до черги для великого імпорту
   */
  async addBatchImportJob(urls: string[], userId?: string): Promise<string> {
    return await jobQueueManager.addBatchImportJob(urls, {
      parallel: 3,
      retryFailed: true,
      includeFiles: true,
      includeImages: true,
      validateLicense: true,
      autoPublish: false
    }, {
      userId,
      priority: 2
    });
  }

  /**
   * Імпорт одиночної моделі з URL
   */
  async importSingleModel(url: string): Promise<ScrapedModel | null> {
    try {
      // Визначаємо платформу за URL
      if (url.includes('thingiverse.com')) {
        const result = await thingiverseScraper.scrapeModel(url);
        return this.convertToScrapedModel(result);
      } else if (url.includes('myminifactory.com')) {
        const result = await myMiniFactoryScraper.scrapeModel(url);
        return this.convertToScrapedModel(result);
      } else if (url.includes('printables.com')) {
        // Використовуємо існуючий скрапер
        return null; // TODO: Реалізувати
      } else if (url.includes('thangs.com')) {
        // Використовуємо існуючий скрапер
        return null; // TODO: Реалізувати
      } else {
        throw new Error('Непідтримувана платформа');
      }
    } catch (error) {
      await errorHandler.handleError(error as Error, { url });
      return null;
    }
  }

  // Метод для генерації фейкових даних якщо скрапінг не працює
  async generateFakeData(count: number = 100): Promise<ScrapedModel[]> {
    console.log(`🎭 Генерація ${count} фейкових моделей...`);

    const fakeModels: ScrapedModel[] = [];
    const categories = ['Іграшки', 'Ігри', 'Мініатюри', 'Побутові предмети', 'Інструменти', 'Гаджети', 'Мистецтво', 'Прикраси'];
    const names = [
      'Дракон-мініатюра', 'Функціональна ваза', 'Іграшковий робот', 'Декоративна фігурка',
      'Корисний гаджет', 'Настільна гра', 'Прикраса для дому', 'Інструмент для кухні',
      'Автомобільна деталь', 'Електронний корпус', 'Модний аксесуар', 'Садовий декор',
      'Освітня модель', 'Запасна частина', 'Художня скульптура', 'Практичний держак'
    ];

    for (let i = 0; i < count; i++) {
      const category = categories[Math.floor(Math.random() * categories.length)];
      const name = names[Math.floor(Math.random() * names.length)] + ` #${i + 1}`;

      const model: ScrapedModel = {
        id: `fake_${Date.now()}_${i}`,
        name,
        description: `Високоякісна 3D модель "${name}". Ідеально підходить для 3D друку та використання в проектах. Детально опрацьована та готова до друку.`,
        thumbnail_url: `https://picsum.photos/400/300?random=${i}`,
        model_url: `https://example.com/model/${i}`,
        category,
        tags: ['3d-print', 'stl', 'model', category.toLowerCase()],
        author_name: `Designer${Math.floor(Math.random() * 1000)}`,
        author_avatar: `https://picsum.photos/100/100?random=${i + 1000}`,
        download_count: Math.floor(Math.random() * 10000) + 100,
        like_count: Math.floor(Math.random() * 1000) + 10,
        view_count: Math.floor(Math.random() * 50000) + 500,
        is_free: Math.random() > 0.3, // 70% безкоштовних
        price: Math.random() > 0.3 ? 0 : Math.floor(Math.random() * 50) + 5,
        file_size: Math.floor(Math.random() * 50000000) + 1000000,
        file_formats: ['STL', 'OBJ', 'PLY'],
        print_settings: {
          layer_height: '0.2mm',
          infill: `${Math.floor(Math.random() * 30) + 10}%`,
          supports: Math.random() > 0.5,
          print_time: `${Math.floor(Math.random() * 12) + 1}h ${Math.floor(Math.random() * 60)}m`
        },
        license: 'Creative Commons',
        created_at: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
        source_url: `https://example.com/model/${i}`,
        source_platform: 'Generated'
      };

      fakeModels.push(model);
    }

    return fakeModels;
  }

  async populateWithFakeData(count: number = 100): Promise<void> {
    console.log('🎭 Наповнення бази даних фейковими даними...');

    try {
      const fakeModels = await this.generateFakeData(count);
      await saveModelsToDatabase(fakeModels);
      console.log(`✅ Збережено ${fakeModels.length} фейкових моделей в базу даних!`);
    } catch (error) {
      console.error('❌ Помилка під час збереження фейкових даних:', error);
      throw error;
    }
  }
}
