'use client';

import { SplineSceneBasic } from "@/components/demo/spline-scene-demo";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { InteractiveSpotlight } from "@/components/ui/interactive-spotlight";
import Link from "next/link";

export default function SplineDemo() {
  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-8">3D Spline Demo</h1>
      
      <div className="space-y-12">
        <section>
          <h2 className="text-2xl font-semibold mb-6">Basic Spline Scene</h2>
          <SplineSceneBasic />
          <p className="mt-4 text-muted-foreground">
            This demo showcases a 3D scene created with Spline and integrated into our UI using the SplineScene component.
          </p>
        </section>
        
        <section>
          <h2 className="text-2xl font-semibold mb-6">Interactive Card with Spotlight</h2>
          <Card className="relative overflow-hidden">
            <InteractiveSpotlight className="from-primary/20 via-primary/10 to-transparent" />
            <CardHeader>
              <CardTitle>Interactive Spotlight Effect</CardTitle>
              <CardDescription>Hover over this card to see the interactive spotlight effect</CardDescription>
            </CardHeader>
            <CardContent>
              <p>This card demonstrates the interactive spotlight effect that follows your cursor. The effect is created using Framer Motion and Tailwind CSS.</p>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">Cancel</Button>
              <Button>Submit</Button>
            </CardFooter>
          </Card>
        </section>
      </div>
      
      <div className="mt-12 text-center">
        <Link href="/">
          <Button variant="outline">Back to Home</Button>
        </Link>
      </div>
    </div>
  );
}
