import { NextRequest, NextResponse } from 'next/server';
import { queryOne, execute, generateId, getDb } from '@/lib/db';
import { z } from 'zod';
import crypto from 'crypto';

// Схема валідації для запиту на відновлення пароля
const forgotPasswordSchema = z.object({
  email: z.string().email({ message: 'Невірний формат email' }),
});

/**
 * Обробник POST-запиту для відновлення пароля
 * @param request Запит
 * @returns Відповідь
 */
export async function POST(request: NextRequest) {
  try {
    // Отримання даних з запиту
    const body = await request.json();

    // Валідація даних
    const result = forgotPasswordSchema.safeParse(body);
    if (!result.success) {
      return NextResponse.json(
        { success: false, message: result.error.errors[0].message },
        { status: 400 }
      );
    }

    const { email } = result.data;

    // Перевірка, чи доступна база даних
    const db = getDb();
    if (!db) {
      console.warn('База даних недоступна. Використовується тестовий режим відновлення пароля.');

      // Генерація токена для відновлення пароля
      const token = crypto.randomBytes(32).toString('hex');

      return NextResponse.json(
        {
          success: true,
          message: 'Посилання для відновлення пароля надіслано на вашу електронну пошту (тестовий режим)',
          // Тільки для тестування
          debug: {
            resetUrl: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/auth/reset-password?token=${token}&email=${encodeURIComponent(email)}`
          }
        },
        { status: 200 }
      );
    }

    // Перевірка, чи існує користувач з таким email
    const user = await queryOne<{ id: string }>(
      'SELECT id FROM users WHERE email = ?',
      [email]
    );

    // Навіть якщо користувач не знайдений, ми повертаємо успішну відповідь
    // для запобігання витоку інформації про існування користувача
    if (!user) {
      return NextResponse.json(
        { success: true, message: 'Якщо обліковий запис з цією адресою існує, ми надіслали посилання для відновлення пароля' },
        { status: 200 }
      );
    }

    // Генерація токена для відновлення пароля
    const token = crypto.randomBytes(32).toString('hex');
    const expires = new Date();
    expires.setHours(expires.getHours() + 1); // Токен дійсний 1 годину

    // Збереження токена в базі даних
    await execute(
      `INSERT INTO verification_tokens (identifier, token, expires_at)
       VALUES (?, ?, ?)`,
      [email, token, expires.toISOString()]
    );

    // В реальному додатку тут буде відправка електронного листа з посиланням для відновлення пароля
    // Наприклад: await sendPasswordResetEmail(email, token);

    // Для тестування ми просто повертаємо токен у відповіді
    // В реальному додатку цього робити не слід
    return NextResponse.json(
      {
        success: true,
        message: 'Посилання для відновлення пароля надіслано на вашу електронну пошту',
        // Тільки для тестування, в реальному додатку не повертайте токен
        debug: {
          resetUrl: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/auth/reset-password?token=${token}&email=${encodeURIComponent(email)}`
        }
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Помилка при запиті на відновлення пароля:', error);

    return NextResponse.json(
      { success: false, message: 'Сталася помилка при обробці запиту. Спробуйте ще раз.' },
      { status: 500 }
    );
  }
}
