# 🚀 3D Marketplace Scraper

Повноцінний скрапер для наповнення маркетплейсу 3D моделями з популярних платформ.

## 🎯 Підтримувані платформи

- **Printables.com** - Найбільша безкоштовна платформа 3D моделей
- **Thangs.com** - Професійна платформа з платними та безкоштовними моделями
- **Генератор тестових даних** - Для швидкого наповнення під час розробки

## 🛠️ Встановлення

Всі необхідні пакети вже встановлені:

```bash
npm install puppeteer cheerio @types/cheerio
```

## 🚀 Швидкий старт

### 1. Запустіть проект
```bash
npm run dev
```

### 2. Відкрийте адмін панель
Перейдіть на http://localhost:3000/admin/scraper

### 3. Згенеруйте тестові дані
Натисніть кнопку "Згенерувати тестові дані" для швидкого наповнення

## 📋 API Endpoints

### POST /api/scrape

Основний endpoint для управління скрапінгом:

```javascript
// Згенерувати тестові дані
fetch('/api/scrape', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'fake-data',
    count: 100
  })
})

// Скрапити з Printables
fetch('/api/scrape', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'scrape-printables',
    count: 50
  })
})

// Скрапити з Thangs
fetch('/api/scrape', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'scrape-thangs',
    count: 50
  })
})

// Повний скрапінг з усіх платформ
fetch('/api/scrape', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'populate',
    count: 100
  })
})

// Скрапінг за категоріями
fetch('/api/scrape', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'populate-categories'
  })
})
```

### GET /api/scraped-models

Отримання скрапованих моделей:

```javascript
// Всі моделі
fetch('/api/scraped-models')

// З фільтрами
fetch('/api/scraped-models?category=Іграшки&sort=popular&limit=20')

// Пошук
fetch('/api/scraped-models?search=дракон&sort=newest')
```

## 🎮 Використання через адмін панель

### Швидкий старт
1. **Тестові дані** - Найшвидший спосіб наповнити базу
2. Встановіть кількість моделей (за замовчуванням 50)
3. Натисніть "Згенерувати тестові дані"

### Скрапінг з платформ
1. **Printables** - Безкоштовні моделі високої якості
2. **Thangs** - Професійні моделі (платні та безкоштовні)
3. Встановіть кількість моделей для кожної платформи

### Розширені опції
1. **Повний скрапінг** - З усіх платформ одночасно
2. **За категоріями** - Детальний скрапінг по категоріях

## 📊 Структура даних

Кожна скрапована модель містить:

```typescript
interface ScrapedModel {
  id: string;                    // Унікальний ID
  name: string;                  // Назва моделі
  description: string;           // Опис
  thumbnail_url: string;         // URL зображення
  model_url: string;            // URL оригінальної моделі
  category: string;             // Категорія
  tags: string[];               // Теги
  author_name: string;          // Автор
  author_avatar: string;        // Аватар автора
  download_count: number;       // Кількість завантажень
  like_count: number;           // Кількість лайків
  view_count: number;           // Кількість переглядів
  is_free: boolean;             // Чи безкоштовна
  price: number;                // Ціна
  file_size: number;            // Розмір файлу
  file_formats: string[];       // Формати файлів
  print_settings: object;       // Налаштування друку
  license: string;              // Ліцензія
  created_at: string;           // Дата створення
  source_url: string;           // Оригінальне посилання
  source_platform: string;     // Платформа джерело
}
```

## 🔧 Налаштування скрапера

### Категорії
Скрапер автоматично мапить категорії:
- toys → Іграшки
- games → Ігри
- miniatures → Мініатюри
- household → Побутові предмети
- tools → Інструменти
- gadgets → Гаджети
- art → Мистецтво
- jewelry → Прикраси

### Затримки
- Між моделями: 1-1.5 секунди
- Між платформами: 2 секунди
- Між категоріями: 3 секунди

## 🚨 Обмеження та рекомендації

### Етичне використання
- Дотримуйтесь умов використання сайтів
- Не перевантажуйте сервери частими запитами
- Використовуйте розумні затримки

### Технічні обмеження
- Максимум 200 моделей за один запит
- Puppeteer потребує достатньо RAM
- Деякі сайти можуть блокувати автоматизацію

### Рекомендації
1. **Для розробки** - використовуйте тестові дані
2. **Для демо** - скрапіть 20-50 моделей
3. **Для продакшену** - налаштуйте кешування та оптимізацію

## 🐛 Усунення проблем

### Скрапер не працює
1. Перевірте інтернет з'єднання
2. Переконайтесь, що сайти доступні
3. Спробуйте зменшити кількість моделей

### Помилки Puppeteer
```bash
# Встановіть додаткові залежності для Linux
sudo apt-get install -y gconf-service libasound2 libatk1.0-0 libc6 libcairo2
```

### Порожні результати
1. Сайти могли змінити структуру
2. Використовуйте тестові дані як альтернативу
3. Перевірте логи в консолі браузера

## 📈 Моніторинг

### Логи
Всі операції логуються в консоль:
- 🚀 Початок скрапінгу
- 📦 Прогрес по платформах
- ✅ Успішні операції
- ❌ Помилки

### Статистика
Адмін панель показує:
- Кількість моделей в базі
- Статус останнього скрапінгу
- Поточний стан системи

## 🔄 Автоматизація

Для автоматичного оновлення можна налаштувати cron job:

```bash
# Щоденне оновлення о 2:00
0 2 * * * curl -X POST http://localhost:3000/api/scrape \
  -H "Content-Type: application/json" \
  -d '{"action":"populate","count":50}'
```

## 🎉 Готово!

Тепер у вас є повноцінний маркетплейс з реальними 3D моделями!

Перейдіть на http://localhost:3000/marketplace щоб побачити результат.
