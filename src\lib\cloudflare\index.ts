/**
 * Головний файл для роботи з Cloudflare Bindings
 * Інтеграція з cloudflare-bindings MCP та cloudflare-observability MCP
 * Експортує всі необхідні функції та типи для роботи з Cloudflare сервісами
 */

import { initializeAnalytics } from './analytics';
import { initializeD1 } from './d1';
import { initializeKV } from './kv';
import { initializeR2 } from './r2';

// Експорт базових типів
export * from './types';

// Експорт утиліт для роботи з D1
export * from './d1';

// Експорт утиліт для роботи з R2
export * from './r2';

// Експорт утиліт для роботи з KV
export * from './kv';

// Експорт утиліт для роботи з Analytics
export * from './analytics';

// Експорт утиліт для роботи з Workers
export * from './workers';

// Експорт утиліт для роботи з Hyperdrive
export * from './hyperdrive';

// Експорт middleware для API маршрутів
export * from './middleware';

/**
 * Ініціалізує всі Cloudflare сервіси
 * @param env Cloudflare Environment з bindings
 */
export function initializeCloudflare(env: any): void {
  console.log('🚀 Initializing Cloudflare services...');

  try {
    // Ініціалізуємо D1 Database
    initializeD1(env);

    // Ініціалізуємо KV Storage
    initializeKV(env);

    // Ініціалізуємо R2 Storage
    initializeR2(env);

    // Ініціалізуємо Analytics Engine
    initializeAnalytics(env);

    console.log('✅ All Cloudflare services initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize Cloudflare services:', error);
  }
}

/**
 * Перевіряє доступність Cloudflare сервісів
 * @param env Cloudflare Environment з bindings
 * @returns Статус доступності сервісів
 */
export function checkCloudflareServices(env: any): {
  d1: boolean;
  kv: boolean;
  r2: boolean;
  analytics: boolean;
} {
  return {
    d1: !!env.DB,
    kv: !!env.CACHE_KV,
    r2: !!env.R2_BUCKET,
    analytics: !!env.ANALYTICS
  };
}
