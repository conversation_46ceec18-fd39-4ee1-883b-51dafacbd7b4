'use client';

import React, { useState, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import {
  Upload,
  X,
  Plus,
  FileText,
  ImageIcon,
  ArrowLeft,
  ArrowRight,
  Check,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Alert, AlertDescription } from '@/components/ui/alert';

// Категорії для вибору
const CATEGORIES = [
  'Art',
  'Fashion',
  'Gadgets',
  'Home',
  'Toys',
  'Tools',
  'Figurines',
  'Educational',
  'Other',
];

// Ліцензії для вибору
const LICENSES = [
  { value: 'standard', label: 'Стандартна - Тільки для особистого використання' },
  { value: 'commercial', label: 'Комерційна - Особисте та комерційне використання' },
  { value: 'creative-commons', label: 'Creative Commons - Attribution' },
  { value: 'custom', label: 'Власна ліцензія (вказати в описі)' },
];

export default function UploadModel() {
  const { data: session, status } = useSession();
  const router = useRouter();

  const [currentStep, setCurrentStep] = useState(1);
  const [modelFiles, setModelFiles] = useState<File[]>([]);
  const [thumbnails, setThumbnails] = useState<File[]>([]);
  const [thumbnailPreviews, setThumbnailPreviews] = useState<string[]>([]);
  const [modelName, setModelName] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('');
  const [tags, setTags] = useState('');
  const [price, setPrice] = useState('0');
  const [license, setLicense] = useState('standard');
  const [printSettings, setPrintSettings] = useState({
    material: '',
    layerHeight: '',
    infill: '',
    supports: '',
    rafts: '',
  });

  // Loading and error states
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const modelFileInputRef = useRef<HTMLInputElement>(null);
  const thumbnailInputRef = useRef<HTMLInputElement>(null);

  // Redirect if not authenticated
  if (status === 'loading') {
    return <div className="flex justify-center items-center min-h-screen">
      <Loader2 className="h-8 w-8 animate-spin" />
    </div>;
  }

  if (status === 'unauthenticated') {
    router.push('/auth/signin');
    return null;
  }

  // Обробка завантаження файлів моделі
  const handleModelFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files);
      setModelFiles([...modelFiles, ...newFiles]);
    }
  };

  // Обробка завантаження зображень
  const handleThumbnailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files);
      setThumbnails([...thumbnails, ...newFiles]);

      // Створення URL для попереднього перегляду
      const newPreviews = newFiles.map(file => URL.createObjectURL(file));
      setThumbnailPreviews([...thumbnailPreviews, ...newPreviews]);
    }
  };

  // Видалення файлу моделі
  const removeModelFile = (index: number) => {
    setModelFiles(modelFiles.filter((_, i) => i !== index));
  };

  // Видалення зображення
  const removeThumbnail = (index: number) => {
    // Звільнення URL для попереднього перегляду
    URL.revokeObjectURL(thumbnailPreviews[index]);

    setThumbnails(thumbnails.filter((_, i) => i !== index));
    setThumbnailPreviews(thumbnailPreviews.filter((_, i) => i !== index));
  };

  // Оновлення налаштувань друку
  const updatePrintSettings = (key: keyof typeof printSettings, value: string) => {
    setPrintSettings({
      ...printSettings,
      [key]: value,
    });
  };

  // Перевірка, чи можна перейти до наступного кроку
  const canProceedToNextStep = () => {
    if (currentStep === 1) {
      return modelFiles.length > 0;
    }
    if (currentStep === 2) {
      return thumbnails.length > 0;
    }
    if (currentStep === 3) {
      return modelName && description && category;
    }
    return true;
  };

  // Перехід до наступного кроку
  const goToNextStep = () => {
    if (canProceedToNextStep()) {
      setCurrentStep(currentStep + 1);
    }
  };

  // Перехід до попереднього кроку
  const goToPreviousStep = () => {
    setCurrentStep(currentStep - 1);
  };

  // Функція завантаження файлів
  const uploadFiles = async (files: File[], type: 'model' | 'thumbnail'): Promise<string[]> => {
    const uploadedUrls: string[] = [];

    for (const file of files) {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', type);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Failed to upload ${file.name}`);
      }

      const result = await response.json();
      uploadedUrls.push(result.url);
    }

    return uploadedUrls;
  };

  // Відправка форми
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsUploading(true);
    setError(null);
    setUploadProgress(0);

    try {
      // Крок 1: Завантаження файлів моделі (30%)
      setUploadProgress(10);
      const modelUrls = await uploadFiles(modelFiles, 'model');
      setUploadProgress(30);

      // Крок 2: Завантаження зображень (60%)
      const thumbnailUrls = await uploadFiles(thumbnails, 'thumbnail');
      setUploadProgress(60);

      // Крок 3: Створення моделі в базі даних (90%)
      const modelData = {
        name: modelName,
        description,
        category,
        tags: tags.split(',').map(tag => tag.trim()).filter(Boolean),
        price: parseFloat(price),
        is_free: parseFloat(price) === 0,
        license,
        model_url: modelUrls[0], // Основний файл моделі
        thumbnail_url: thumbnailUrls[0], // Основне зображення
        additional_files: modelUrls.slice(1), // Додаткові файли
        additional_images: thumbnailUrls.slice(1), // Додаткові зображення
        print_settings: printSettings,
      };

      const response = await fetch('/api/models', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(modelData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create model');
      }

      const result = await response.json();
      setUploadProgress(100);
      setSuccess(true);

      // Перенаправлення на сторінку моделі через 2 секунди
      setTimeout(() => {
        router.push(`/models/${result.data.id}`);
      }, 2000);

    } catch (error) {
      console.error('Upload error:', error);
      setError(error instanceof Error ? error.message : 'Failed to upload model');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Завантажити 3D модель</h1>
        <p className="text-gray-600">Поділіться своїми дизайнами зі спільнотою або продавайте їх іншим творцям.</p>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert className="mb-6 border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Success Alert */}
      {success && (
        <Alert className="mb-6 border-green-200 bg-green-50">
          <Check className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            Модель успішно завантажена! Перенаправляємо на сторінку моделі...
          </AlertDescription>
        </Alert>
      )}

      {/* Upload Progress */}
      {isUploading && (
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-4">
              <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
              <div className="flex-1">
                <div className="flex justify-between text-sm mb-2">
                  <span>Завантаження моделі...</span>
                  <span>{uploadProgress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center">
          <div className={`flex items-center justify-center w-10 h-10 rounded-full ${
            currentStep >= 1 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'
          }`}>
            1
          </div>
          <div className={`flex-1 h-1 mx-2 ${currentStep >= 2 ? 'bg-blue-600' : 'bg-gray-200'}`}></div>
          <div className={`flex items-center justify-center w-10 h-10 rounded-full ${
            currentStep >= 2 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'
          }`}>
            2
          </div>
          <div className={`flex-1 h-1 mx-2 ${currentStep >= 3 ? 'bg-blue-600' : 'bg-gray-200'}`}></div>
          <div className={`flex items-center justify-center w-10 h-10 rounded-full ${
            currentStep >= 3 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'
          }`}>
            3
          </div>
        </div>
        <div className="flex justify-between mt-2 text-sm">
          <div className="text-center w-10">Files</div>
          <div className="text-center w-10">Details</div>
          <div className="text-center w-10">Publish</div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <form onSubmit={handleSubmit}>
          {/* Step 1: Upload Files */}
          {currentStep === 1 && (
            <div>
              <h2 className="text-xl font-semibold mb-4">Upload Your 3D Model Files</h2>

              <div className="mb-6">
                <label className="block text-gray-700 font-medium mb-2">
                  3D Model Files (STL, OBJ, 3MF, etc.)
                </label>
                <div
                  className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:bg-gray-50 transition-colors"
                  onClick={() => modelFileInputRef.current?.click()}
                >
                  <input
                    type="file"
                    ref={modelFileInputRef}
                    onChange={handleModelFileChange}
                    accept=".stl,.obj,.3mf,.gltf,.glb"
                    multiple
                    className="hidden"
                  />

                  {modelFiles.length === 0 ? (
                    <div>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                      </svg>
                      <p className="text-gray-700 mb-1">Drag and drop your 3D model files here</p>
                      <p className="text-gray-500 text-sm">or click to browse</p>
                    </div>
                  ) : (
                    <div>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-green-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <p className="text-gray-700 mb-1">{modelFiles.length} file(s) selected</p>
                      <ul className="text-sm text-gray-500">
                        {modelFiles.map((file, index) => (
                          <li key={index}>{file.name}</li>
                        ))}
                      </ul>
                      <button
                        type="button"
                        className="mt-4 text-blue-600 hover:underline text-sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          setModelFiles([]);
                        }}
                      >
                        Remove files
                      </button>
                    </div>
                  )}
                </div>
              </div>

              <div className="mb-6">
                <label className="block text-gray-700 font-medium mb-2">
                  Thumbnail Images (PNG, JPG)
                </label>
                <div
                  className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:bg-gray-50 transition-colors"
                  onClick={() => thumbnailInputRef.current?.click()}
                >
                  <input
                    type="file"
                    ref={thumbnailInputRef}
                    onChange={handleThumbnailChange}
                    accept="image/*"
                    multiple
                    className="hidden"
                  />

                  {thumbnails.length === 0 ? (
                    <div>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <p className="text-gray-700 mb-1">Upload thumbnail images of your model</p>
                      <p className="text-gray-500 text-sm">or click to browse</p>
                    </div>
                  ) : (
                    <div>
                      <div className="flex flex-wrap justify-center gap-4 mb-4">
                        {thumbnailPreviews.map((preview, index) => (
                          <div key={index} className="relative w-24 h-24">
                            <Image
                              src={preview}
                              alt={`Thumbnail ${index + 1}`}
                              fill
                              style={{ objectFit: 'cover' }}
                              className="rounded-lg"
                            />
                          </div>
                        ))}
                      </div>
                      <button
                        type="button"
                        className="text-blue-600 hover:underline text-sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          setThumbnails([]);
                          setThumbnailPreviews([]);
                        }}
                      >
                        Remove images
                      </button>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex justify-end">
                <Button
                  type="button"
                  onClick={() => setCurrentStep(2)}
                  disabled={modelFiles.length === 0 || thumbnails.length === 0 || isUploading}
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed"
                >
                  Далі: Деталі моделі
                </Button>
              </div>
            </div>
          )}

          {/* Step 2: Model Details */}
          {currentStep === 2 && (
            <div>
              <h2 className="text-xl font-semibold mb-4">Model Details</h2>

              <div className="grid grid-cols-1 gap-6">
                <div>
                  <label htmlFor="modelName" className="block text-gray-700 font-medium mb-2">
                    Model Name *
                  </label>
                  <input
                    type="text"
                    id="modelName"
                    value={modelName}
                    onChange={(e) => setModelName(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="description" className="block text-gray-700 font-medium mb-2">
                    Description *
                  </label>
                  <textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    rows={4}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                    required
                  ></textarea>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="category" className="block text-gray-700 font-medium mb-2">
                      Category *
                    </label>
                    <select
                      id="category"
                      value={category}
                      onChange={(e) => setCategory(e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                      required
                    >
                      <option value="">Select a category</option>
                      {CATEGORIES.map((cat) => (
                        <option key={cat} value={cat}>{cat}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label htmlFor="tags" className="block text-gray-700 font-medium mb-2">
                      Tags (comma separated)
                    </label>
                    <input
                      type="text"
                      id="tags"
                      value={tags}
                      onChange={(e) => setTags(e.target.value)}
                      placeholder="e.g. skull, halloween, decoration"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="price" className="block text-gray-700 font-medium mb-2">
                      Price (USD) *
                    </label>
                    <input
                      type="number"
                      id="price"
                      value={price}
                      onChange={(e) => setPrice(e.target.value)}
                      min="0"
                      step="0.01"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                    <p className="text-sm text-gray-500 mt-1">Set to 0 for free models</p>
                  </div>

                  <div>
                    <label htmlFor="license" className="block text-gray-700 font-medium mb-2">
                      License *
                    </label>
                    <select
                      id="license"
                      value={license}
                      onChange={(e) => setLicense(e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                      required
                    >
                      {LICENSES.map((lic) => (
                        <option key={lic.value} value={lic.value}>{lic.label}</option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>

              <div className="flex justify-between mt-8">
                <Button
                  type="button"
                  onClick={() => setCurrentStep(1)}
                  disabled={isUploading}
                  variant="outline"
                >
                  Назад
                </Button>
                <Button
                  type="button"
                  onClick={() => setCurrentStep(3)}
                  disabled={!modelName || !description || !category || isUploading}
                >
                  Далі: Налаштування друку
                </Button>
              </div>
            </div>
          )}

          {/* Step 3: Print Settings and Publish */}
          {currentStep === 3 && (
            <div>
              <h2 className="text-xl font-semibold mb-4">Print Settings</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div>
                  <label htmlFor="material" className="block text-gray-700 font-medium mb-2">
                    Recommended Materials
                  </label>
                  <input
                    type="text"
                    id="material"
                    value={printSettings.material}
                    onChange={(e) => setPrintSettings({...printSettings, material: e.target.value})}
                    placeholder="e.g. PLA, PETG"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label htmlFor="layerHeight" className="block text-gray-700 font-medium mb-2">
                    Layer Height
                  </label>
                  <input
                    type="text"
                    id="layerHeight"
                    value={printSettings.layerHeight}
                    onChange={(e) => setPrintSettings({...printSettings, layerHeight: e.target.value})}
                    placeholder="e.g. 0.2mm"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label htmlFor="infill" className="block text-gray-700 font-medium mb-2">
                    Recommended Infill
                  </label>
                  <input
                    type="text"
                    id="infill"
                    value={printSettings.infill}
                    onChange={(e) => setPrintSettings({...printSettings, infill: e.target.value})}
                    placeholder="e.g. 15-20%"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label htmlFor="supports" className="block text-gray-700 font-medium mb-2">
                    Supports Required
                  </label>
                  <input
                    type="text"
                    id="supports"
                    value={printSettings.supports}
                    onChange={(e) => setPrintSettings({...printSettings, supports: e.target.value})}
                    placeholder="e.g. Minimal, None, Yes"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
                <h3 className="font-medium text-blue-800 mb-2">Ready to Publish</h3>
                <p className="text-blue-700 text-sm">
                  By publishing this model, you confirm that you own the rights to this content or have permission to share it under the selected license.
                </p>
              </div>

              <div className="flex justify-between">
                <Button
                  type="button"
                  onClick={() => setCurrentStep(2)}
                  disabled={isUploading}
                  variant="outline"
                >
                  Назад
                </Button>
                <Button
                  type="submit"
                  disabled={isUploading}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isUploading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Завантаження...
                    </>
                  ) : (
                    'Опублікувати модель'
                  )}
                </Button>
              </div>
            </div>
          )}
        </form>
      </div>
    </main>
  );
}
