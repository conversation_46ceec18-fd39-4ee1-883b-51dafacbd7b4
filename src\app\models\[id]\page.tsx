'use client';

import React, { useState } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import {
  Heart,
  Download,
  Share2,
  ShoppingCart,
  Printer,
  ChevronLeft,
  Tag,
  Info,
  MessageSquare,
  Star,
  Clock,
  FileText,
  ArrowLeft
} from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { ModelViewer } from '@/components/model-viewer/model-viewer';
import RecommendedModels from '@/components/recommended-models';

// Тимчасові дані для демонстрації
const MODEL_DATA = {
  id: '1',
  name: 'Articulated Dragon',
  description: 'A fully Articulated Dragon model with movable joints. Perfect for testing your printer\'s capabilities and as a desk toy.',
  splineSceneUrl: 'https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode',
  thumbnailUrl: '/models/dragon-thumbnail.jpg',
  images: [
    'https://images.unsplash.com/photo-1581235720704-06d3acfcb36f?q=80&w=1935&auto=format&fit=crop',
    'https://images.unsplash.com/photo-1581094794329-c8112a89af12?q=80&w=2070&auto=format&fit=crop',
    'https://images.unsplash.com/photo-1535350356005-fd52b3b524fb?q=80&w=2070&auto=format&fit=crop',
  ],
  fileSize: '15.2 MB',
  fileFormat: 'STL',
  category: 'Toys',
  tags: ['Dragon', 'Articulated', 'Fantasy', 'Desk Toy'],
  creator: {
    name: 'DragonMaster3D',
    avatarUrl: 'https://randomuser.me/api/portraits/men/32.jpg',
    models: 48,
    followers: 1250,
  },
  price: 'Free' as const,
  downloadCount: 12543,
  likes: 3254,
  createdAt: '2023-05-15',
  printSettings: {
    material: 'PLA',
    layerHeight: '0.2mm',
    infill: '20%',
    supports: 'Minimal',
    printTime: '8h 45m',
  },
  license: 'Creative Commons - Attribution',
};

// Тимчасові дані для відгуків
const REVIEWS = [
  {
    id: 1,
    user: {
      name: 'Alex Johnson',
      avatar: 'https://randomuser.me/api/portraits/men/42.jpg',
    },
    rating: 5,
    date: '2023-06-10',
    comment: 'Amazing model! Printed perfectly on my Ender 3 with minimal supports. The articulation works great.',
    images: ['https://images.unsplash.com/photo-1581235720704-06d3acfcb36f?q=80&w=1935&auto=format&fit=crop'],
  },
  {
    id: 2,
    user: {
      name: 'Sarah Miller',
      avatar: 'https://randomuser.me/api/portraits/women/24.jpg',
    },
    rating: 4,
    date: '2023-05-22',
    comment: 'Great design, but I had some issues with the tail. Overall very happy with the result.',
    images: [],
  },
];

export default function ModelDetailPage() {
  const params = useParams();
  const id = params.id;
  const [activeTab, setActiveTab] = useState('overview');
  const [activeImageIndex, setActiveImageIndex] = useState(0);

  // В реальному додатку тут буде запит до API для отримання даних моделі за ID
  const model = MODEL_DATA;

  return (
    <main className="container mx-auto py-8 px-4">
      {/* Навігаційний хлібний крихти */}
      <div className="mb-6">
        <Link href="/models" className="flex items-center text-muted-foreground hover:text-primary transition-colors">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Назад до моделей
        </Link>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Ліва колонка - 3D модель та зображення */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardContent className="p-0 overflow-hidden">
              <div className="h-[500px] relative">
                <ModelViewer
                  model={model}
                />
              </div>
            </CardContent>
          </Card>

          {/* Галерея зображень */}
          <div className="grid grid-cols-4 gap-2">
            {model.images.map((image, index) => (
              <div
                key={index}
                className={`relative aspect-square rounded-md overflow-hidden cursor-pointer border-2 ${
                  activeImageIndex === index ? 'border-primary' : 'border-transparent'
                }`}
                onClick={() => setActiveImageIndex(index)}
              >
                <Image
                  src={image}
                  alt={`${model.name} - Image ${index + 1}`}
                  fill
                  className="object-cover"
                />
              </div>
            ))}
          </div>
        </div>

        {/* Права колонка - інформація про модель */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-2xl">{model.name}</CardTitle>
                  <CardDescription className="mt-2">
                    <Link href={`/designers/${model.creator.name}`} className="flex items-center text-sm hover:text-primary">
                      <img
                        src={model.creator.avatarUrl}
                        alt={model.creator.name}
                        className="rounded-full mr-2 w-6 h-6"
                      />
                      {model.creator.name}
                    </Link>
                  </CardDescription>
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" size="icon">
                    <Heart className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="icon">
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between mb-6">
                <div className="text-2xl font-bold">
                  {model.price === 'Free' ? 'Free' : `$${model.price}`}
                </div>
                <div className="flex items-center space-x-4 text-muted-foreground">
                  <div className="flex items-center">
                    <Heart className="h-4 w-4 mr-1 text-red-500" />
                    <span>{model.likes}</span>
                  </div>
                  <div className="flex items-center">
                    <Download className="h-4 w-4 mr-1" />
                    <span>{model.downloadCount}</span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <Button className="w-full">
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>

                <Button variant="outline" className="w-full">
                  <Printer className="h-4 w-4 mr-2" />
                  Order Print
                </Button>
              </div>

              <div className="mt-6 space-y-4">
                <div className="flex items-start">
                  <FileText className="h-4 w-4 mr-2 mt-1 text-muted-foreground" />
                  <div>
                    <div className="text-sm font-medium">File Format</div>
                    <div className="text-sm text-muted-foreground">{model.fileFormat} ({model.fileSize})</div>
                  </div>
                </div>

                <div className="flex items-start">
                  <Tag className="h-4 w-4 mr-2 mt-1 text-muted-foreground" />
                  <div>
                    <div className="text-sm font-medium">License</div>
                    <div className="text-sm text-muted-foreground">{model.license}</div>
                  </div>
                </div>

                <div className="flex items-start">
                  <Clock className="h-4 w-4 mr-2 mt-1 text-muted-foreground" />
                  <div>
                    <div className="text-sm font-medium">Published</div>
                    <div className="text-sm text-muted-foreground">{model.createdAt}</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Теги</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {model.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary">
                    {tag}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Tabs with additional information */}
      <div className="mt-8">
        <Tabs defaultValue="overview" onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="print-settings">Print Settings</TabsTrigger>
            <TabsTrigger value="reviews">Reviews</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Description</CardTitle>
              </CardHeader>
              <CardContent>
                <p>{model.description}</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="print-settings" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Recommended Print Settings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm font-medium">Material</div>
                    <div className="text-sm text-muted-foreground">{model.printSettings.material}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium">Layer Height</div>
                    <div className="text-sm text-muted-foreground">{model.printSettings.layerHeight}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium">Infill</div>
                    <div className="text-sm text-muted-foreground">{model.printSettings.infill}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium">Supports</div>
                    <div className="text-sm text-muted-foreground">{model.printSettings.supports}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium">Print Time</div>
                    <div className="text-sm text-muted-foreground">{model.printSettings.printTime}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="reviews" className="mt-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Reviews</CardTitle>
                <Button>Write a Review</Button>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {REVIEWS.map((review) => (
                    <div key={review.id} className="pb-6 border-b last:border-0">
                      <div className="flex justify-between items-start mb-2">
                        <div className="flex items-center">
                          <img
                            src={review.user.avatar}
                            alt={review.user.name}
                            className="rounded-full mr-3 w-10 h-10"
                          />
                          <div>
                            <div className="font-medium">{review.user.name}</div>
                            <div className="text-sm text-muted-foreground">{review.date}</div>
                          </div>
                        </div>
                        <div className="flex">
                          {Array.from({ length: 5 }).map((_, i) => (
                            <Star
                              key={i}
                              className={`h-4 w-4 ${i < review.rating ? 'text-yellow-400 fill-yellow-400' : 'text-muted-foreground'}`}
                            />
                          ))}
                        </div>
                      </div>
                      <p className="text-sm mb-3">{review.comment}</p>
                      {review.images.length > 0 && (
                        <div className="flex gap-2 mt-3">
                          {review.images.map((image, index) => (
                            <div key={index} className="relative w-20 h-20 rounded-md overflow-hidden">
                              <img
                                src={image}
                                alt={`Review image ${index + 1}`}
                                className="w-full h-full object-cover"
                              />
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Recommended models */}
      <RecommendedModels
        models={[
          {
            id: '2',
            title: 'Smartphone Stand',
            thumbnail: 'https://images.unsplash.com/photo-1517420704952-d9f39e95b43e?q=80&w=1854&auto=format&fit=crop',
            designer: 'TechPrints',
            price: 0,
            likes: 189,
            downloads: 3400,
          },
          {
            id: '4',
            title: 'Dragon Figurine',
            thumbnail: 'https://images.unsplash.com/photo-1581235720704-06d3acfcb36f?q=80&w=1935&auto=format&fit=crop',
            designer: 'FantasyCreations',
            price: 9.99,
            likes: 521,
            downloads: 1500,
          },
          {
            id: '5',
            title: 'Planter Pot',
            thumbnail: 'https://images.unsplash.com/photo-1593062096033-9a26b09da705?q=80&w=2070&auto=format&fit=crop',
            designer: 'GreenThumb',
            price: 3.99,
            likes: 98,
            downloads: 650,
          },
          {
            id: '8',
            title: 'Vase with Pattern',
            thumbnail: 'https://images.unsplash.com/photo-1513384312027-9fa69a360337?q=80&w=2080&auto=format&fit=crop',
            designer: 'HomeDecor',
            price: 5.99,
            likes: 421,
            downloads: 2100,
          },
        ]}
        title="Similar Models"
        className="mt-12"
      />
    </main>
  );
}
