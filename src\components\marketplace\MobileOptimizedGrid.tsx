'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Filter, 
  Grid3X3, 
  List, 
  Heart, 
  Download, 
  Share2, 
  Eye,
  Star,
  DollarSign,
  User,
  Calendar,
  FileText,
  Smartphone
} from 'lucide-react';
import { Model, ModelsQueryParams } from '@/types/models';
import AdvancedFilters from './AdvancedFilters';
import EnhancedModelViewer from '../3d-viewer/EnhancedModelViewer';

interface MobileOptimizedGridProps {
  models: Model[];
  filters: ModelsQueryParams;
  onFiltersChange: (filters: ModelsQueryParams) => void;
  onResetFilters: () => void;
  isLoading?: boolean;
  onModelSelect?: (model: Model) => void;
  availableCategories?: string[];
  availableTags?: string[];
  availableFileFormats?: string[];
}

type ViewMode = 'grid' | 'list';

export default function MobileOptimizedGrid({
  models,
  filters,
  onFiltersChange,
  onResetFilters,
  isLoading = false,
  onModelSelect,
  availableCategories = [],
  availableTags = [],
  availableFileFormats = []
}: MobileOptimizedGridProps) {
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [selectedModel, setSelectedModel] = useState<Model | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [showFilters, setShowFilters] = useState(false);

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const handleModelClick = (model: Model) => {
    setSelectedModel(model);
    onModelSelect?.(model);
  };

  const formatPrice = (price: number, isFree?: boolean) => {
    if (isFree || price === 0) return 'Безкоштовно';
    return `$${price.toFixed(2)}`;
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.search) count++;
    if (filters.category) count++;
    if (filters.minPrice || filters.maxPrice) count++;
    if (filters.tags?.length) count += filters.tags.length;
    if (filters.source) count++;
    if (filters.dateRange && filters.dateRange !== 'all') count++;
    if (filters.isFree !== undefined) count++;
    if (filters.minDownloads) count++;
    if (filters.fileFormats?.length) count += filters.fileFormats.length;
    return count;
  };

  const ModelCard = ({ model }: { model: Model }) => (
    <Card 
      className={`overflow-hidden cursor-pointer transition-all duration-200 hover:shadow-lg ${
        isMobile ? 'active:scale-95' : 'hover:scale-105'
      }`}
      onClick={() => handleModelClick(model)}
    >
      {/* Image */}
      <div className="relative aspect-square bg-gray-100">
        <img
          src={model.thumbnail}
          alt={model.title}
          className="w-full h-full object-cover"
          loading="lazy"
        />
        
        {/* Overlay badges */}
        <div className="absolute top-2 left-2 flex flex-col gap-1">
          {model.isFeatured && (
            <Badge className="bg-yellow-500 text-white text-xs">
              ⭐ Рекомендовано
            </Badge>
          )}
          {model.isFree && (
            <Badge className="bg-green-500 text-white text-xs">
              Безкоштовно
            </Badge>
          )}
        </div>

        {/* Quick actions */}
        <div className="absolute top-2 right-2 flex flex-col gap-1">
          <Button
            size="sm"
            variant="secondary"
            className="w-8 h-8 p-0 bg-white/80 backdrop-blur-sm"
            onClick={(e) => {
              e.stopPropagation();
              // Handle favorite
            }}
          >
            <Heart className="w-4 h-4" />
          </Button>
          <Button
            size="sm"
            variant="secondary"
            className="w-8 h-8 p-0 bg-white/80 backdrop-blur-sm"
            onClick={(e) => {
              e.stopPropagation();
              // Handle share
            }}
          >
            <Share2 className="w-4 h-4" />
          </Button>
        </div>

        {/* Platform badge */}
        <div className="absolute bottom-2 left-2">
          <Badge variant="outline" className="bg-white/80 backdrop-blur-sm text-xs">
            {model.source}
          </Badge>
        </div>
      </div>

      {/* Content */}
      <div className="p-3 space-y-2">
        <div>
          <h3 className="font-semibold text-sm line-clamp-2 leading-tight">
            {model.title}
          </h3>
          <p className="text-xs text-gray-600 flex items-center gap-1 mt-1">
            <User className="w-3 h-3" />
            {model.designer.name}
          </p>
        </div>

        {/* Stats */}
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center gap-3">
            <span className="flex items-center gap-1">
              <Eye className="w-3 h-3" />
              {formatNumber(model.likes)}
            </span>
            <span className="flex items-center gap-1">
              <Download className="w-3 h-3" />
              {formatNumber(model.downloads)}
            </span>
          </div>
          <div className="font-semibold text-primary">
            {formatPrice(model.price, model.isFree)}
          </div>
        </div>

        {/* Tags */}
        {model.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {model.tags.slice(0, 2).map(tag => (
              <Badge key={tag} variant="secondary" className="text-xs px-1 py-0">
                {tag}
              </Badge>
            ))}
            {model.tags.length > 2 && (
              <Badge variant="secondary" className="text-xs px-1 py-0">
                +{model.tags.length - 2}
              </Badge>
            )}
          </div>
        )}
      </div>
    </Card>
  );

  const ModelListItem = ({ model }: { model: Model }) => (
    <Card 
      className="p-3 cursor-pointer transition-all duration-200 hover:shadow-md"
      onClick={() => handleModelClick(model)}
    >
      <div className="flex gap-3">
        {/* Thumbnail */}
        <div className="w-16 h-16 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
          <img
            src={model.thumbnail}
            alt={model.title}
            className="w-full h-full object-cover"
            loading="lazy"
          />
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0 space-y-1">
          <div className="flex items-start justify-between">
            <h3 className="font-semibold text-sm line-clamp-1">
              {model.title}
            </h3>
            <div className="text-sm font-semibold text-primary ml-2">
              {formatPrice(model.price, model.isFree)}
            </div>
          </div>

          <p className="text-xs text-gray-600 flex items-center gap-1">
            <User className="w-3 h-3" />
            {model.designer.name}
          </p>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3 text-xs text-gray-500">
              <span className="flex items-center gap-1">
                <Eye className="w-3 h-3" />
                {formatNumber(model.likes)}
              </span>
              <span className="flex items-center gap-1">
                <Download className="w-3 h-3" />
                {formatNumber(model.downloads)}
              </span>
            </div>
            
            <div className="flex gap-1">
              {model.isFeatured && (
                <Badge className="bg-yellow-500 text-white text-xs">⭐</Badge>
              )}
              {model.isFree && (
                <Badge className="bg-green-500 text-white text-xs">Free</Badge>
              )}
            </div>
          </div>
        </div>
      </div>
    </Card>
  );

  return (
    <div className="space-y-4">
      {/* Header with controls */}
      <div className="flex items-center justify-between gap-2">
        {/* Filters button for mobile */}
        {isMobile ? (
          <Sheet open={showFilters} onOpenChange={setShowFilters}>
            <SheetTrigger asChild>
              <Button variant="outline" size="sm" className="flex items-center gap-2">
                <Filter className="w-4 h-4" />
                Фільтри
                {getActiveFiltersCount() > 0 && (
                  <Badge variant="secondary" className="ml-1">
                    {getActiveFiltersCount()}
                  </Badge>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-80 overflow-y-auto">
              <SheetHeader>
                <SheetTitle>Фільтри</SheetTitle>
                <SheetDescription>
                  Налаштуйте параметри пошуку моделей
                </SheetDescription>
              </SheetHeader>
              <div className="mt-4">
                <AdvancedFilters
                  filters={filters}
                  onFiltersChange={onFiltersChange}
                  onReset={onResetFilters}
                  availableCategories={availableCategories}
                  availableTags={availableTags}
                  availableFileFormats={availableFileFormats}
                  isLoading={isLoading}
                />
              </div>
            </SheetContent>
          </Sheet>
        ) : (
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4" />
            <span className="text-sm font-medium">Фільтри активні:</span>
            {getActiveFiltersCount() > 0 && (
              <Badge variant="secondary">
                {getActiveFiltersCount()}
              </Badge>
            )}
          </div>
        )}

        {/* View mode toggle */}
        <div className="flex items-center gap-1 bg-gray-100 rounded-lg p-1">
          <Button
            size="sm"
            variant={viewMode === 'grid' ? 'default' : 'ghost'}
            className="w-8 h-8 p-0"
            onClick={() => setViewMode('grid')}
          >
            <Grid3X3 className="w-4 h-4" />
          </Button>
          <Button
            size="sm"
            variant={viewMode === 'list' ? 'default' : 'ghost'}
            className="w-8 h-8 p-0"
            onClick={() => setViewMode('list')}
          >
            <List className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Models grid/list */}
      {isLoading ? (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {Array.from({ length: 8 }).map((_, i) => (
            <Card key={i} className="overflow-hidden">
              <div className="aspect-square bg-gray-200 animate-pulse" />
              <div className="p-3 space-y-2">
                <div className="h-4 bg-gray-200 rounded animate-pulse" />
                <div className="h-3 bg-gray-200 rounded animate-pulse w-2/3" />
              </div>
            </Card>
          ))}
        </div>
      ) : (
        <div className={
          viewMode === 'grid' 
            ? `grid gap-4 ${isMobile ? 'grid-cols-2' : 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5'}`
            : 'space-y-3'
        }>
          {models.map(model => 
            viewMode === 'grid' ? (
              <ModelCard key={model.id} model={model} />
            ) : (
              <ModelListItem key={model.id} model={model} />
            )
          )}
        </div>
      )}

      {/* Model detail modal */}
      {selectedModel && (
        <Dialog open={!!selectedModel} onOpenChange={() => setSelectedModel(null)}>
          <DialogContent className={`${isMobile ? 'w-full h-full max-w-none' : 'max-w-4xl'}`}>
            <DialogHeader>
              <DialogTitle className="line-clamp-1">{selectedModel.title}</DialogTitle>
              <DialogDescription>
                від {selectedModel.designer.name}
              </DialogDescription>
            </DialogHeader>
            
            <div className={`${isMobile ? 'space-y-4' : 'grid grid-cols-2 gap-6'}`}>
              {/* 3D Viewer */}
              <div className={isMobile ? 'h-64' : 'h-96'}>
                <EnhancedModelViewer
                  modelUrl={selectedModel.downloadUrl || '/models/sample.glb'}
                  showControls={!isMobile}
                  enableFullscreen={!isMobile}
                />
              </div>

              {/* Model info */}
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-lg">{selectedModel.title}</h3>
                  <p className="text-gray-600">{selectedModel.description}</p>
                </div>

                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold text-primary">
                    {formatPrice(selectedModel.price, selectedModel.isFree)}
                  </div>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline">
                      <Heart className="w-4 h-4" />
                    </Button>
                    <Button size="sm" variant="outline">
                      <Share2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Завантажень:</span>
                    <span className="ml-2 font-medium">{formatNumber(selectedModel.downloads)}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Вподобань:</span>
                    <span className="ml-2 font-medium">{formatNumber(selectedModel.likes)}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Категорія:</span>
                    <span className="ml-2 font-medium">{selectedModel.category}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Формати:</span>
                    <span className="ml-2 font-medium">{selectedModel.fileFormats.join(', ')}</span>
                  </div>
                </div>

                {selectedModel.tags.length > 0 && (
                  <div>
                    <span className="text-sm text-gray-500 block mb-2">Теги:</span>
                    <div className="flex flex-wrap gap-1">
                      {selectedModel.tags.map(tag => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                <Button className="w-full" size={isMobile ? 'lg' : 'default'}>
                  <Download className="w-4 h-4 mr-2" />
                  Завантажити модель
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
