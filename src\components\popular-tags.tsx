'use client';

import React from 'react';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { Tag } from 'lucide-react';

interface PopularTagsProps {
  tags: string[];
}

const PopularTags: React.FC<PopularTagsProps> = ({ tags }) => {
  return (
    <section className="py-12 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="flex items-center gap-3 mb-6">
          <Tag className="h-5 w-5 text-primary" />
          <h2 className="text-xl font-semibold">Популярні теги</h2>
        </div>

        <div className="flex flex-wrap gap-3">
          {tags.map((tag, index) => (
            <Link key={index} href={`/marketplace?search=${encodeURIComponent(tag)}`}>
              <Badge variant="outline" className="px-4 py-2 text-sm hover:bg-primary hover:text-primary-foreground transition-colors">
                {tag}
              </Badge>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
};

export default PopularTags;
