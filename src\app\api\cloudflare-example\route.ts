import { NextRequest, NextResponse } from 'next/server';
import { withApiMiddleware } from '@/lib/cloudflare/middleware';

/**
 * GET: Приклад використання Cloudflare Bindings
 */
export const GET = withApiMiddleware(async (
  _request: NextRequest,
  { db, storage }
) => {
  try {
    // Приклад запиту до D1
    const dbInfo = {
      tables: await db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' 
        ORDER BY name
      `).all(),
    };
    
    // Приклад запиту до R2
    let storageInfo;
    try {
      storageInfo = await storage.list({ limit: 10 });
    } catch (error) {
      storageInfo = { error: 'R2 not available or not configured' };
    }
    
    return NextResponse.json({
      success: true,
      message: 'Cloudflare Bindings example',
      data: {
        database: dbInfo,
        storage: storageInfo,
      }
    });
  } catch (error) {
    console.error('Error in Cloudflare Bindings example:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to execute Cloudflare Bindings example' },
      { status: 500 }
    );
  }
});
