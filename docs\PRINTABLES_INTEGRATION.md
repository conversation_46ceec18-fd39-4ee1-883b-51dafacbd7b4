# Інтеграція з Printables

Цей документ описує функціональність імпорту моделей з Printables.com у ваш 3D marketplace.

## Огляд

Система дозволяє користувачам імпортувати безкоштовні 3D моделі з Printables.com безпосередньо в marketplace. Імпортовані моделі зберігають інформацію про оригінальне джерело, ліцензію та автора.

## Основні компоненти

### 1. Розширені типи моделей (`src/types/models.ts`)

Додано нові типи для підтримки зовнішніх джерел:

- `ModelSource` - тип джерела моделі (local, printables, thingiverse, etc.)
- `ExternalSource` - інформація про зовнішнє джерело
- `License` - інформація про ліцензію моделі

### 2. Printables API утиліти (`src/lib/api/printables.ts`)

Основні функції:
- `extractPrintablesModelId(url)` - витягує ID моделі з URL
- `fetchPrintablesModel(modelId)` - отримує дані моделі (симуляція API)
- `convertPrintablesModelToInternal(model)` - конвертує в внутрішній формат
- `importModelFromPrintables(url)` - повний процес імпорту

### 3. Компонент імпорту (`src/components/marketplace/import-model-dialog.tsx`)

Діалогове вікно для імпорту моделей з функціями:
- Введення URL моделі
- Попередній перегляд моделі
- Валідація URL
- Імпорт моделі в систему

### 4. Компонент відображення джерела (`src/components/marketplace/model-source-badge.tsx`)

Показує:
- Джерело моделі (бейдж)
- Ліцензію з детальною інформацією
- Посилання на оригінал
- Статус "Безкоштовно"

## Використання

### Імпорт моделі

1. Відкрийте marketplace (`/marketplace`)
2. Натисніть кнопку "Імпортувати модель"
3. Введіть URL моделі з Printables:
   ```
   https://www.printables.com/model/1286204-dummy-13-terminator-t800-armor
   ```
4. Натисніть "Попередній перегляд"
5. Перевірте інформацію про модель
6. Натисніть "Імпортувати модель"

### Підтримувані URL формати

- `https://www.printables.com/model/[ID]-[name]`
- `https://printables.com/model/[ID]-[name]`

### Приклад імпортованої моделі

Модель Terminator T800 Armor вже додана як приклад:
- **ID**: `printables_1286204_terminator_t800`
- **Джерело**: Printables
- **Ліцензія**: CC BY 4.0
- **Статус**: Безкоштовно

## Технічні деталі

### Структура даних

```typescript
interface Model {
  // ... існуючі поля
  source: ModelSource;
  externalSource?: ExternalSource;
  license?: License;
  isFree?: boolean;
  originalPrice?: number;
}
```

### Ліцензії

Підтримувані типи ліцензій:
- CC0 - Публічне надбання
- CC-BY - Атрибуція обов'язкова
- CC-BY-SA - Атрибуція + ShareAlike
- CC-BY-NC - Атрибуція + некомерційне
- CC-BY-NC-SA - Атрибуція + некомерційне + ShareAlike
- GPL, MIT, Custom, Commercial

### Обмеження

1. **API Printables**: Printables не має публічного API, тому використовується симуляція
2. **Підтримувані моделі**: Наразі підтримується тільки модель з ID 1286204
3. **Формати файлів**: Підтримуються тільки STL файли

## Розширення функціональності

### Додавання нових платформ

1. Додайте новий тип до `ModelSource`
2. Створіть утиліти для нової платформи в `src/lib/api/`
3. Оновіть `sourceLabels` та `sourceColors` в `ModelSourceBadge`

### Додавання реального API

Замініть функцію `fetchPrintablesModel` на реальний API запит або веб-скрапінг.

### Додавання нових ліцензій

1. Додайте новий тип до `License.type`
2. Оновіть `licenseColors` в `ModelSourceBadge`

## Безпека

- Всі URL валідуються перед обробкою
- Зовнішні посилання відкриваються в новій вкладці з `rel="noopener noreferrer"`
- Імпортовані моделі зберігають інформацію про оригінальне джерело

## Тестування

Для тестування використовуйте URL:
```
https://www.printables.com/model/1286204-dummy-13-terminator-t800-armor
```

Цей URL поверне симульовані дані для моделі Terminator T800 Armor.

## Майбутні покращення

1. **Реальний API**: Інтеграція з офіційним API Printables (коли буде доступний)
2. **Веб-скрапінг**: Автоматичне отримання даних через веб-скрапінг
3. **Кеш**: Кешування імпортованих моделей
4. **Синхронізація**: Автоматичне оновлення імпортованих моделей
5. **Пакетний імпорт**: Імпорт декількох моделей одночасно
6. **Інші платформи**: Підтримка Thingiverse, MyMiniFactory, Thangs
