import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

/**
 * GET /api/admin/dashboard-stats
 * Отримання статистики для адміністративного дашборду
 */
export async function GET(request: NextRequest) {
  try {
    // Перевірка авторизації
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // В реальному додатку тут має бути перевірка ролі адміністратора
    // if (session.user.role !== 'admin') {
    //   return NextResponse.json(
    //     { success: false, error: 'Forbidden' },
    //     { status: 403 }
    //   );
    // }

    // Генерація демо-даних для дашборду
    const stats = {
      overview: {
        totalUsers: 1247 + Math.floor(Math.random() * 100),
        totalModels: 8934 + Math.floor(Math.random() * 200),
        totalDownloads: 45672 + Math.floor(Math.random() * 1000),
        totalRevenue: 12450.75 + Math.random() * 500,
        activeUsers: 89 + Math.floor(Math.random() * 20),
        newUsersToday: 23 + Math.floor(Math.random() * 10),
        modelsAddedToday: 156 + Math.floor(Math.random() * 50),
        downloadsToday: 892 + Math.floor(Math.random() * 200)
      },
      system: {
        uptime: '7 днів 14 годин',
        cpuUsage: 35 + Math.floor(Math.random() * 30),
        memoryUsage: 55 + Math.floor(Math.random() * 25),
        diskUsage: 25 + Math.floor(Math.random() * 20),
        networkTraffic: `${(2.1 + Math.random() * 1).toFixed(1)} GB/день`,
        healthScore: 92 + Math.floor(Math.random() * 8),
        lastBackup: '2 години тому',
        activeConnections: 200 + Math.floor(Math.random() * 100)
      },
      scraping: {
        totalRequests: 15678 + Math.floor(Math.random() * 1000),
        successfulRequests: 14892 + Math.floor(Math.random() * 800),
        failedRequests: 786 + Math.floor(Math.random() * 200),
        successRate: 94 + Math.floor(Math.random() * 5),
        averageResponseTime: 1.0 + Math.random() * 0.5,
        platformStats: {
          thingiverse: { 
            requests: 5234 + Math.floor(Math.random() * 500), 
            success: 4987 + Math.floor(Math.random() * 400), 
            avgTime: 1.0 + Math.random() * 0.3 
          },
          printables: { 
            requests: 4567 + Math.floor(Math.random() * 400), 
            success: 4321 + Math.floor(Math.random() * 350), 
            avgTime: 0.8 + Math.random() * 0.3 
          },
          myminifactory: { 
            requests: 3456 + Math.floor(Math.random() * 300), 
            success: 3298 + Math.floor(Math.random() * 250), 
            avgTime: 1.2 + Math.random() * 0.4 
          },
          thangs: { 
            requests: 1567 + Math.floor(Math.random() * 200), 
            success: 1456 + Math.floor(Math.random() * 150), 
            avgTime: 1.4 + Math.random() * 0.4 
          },
          makerworld: { 
            requests: 854 + Math.floor(Math.random() * 100), 
            success: 830 + Math.floor(Math.random() * 80), 
            avgTime: 1.1 + Math.random() * 0.4 
          }
        },
        rateLimitStatus: {
          thingiverse: { 
            remaining: 5 + Math.floor(Math.random() * 5), 
            total: 10, 
            resetTime: '14:30' 
          },
          printables: { 
            remaining: 8 + Math.floor(Math.random() * 7), 
            total: 15, 
            resetTime: '15:00' 
          },
          myminifactory: { 
            remaining: 3 + Math.floor(Math.random() * 5), 
            total: 8, 
            resetTime: '14:45' 
          }
        }
      },
      content: {
        pendingModeration: 15 + Math.floor(Math.random() * 20),
        reportedContent: 2 + Math.floor(Math.random() * 8),
        featuredModels: 40 + Math.floor(Math.random() * 15),
        categoriesCount: 28,
        tagsCount: 1456 + Math.floor(Math.random() * 100),
        averageRating: 4.2 + Math.random() * 0.3
      }
    };

    // В реальному додатку тут мають бути запити до бази даних:
    // const totalUsers = await query('SELECT COUNT(*) as count FROM users');
    // const totalModels = await query('SELECT COUNT(*) as count FROM models WHERE status = "approved"');
    // const totalDownloads = await query('SELECT SUM(download_count) as total FROM models');
    // const totalRevenue = await query('SELECT SUM(amount) as total FROM orders WHERE status = "completed"');
    // 
    // const systemMetrics = await getSystemMetrics();
    // const scrapingStats = await getScrapingStatistics();
    // const contentStats = await getContentStatistics();

    return NextResponse.json({
      success: true,
      data: stats,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch dashboard statistics' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/dashboard-stats
 * Оновлення або скидання статистики (для тестування)
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json() as any;
    const { action } = body;

    if (action === 'reset') {
      // Скидання статистики до початкових значень
      return NextResponse.json({
        success: true,
        message: 'Statistics reset successfully',
        timestamp: new Date().toISOString()
      });
    }

    if (action === 'refresh') {
      // Примусове оновлення статистики
      return NextResponse.json({
        success: true,
        message: 'Statistics refreshed successfully',
        timestamp: new Date().toISOString()
      });
    }

    return NextResponse.json(
      { success: false, error: 'Invalid action' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error updating dashboard stats:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update dashboard statistics' },
      { status: 500 }
    );
  }
}
