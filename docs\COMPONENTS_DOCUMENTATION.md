# Документація компонентів 3D-Маркетплейсу

## Зміст

1. [Вступ](#вступ)
2. [Базові компоненти UI](#базові-компоненти-ui)
   - [Button](#button)
   - [Card](#card)
   - [Input](#input)
   - [Label](#label)
   - [Tabs](#tabs)
   - [Badge](#badge)
   - [Form](#form)
   - [Select](#select)
   - [Dropdown Menu](#dropdown-menu)
3. [Спеціалізовані компоненти](#спеціалізовані-компоненти)
   - [HeroSection](#herosection)
   - [FeaturedModels](#featuredmodels)
   - [CategoriesDemo](#categoriesdemo)
   - [CollectionsSection](#collectionssection)
   - [HowItWorks](#howitworks)
   - [JoinCommunity](#joincommunity)
   - [PopularTags](#populartags)
4. [3D-компоненти](#3d-компоненти)
   - [ModelViewer](#modelviewer)
   - [SplineScene](#splinescene)
   - [CategoryExplorer](#categoryexplorer)
5. [Компоненти навігації](#компоненти-навігації)
   - [ThemeToggle](#themetoggle)
   - [ClientLayout](#clientlayout)
6. [Приклади використання](#приклади-використання)

## Вступ

Ця документація описує компоненти, які використовуються в 3D-маркетплейсі. Вона призначена для розробників, які працюють над проектом, і забезпечує розуміння структури та використання компонентів.

## Базові компоненти UI

Базові компоненти UI базуються на бібліотеці shadcn/ui, яка використовує Radix UI та Tailwind CSS.

### Button

Компонент кнопки з різними варіантами та розмірами.

**Імпорт:**
```tsx
import { Button } from "@/components/ui/button";
```

**Варіанти:**
- `default` - основна кнопка
- `secondary` - вторинна кнопка
- `destructive` - кнопка для деструктивних дій
- `outline` - контурна кнопка
- `ghost` - прозора кнопка
- `link` - кнопка-посилання

**Розміри:**
- `default` - стандартний розмір
- `sm` - малий розмір
- `lg` - великий розмір
- `icon` - кнопка-іконка

**Приклад:**
```tsx
<Button>Стандартна кнопка</Button>
<Button variant="secondary" size="lg">Велика вторинна кнопка</Button>
<Button variant="destructive">Видалити</Button>
<Button variant="outline">Контурна кнопка</Button>
<Button variant="ghost">Прозора кнопка</Button>
<Button variant="link">Посилання</Button>
<Button size="icon"><Search className="h-4 w-4" /></Button>
```

### Card

Компонент картки для відображення контенту.

**Імпорт:**
```tsx
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
```

**Приклад:**
```tsx
<Card>
  <CardHeader>
    <CardTitle>Заголовок картки</CardTitle>
    <CardDescription>Опис картки</CardDescription>
  </CardHeader>
  <CardContent>
    Вміст картки
  </CardContent>
  <CardFooter>
    <Button>Дія</Button>
  </CardFooter>
</Card>
```

### Input

Компонент для введення тексту.

**Імпорт:**
```tsx
import { Input } from "@/components/ui/input";
```

**Приклад:**
```tsx
<Input type="text" placeholder="Введіть текст" />
<Input type="email" placeholder="Введіть email" />
<Input type="password" placeholder="Введіть пароль" />
```

### Label

Компонент для підпису полів форми.

**Імпорт:**
```tsx
import { Label } from "@/components/ui/label";
```

**Приклад:**
```tsx
<div className="space-y-2">
  <Label htmlFor="email">Email</Label>
  <Input id="email" type="email" placeholder="Введіть email" />
</div>
```

### Tabs

Компонент для створення вкладок.

**Імпорт:**
```tsx
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
```

**Приклад:**
```tsx
<Tabs defaultValue="overview">
  <TabsList>
    <TabsTrigger value="overview">Огляд</TabsTrigger>
    <TabsTrigger value="settings">Налаштування</TabsTrigger>
  </TabsList>
  <TabsContent value="overview">Вміст вкладки "Огляд"</TabsContent>
  <TabsContent value="settings">Вміст вкладки "Налаштування"</TabsContent>
</Tabs>
```

### Badge

Компонент для відображення бейджів.

**Імпорт:**
```tsx
import { Badge } from "@/components/ui/badge";
```

**Варіанти:**
- `default` - основний бейдж
- `secondary` - вторинний бейдж
- `destructive` - бейдж для деструктивних дій
- `outline` - контурний бейдж

**Приклад:**
```tsx
<Badge>Новинка</Badge>
<Badge variant="secondary">Популярне</Badge>
<Badge variant="destructive">Видалено</Badge>
<Badge variant="outline">Контурний</Badge>
```

### Form

Компонент для створення форм з валідацією.

**Імпорт:**
```tsx
import { Form, FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from "@/components/ui/form";
```

**Приклад:**
```tsx
<Form {...form}>
  <form onSubmit={form.handleSubmit(onSubmit)}>
    <FormField
      control={form.control}
      name="email"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Email</FormLabel>
          <FormControl>
            <Input placeholder="Введіть email" {...field} />
          </FormControl>
          <FormDescription>Введіть ваш email для входу</FormDescription>
          <FormMessage />
        </FormItem>
      )}
    />
    <Button type="submit">Відправити</Button>
  </form>
</Form>
```

### Select

Компонент для вибору з випадаючого списку.

**Імпорт:**
```tsx
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
```

**Приклад:**
```tsx
<Select>
  <SelectTrigger>
    <SelectValue placeholder="Виберіть категорію" />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="art">Мистецтво</SelectItem>
    <SelectItem value="gadgets">Гаджети</SelectItem>
    <SelectItem value="home">Дім</SelectItem>
  </SelectContent>
</Select>
```

### Dropdown Menu

Компонент для створення випадаючого меню.

**Імпорт:**
```tsx
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
```

**Приклад:**
```tsx
<DropdownMenu>
  <DropdownMenuTrigger asChild>
    <Button variant="outline">Меню</Button>
  </DropdownMenuTrigger>
  <DropdownMenuContent>
    <DropdownMenuItem>Профіль</DropdownMenuItem>
    <DropdownMenuItem>Налаштування</DropdownMenuItem>
    <DropdownMenuItem>Вихід</DropdownMenuItem>
  </DropdownMenuContent>
</DropdownMenu>
```

## Спеціалізовані компоненти

### HeroSection

Компонент для відображення герой-секції на головній сторінці.

**Імпорт:**
```tsx
import HeroSection from "@/components/hero-section";
```

**Приклад:**
```tsx
<HeroSection />
```

### FeaturedModels

Компонент для відображення популярних моделей.

**Імпорт:**
```tsx
import FeaturedModels from "@/components/featured-models";
```

**Приклад:**
```tsx
<FeaturedModels />
```

### CategoriesDemo

Компонент для відображення категорій 3D-моделей.

**Імпорт:**
```tsx
import CategoriesDemo from "@/components/categories-demo";
```

**Приклад:**
```tsx
<CategoriesDemo />
```

### CollectionsSection

Компонент для відображення колекцій 3D-моделей.

**Імпорт:**
```tsx
import CollectionsSection from "@/components/collections-section";
```

**Приклад:**
```tsx
<CollectionsSection collections={collections} />
```

### HowItWorks

Компонент для відображення секції "Як це працює".

**Імпорт:**
```tsx
import HowItWorks from "@/components/how-it-works";
```

**Приклад:**
```tsx
<HowItWorks />
```

### JoinCommunity

Компонент для відображення секції "Приєднуйтесь до спільноти".

**Імпорт:**
```tsx
import JoinCommunity from "@/components/join-community";
```

**Приклад:**
```tsx
<JoinCommunity />
```

### PopularTags

Компонент для відображення популярних тегів.

**Імпорт:**
```tsx
import PopularTags from "@/components/popular-tags";
```

**Приклад:**
```tsx
<PopularTags tags={popularTags} />
```

## 3D-компоненти

### ModelViewer

Компонент для відображення 3D-моделей.

**Імпорт:**
```tsx
import { ModelViewer } from "@/components/model-viewer/model-viewer";
```

**Приклад:**
```tsx
<ModelViewer model={model} />
```

### SplineScene

Компонент для відображення 3D-сцен, створених у Spline.

**Імпорт:**
```tsx
import { SplineScene, SCENE_PRESETS } from "@/components/ui/splite";
```

**Приклад:**
```tsx
<SplineScene 
  scene="https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode"
  preset="PRODUCT_VIEWER"
/>
```

### CategoryExplorer

Компонент для відображення категорій у 3D-просторі.

**Імпорт:**
```tsx
import CategoryExplorer from "@/components/model-viewer/category-explorer";
```

**Приклад:**
```tsx
<CategoryExplorer 
  splineSceneUrl="https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode"
  categories={categories}
  onCategorySelect={handleCategorySelect}
/>
```

## Компоненти навігації

### ThemeToggle

Компонент для перемикання між світлою та темною темами.

**Імпорт:**
```tsx
import { ThemeToggle } from "@/components/theme-toggle";
```

**Приклад:**
```tsx
<ThemeToggle />
```

### ClientLayout

Компонент для відображення загального макету сторінки.

**Імпорт:**
```tsx
import ClientLayout from "@/app/ClientLayout";
```

**Приклад:**
```tsx
<ClientLayout>
  {children}
</ClientLayout>
```

## Приклади використання

### Головна сторінка

```tsx
export default function Home() {
  return (
    <main className="flex min-h-screen flex-col bg-background">
      <HeroSection />
      <PopularTags tags={POPULAR_TAGS} />
      <FeaturedModels />
      <CategoriesDemo />
      <CollectionsSection collections={COLLECTIONS} />
      <HowItWorks />
      <JoinCommunity />
    </main>
  );
}
```

### Сторінка деталей моделі

```tsx
export default function ModelDetailPage({ params }: { params: { id: string } }) {
  const [activeTab, setActiveTab] = useState('overview');
  const model = MODEL_DATA;
  
  return (
    <main className="container mx-auto py-8 px-4">
      <div className="mb-6">
        <Link href="/models" className="flex items-center text-muted-foreground hover:text-primary transition-colors">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Назад до моделей
        </Link>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardContent className="p-0 overflow-hidden">
              <div className="h-[500px] relative">
                <ModelViewer model={model} />
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{model.name}</CardTitle>
            </CardHeader>
            <CardContent>
              <Button className="w-full">
                <Download className="h-4 w-4 mr-2" />
                Завантажити
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
      
      <Tabs defaultValue="overview" className="mt-8">
        <TabsList>
          <TabsTrigger value="overview">Огляд</TabsTrigger>
          <TabsTrigger value="print-settings">Налаштування друку</TabsTrigger>
          <TabsTrigger value="reviews">Відгуки</TabsTrigger>
        </TabsList>
        <TabsContent value="overview">
          <Card>
            <CardHeader>
              <CardTitle>Опис</CardTitle>
            </CardHeader>
            <CardContent>
              <p>{model.description}</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      <RecommendedModels models={recommendedModels} title="Схожі моделі" className="mt-12" />
    </main>
  );
}
