// Environment setup for Jest tests

// Set test environment variables
process.env.NODE_ENV = 'test'
process.env.NEXT_PUBLIC_API_URL = 'http://localhost:3000'

// Mock Cloudflare environment
process.env.CLOUDFLARE_ACCOUNT_ID = 'test-account-id'
process.env.CLOUDFLARE_API_TOKEN = 'test-api-token'

// Mock database URLs
process.env.DATABASE_URL = 'sqlite://test.db'
process.env.REDIS_URL = 'redis://localhost:6379'

// Mock external service URLs
process.env.PRINTABLES_BASE_URL = 'https://www.printables.com'
process.env.MAKERWORLD_BASE_URL = 'https://makerworld.com'
process.env.THANGS_BASE_URL = 'https://thangs.com'

// Mock rate limiting settings
process.env.RATE_LIMIT_PRINTABLES = '10'
process.env.RATE_LIMIT_MAKERWORLD = '15'
process.env.RATE_LIMIT_THANGS = '12'

// Mock scraping settings
process.env.SCRAPING_TIMEOUT = '30000'
process.env.SCRAPING_RETRY_ATTEMPTS = '3'
process.env.SCRAPING_RETRY_DELAY = '1000'

// Mock user agent for scraping
process.env.SCRAPING_USER_AGENT = 'Test-Bot/1.0'

// Disable real network requests in tests
process.env.DISABLE_NETWORK_REQUESTS = 'true'
