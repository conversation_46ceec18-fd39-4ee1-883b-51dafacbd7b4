'use client';

import React, { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Download, Loader2, CheckCircle, AlertCircle, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface DownloadButtonProps {
  modelId: string;
  modelName: string;
  isOwner?: boolean;
  isFree?: boolean;
  hasPurchased?: boolean;
  variant?: 'default' | 'outline' | 'secondary';
  size?: 'sm' | 'default' | 'lg';
  className?: string;
  // Legacy props for backward compatibility
  url?: string;
}

interface DownloadSession {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'expired';
  downloadUrl?: string;
  expiresAt: number;
  attempts: number;
  maxAttempts: number;
}

export default function DownloadButton({
  modelId,
  modelName,
  isOwner = false,
  isFree = false,
  hasPurchased = false,
  variant = 'default',
  size = 'default',
  className = '',
  url // Legacy prop
}: DownloadButtonProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const { toast } = useToast();

  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [downloadSession, setDownloadSession] = useState<DownloadSession | null>(null);
  const [showDownloadDialog, setShowDownloadDialog] = useState(false);

  // Legacy support - якщо передано URL, використовуємо стару логіку
  if (url) {
    return (
      <Button
        variant={variant}
        size={size}
        className={className}
        onClick={() => {
          window.open(url, '_blank');
          toast({
            title: "Завантаження розпочато",
            description: "Ваше завантаження повинно розпочатися незабаром.",
          });
        }}
      >
        <Download className="mr-2 h-4 w-4" />
        Завантажити
      </Button>
    );
  }

  // Перевірка доступу до завантаження
  const canDownload = isOwner || isFree || hasPurchased;

  const handleDownload = async () => {
    if (!session) {
      toast({
        title: 'Потрібна авторизація',
        description: 'Увійдіть в систему для завантаження моделі',
        variant: 'destructive',
      });
      router.push('/auth/signin');
      return;
    }

    if (!canDownload) {
      toast({
        title: 'Доступ заборонено',
        description: 'Ви повинні купити цю модель для завантаження',
        variant: 'destructive',
      });
      return;
    }

    setIsDownloading(true);
    setDownloadProgress(0);
    setShowDownloadDialog(true);

    try {
      // Крок 1: Створюємо сесію завантаження (20%)
      setDownloadProgress(20);
      const sessionResponse = await fetch('/api/models/download', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ modelId }),
      });

      if (!sessionResponse.ok) {
        throw new Error('Failed to create download session');
      }

      const sessionResult = await sessionResponse.json();

      if (!sessionResult.success) {
        throw new Error(sessionResult.error || 'Failed to create download session');
      }

      // Крок 2: Отримуємо інформацію про сесію (40%)
      setDownloadProgress(40);
      const sessionId = sessionResult.sessionId;

      // Крок 3: Обробляємо завантаження через Durable Object (60%)
      setDownloadProgress(60);
      const downloadResponse = await fetch('/api/models/download/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sessionId, modelId }),
      });

      if (!downloadResponse.ok) {
        throw new Error('Failed to process download');
      }

      const downloadResult = await downloadResponse.json();

      if (!downloadResult.success) {
        throw new Error(downloadResult.error || 'Failed to process download');
      }

      // Крок 4: Завантажуємо файл (80%)
      setDownloadProgress(80);

      if (downloadResult.downloadUrl) {
        // Створюємо посилання для завантаження
        const link = document.createElement('a');
        link.href = downloadResult.downloadUrl;
        link.download = downloadResult.fileName || `${modelName}.zip`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }

      // Завершено (100%)
      setDownloadProgress(100);

      toast({
        title: 'Завантаження розпочато!',
        description: `${modelName} завантажується на ваш пристрій`,
      });

      // Закриваємо діалог через 2 секунди
      setTimeout(() => {
        setShowDownloadDialog(false);
      }, 2000);

    } catch (error) {
      console.error('Download error:', error);
      toast({
        title: 'Помилка завантаження',
        description: error instanceof Error ? error.message : 'Не вдалося завантажити модель',
        variant: 'destructive',
      });
    } finally {
      setIsDownloading(false);
    }
  };

  const getButtonText = () => {
    if (isDownloading) {
      return 'Завантаження...';
    }

    if (isFree) {
      return 'Завантажити безкоштовно';
    }

    if (isOwner) {
      return 'Завантажити';
    }

    if (hasPurchased) {
      return 'Завантажити';
    }

    return 'Купити для завантаження';
  };

  const getButtonIcon = () => {
    if (isDownloading) {
      return <Loader2 className="mr-2 h-4 w-4 animate-spin" />;
    }
    return <Download className="mr-2 h-4 w-4" />;
  };

  return (
    <>
      <Button
        variant={variant}
        size={size}
        className={className}
        onClick={handleDownload}
        disabled={isDownloading || (!canDownload && !isFree)}
      >
        {getButtonIcon()}
        {getButtonText()}
      </Button>

      {/* Діалог прогресу завантаження */}
      <Dialog open={showDownloadDialog} onOpenChange={setShowDownloadDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Download className="mr-2 h-5 w-5" />
              Завантаження моделі
            </DialogTitle>
            <DialogDescription>
              {modelName}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Прогрес завантаження</span>
                <span>{downloadProgress}%</span>
              </div>
              <Progress value={downloadProgress} className="w-full" />
            </div>

            {downloadSession && (
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                {downloadSession.status === 'pending' && (
                  <>
                    <Clock className="h-4 w-4" />
                    <span>Очікування обробки...</span>
                  </>
                )}
                {downloadSession.status === 'processing' && (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Обробка файлу...</span>
                  </>
                )}
                {downloadSession.status === 'completed' && (
                  <>
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span>Завантаження завершено!</span>
                  </>
                )}
                {downloadSession.status === 'failed' && (
                  <>
                    <AlertCircle className="h-4 w-4 text-red-600" />
                    <span>Помилка завантаження</span>
                  </>
                )}
              </div>
            )}

            {downloadProgress === 100 && (
              <div className="text-center text-sm text-green-600">
                <CheckCircle className="h-5 w-5 mx-auto mb-2" />
                Файл завантажується на ваш пристрій
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
