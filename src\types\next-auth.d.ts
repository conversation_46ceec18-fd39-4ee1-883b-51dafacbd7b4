import NextAuth, { DefaultSession } from "next-auth";
import { JWT } from "next-auth/jwt";

declare module "next-auth" {
  /**
   * Розширення типу User для NextAuth.js
   */
  interface User {
    id: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
  }

  /**
   * Розширення типу Session для NextAuth.js
   */
  interface Session {
    user: {
      id: string;
    } & DefaultSession["user"];
  }
}

declare module "next-auth/jwt" {
  /**
   * Розширення типу JWT для NextAuth.js
   */
  interface JWT {
    id: string;
  }
}
