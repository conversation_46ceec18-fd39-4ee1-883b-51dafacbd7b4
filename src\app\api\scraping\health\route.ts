/**
 * Health monitoring API endpoint for scraping services
 */

import { NextRequest, NextResponse } from 'next/server';
import { printablesScraper } from '@/lib/api/printables';
import { makerWorldScraper } from '@/lib/api/makerworld';
import { thangsScraper } from '@/lib/api/thangs';
import { platformRateLimiters } from '@/lib/scraping/rate-limiter';
import { PlatformHealth, ModelSource } from '@/types/models';

interface HealthResponse {
  success: boolean;
  data?: {
    status: 'healthy' | 'degraded' | 'down';
    timestamp: string;
    platforms: Record<ModelSource, PlatformHealth>;
    overall: {
      totalPlatforms: number;
      operationalPlatforms: number;
      degradedPlatforms: number;
      downPlatforms: number;
    };
  };
  error?: {
    code: string;
    message: string;
  };
}

/**
 * Test platform connectivity and response time
 */
async function testPlatformHealth(platform: ModelSource, testUrl: string): Promise<PlatformHealth> {
  const startTime = Date.now();

  try {
    let scraper;
    switch (platform) {
      case 'printables':
        scraper = printablesScraper;
        break;
      case 'makerworld':
        scraper = makerWorldScraper;
        break;
      case 'thangs':
        scraper = thangsScraper;
        break;
      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }

    // Test basic connectivity by fetching HTML (without full scraping)
    await scraper['fetchHtml'](testUrl);

    const responseTime = Date.now() - startTime;
    const rateLimitRemaining = await platformRateLimiters.getRemainingPoints(platform);

    // Determine status based on response time
    let status: PlatformHealth['status'] = 'operational';
    if (responseTime > 10000) { // 10 seconds
      status = 'down';
    } else if (responseTime > 5000) { // 5 seconds
      status = 'degraded';
    }

    return {
      platform,
      status,
      lastCheck: new Date().toISOString(),
      responseTime,
      rateLimitRemaining,
      errorRate: 0, // TODO: Calculate from recent history
    };

  } catch (error) {
    const responseTime = Date.now() - startTime;
    const rateLimitRemaining = await platformRateLimiters.getRemainingPoints(platform);

    return {
      platform,
      status: 'down',
      lastCheck: new Date().toISOString(),
      responseTime,
      rateLimitRemaining,
      errorRate: 100, // 100% error rate if completely down
    };
  }
}

/**
 * GET /api/scraping/health
 * Get health status of all scraping platforms
 */
export async function GET(request: NextRequest): Promise<NextResponse<HealthResponse>> {
  try {
    // Test URLs for each platform (using known working models)
    const testUrls = {
      printables: 'https://www.printables.com/model/1',
      makerworld: 'https://makerworld.com/en/models/1',
      thangs: 'https://thangs.com/designer/test/model/1',
    };

    // Test all platforms concurrently
    const healthPromises = Object.entries(testUrls).map(([platform, url]) =>
      testPlatformHealth(platform as ModelSource, url)
    );

    const platformHealthResults = await Promise.allSettled(healthPromises);

    // Process results
    const platforms: Record<ModelSource, PlatformHealth> = {} as Record<ModelSource, PlatformHealth>;
    let operationalCount = 0;
    let degradedCount = 0;
    let downCount = 0;

    platformHealthResults.forEach((result, index) => {
      const platform = Object.keys(testUrls)[index] as ModelSource;

      if (result.status === 'fulfilled') {
        platforms[platform] = result.value;

        switch (result.value.status) {
          case 'operational':
            operationalCount++;
            break;
          case 'degraded':
            degradedCount++;
            break;
          case 'down':
            downCount++;
            break;
        }
      } else {
        // If health check itself failed
        platforms[platform] = {
          platform,
          status: 'down',
          lastCheck: new Date().toISOString(),
          responseTime: 0,
          rateLimitRemaining: 0,
          errorRate: 100,
        };
        downCount++;
      }
    });

    // Determine overall status
    let overallStatus: 'healthy' | 'degraded' | 'down' = 'healthy';
    if (downCount > 0) {
      overallStatus = downCount === Object.keys(testUrls).length ? 'down' : 'degraded';
    } else if (degradedCount > 0) {
      overallStatus = 'degraded';
    }

    return NextResponse.json({
      success: true,
      data: {
        status: overallStatus,
        timestamp: new Date().toISOString(),
        platforms,
        overall: {
          totalPlatforms: Object.keys(testUrls).length,
          operationalPlatforms: operationalCount,
          degradedPlatforms: degradedCount,
          downPlatforms: downCount,
        },
      },
    });

  } catch (error) {
    console.error('Health check API error:', error);

    return NextResponse.json({
      success: false,
      error: {
        code: 'HEALTH_CHECK_FAILED',
        message: 'Failed to perform health check',
      },
    }, { status: 500 });
  }
}



/**
 * POST /api/scraping/health/reset
 * Reset rate limits and clear error counters
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    const { platform, action } = body as { platform?: string; action?: string };

    if (action === 'reset_rate_limits') {
      if (platform && ['printables', 'makerworld', 'thangs'].includes(platform)) {
        await platformRateLimiters.reset(platform as ModelSource);

        return NextResponse.json({
          success: true,
          data: {
            message: `Rate limits reset for ${platform}`,
            platform,
            timestamp: new Date().toISOString(),
          },
        });
      } else {
        // Reset all platforms
        await Promise.all([
          platformRateLimiters.reset('printables'),
          platformRateLimiters.reset('makerworld'),
          platformRateLimiters.reset('thangs'),
        ]);

        return NextResponse.json({
          success: true,
          data: {
            message: 'Rate limits reset for all platforms',
            timestamp: new Date().toISOString(),
          },
        });
      }
    }

    return NextResponse.json({
      success: false,
      error: {
        code: 'INVALID_ACTION',
        message: 'Invalid action. Supported actions: reset_rate_limits',
      },
    }, { status: 400 });

  } catch (error) {
    console.error('Health reset API error:', error);

    return NextResponse.json({
      success: false,
      error: {
        code: 'RESET_FAILED',
        message: 'Failed to reset health status',
      },
    }, { status: 500 });
  }
}

/**
 * OPTIONS /api/scraping/health
 * CORS preflight
 */
export async function OPTIONS(): Promise<NextResponse> {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
