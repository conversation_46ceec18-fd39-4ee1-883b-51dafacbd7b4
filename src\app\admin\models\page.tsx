'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  Package, 
  Search, 
  Eye, 
  Download, 
  Star, 
  Heart,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  ExternalLink,
  Calendar,
  User,
  DollarSign,
  FileText
} from 'lucide-react';

interface Model {
  id: string;
  title: string;
  description: string;
  designer: string;
  platform: string;
  status: 'approved' | 'pending' | 'rejected' | 'featured';
  category: string;
  price: number;
  downloads: number;
  likes: number;
  rating: number;
  createdAt: string;
  updatedAt: string;
  thumbnailUrl?: string;
  tags: string[];
  fileFormats: string[];
  fileSize: number;
}

export default function AdminModelsPage() {
  const [models, setModels] = useState<Model[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterPlatform, setFilterPlatform] = useState<string>('all');

  useEffect(() => {
    fetchModels();
  }, []);

  const fetchModels = async () => {
    try {
      // В реальному додатку тут буде запит до API
      // const response = await fetch('/api/admin/models');
      // const result = await response.json();
      
      // Демо дані
      const demoModels: Model[] = [
        {
          id: '1',
          title: 'Дракон-охоронець',
          description: 'Детальна модель дракона для настільних ігор',
          designer: 'Олександр Петренко',
          platform: 'thingiverse',
          status: 'approved',
          category: 'Ігри та іграшки',
          price: 0,
          downloads: 1250,
          likes: 89,
          rating: 4.8,
          createdAt: '2024-01-15',
          updatedAt: '2024-01-20',
          tags: ['дракон', 'настільні ігри', 'фентезі'],
          fileFormats: ['STL', 'OBJ'],
          fileSize: 15.6
        },
        {
          id: '2',
          title: 'Функціональна шестерня',
          description: 'Механічна шестерня з рухомими частинами',
          designer: 'Марія Іваненко',
          platform: 'printables',
          status: 'featured',
          category: 'Механіка',
          price: 5.99,
          downloads: 567,
          likes: 45,
          rating: 4.6,
          createdAt: '2024-01-10',
          updatedAt: '2024-01-19',
          tags: ['механіка', 'шестерня', 'функціональний'],
          fileFormats: ['STL', '3MF'],
          fileSize: 8.2
        },
        {
          id: '3',
          title: 'Мініатюрний будинок',
          description: 'Архітектурна модель сучасного будинку',
          designer: 'Дмитро Коваленко',
          platform: 'myminifactory',
          status: 'pending',
          category: 'Архітектура',
          price: 12.50,
          downloads: 23,
          likes: 8,
          rating: 4.2,
          createdAt: '2024-01-18',
          updatedAt: '2024-01-18',
          tags: ['архітектура', 'будинок', 'мініатюра'],
          fileFormats: ['STL'],
          fileSize: 22.1
        },
        {
          id: '4',
          title: 'Космічний корабель',
          description: 'Футуристичний дизайн космічного корабля',
          designer: 'Анна Сидоренко',
          platform: 'thangs',
          status: 'rejected',
          category: 'Наука та техніка',
          price: 8.99,
          downloads: 12,
          likes: 3,
          rating: 3.8,
          createdAt: '2024-01-12',
          updatedAt: '2024-01-16',
          tags: ['космос', 'корабель', 'sci-fi'],
          fileFormats: ['STL', 'OBJ', 'PLY'],
          fileSize: 45.3
        },
        {
          id: '5',
          title: 'Декоративна ваза',
          description: 'Елегантна ваза з геометричним орнаментом',
          designer: 'Ігор Мельник',
          platform: 'makerworld',
          status: 'approved',
          category: 'Дім та сад',
          price: 3.99,
          downloads: 234,
          likes: 67,
          rating: 4.5,
          createdAt: '2024-01-05',
          updatedAt: '2024-01-15',
          tags: ['ваза', 'декор', 'геометрія'],
          fileFormats: ['STL'],
          fileSize: 12.8
        }
      ];
      
      setModels(demoModels);
    } catch (error) {
      console.error('Failed to fetch models:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredModels = models.filter(model => {
    const matchesSearch = model.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         model.designer.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         model.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || model.status === filterStatus;
    const matchesPlatform = filterPlatform === 'all' || model.platform === filterPlatform;
    
    return matchesSearch && matchesStatus && matchesPlatform;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800 border-green-200';
      case 'featured': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'rejected': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle className="w-3 h-3" />;
      case 'featured': return <Star className="w-3 h-3" />;
      case 'pending': return <Clock className="w-3 h-3" />;
      case 'rejected': return <XCircle className="w-3 h-3" />;
      default: return <AlertTriangle className="w-3 h-3" />;
    }
  };

  const getPlatformColor = (platform: string) => {
    const colors: Record<string, string> = {
      thingiverse: 'bg-blue-100 text-blue-800 border-blue-200',
      printables: 'bg-orange-100 text-orange-800 border-orange-200',
      myminifactory: 'bg-green-100 text-green-800 border-green-200',
      thangs: 'bg-purple-100 text-purple-800 border-purple-200',
      makerworld: 'bg-red-100 text-red-800 border-red-200'
    };
    return colors[platform] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const formatCurrency = (amount: number) => {
    return amount === 0 ? 'Безкоштовно' : new Intl.NumberFormat('uk-UA', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatFileSize = (sizeInMB: number) => {
    return `${sizeInMB.toFixed(1)} MB`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('uk-UA');
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold">📦 Управління моделями</h1>
          <p className="text-muted-foreground mt-2">
            Модерація та управління 3D моделями в системі
          </p>
        </div>
        <Button>
          <Package className="w-4 h-4 mr-2" />
          Додати модель
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Всього моделей</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{models.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Схвалені</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {models.filter(m => m.status === 'approved').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">На розгляді</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {models.filter(m => m.status === 'pending').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Рекомендовані</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {models.filter(m => m.status === 'featured').length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Фільтри та пошук</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Пошук за назвою, автором або описом..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-3 py-2 border border-input bg-background rounded-md"
            >
              <option value="all">Всі статуси</option>
              <option value="approved">Схвалені</option>
              <option value="featured">Рекомендовані</option>
              <option value="pending">На розгляді</option>
              <option value="rejected">Відхилені</option>
            </select>
            <select
              value={filterPlatform}
              onChange={(e) => setFilterPlatform(e.target.value)}
              className="px-3 py-2 border border-input bg-background rounded-md"
            >
              <option value="all">Всі платформи</option>
              <option value="thingiverse">Thingiverse</option>
              <option value="printables">Printables</option>
              <option value="myminifactory">MyMiniFactory</option>
              <option value="thangs">Thangs</option>
              <option value="makerworld">MakerWorld</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Models Table */}
      <Card>
        <CardHeader>
          <CardTitle>Список моделей ({filteredModels.length})</CardTitle>
          <CardDescription>
            Детальна інформація про всі моделі в системі
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Модель</TableHead>
                <TableHead>Автор</TableHead>
                <TableHead>Платформа</TableHead>
                <TableHead>Статус</TableHead>
                <TableHead>Категорія</TableHead>
                <TableHead>Ціна</TableHead>
                <TableHead>Завантаження</TableHead>
                <TableHead>Рейтинг</TableHead>
                <TableHead>Дата</TableHead>
                <TableHead>Дії</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredModels.map((model) => (
                <TableRow key={model.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                        <Package className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <div className="font-medium">{model.title}</div>
                        <div className="text-sm text-muted-foreground">
                          {model.description.length > 50
                            ? model.description.substring(0, 50) + '...'
                            : model.description}
                        </div>
                        <div className="flex gap-1 mt-1">
                          {model.tags.slice(0, 2).map((tag) => (
                            <Badge key={tag} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                          {model.tags.length > 2 && (
                            <Badge variant="secondary" className="text-xs">
                              +{model.tags.length - 2}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <User className="w-3 h-3 text-muted-foreground" />
                      {model.designer}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className={getPlatformColor(model.platform)}>
                      {model.platform}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className={getStatusColor(model.status)}>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(model.status)}
                        {model.status === 'approved' ? 'Схвалено' :
                         model.status === 'featured' ? 'Рекомендовано' :
                         model.status === 'pending' ? 'На розгляді' : 'Відхилено'}
                      </div>
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">{model.category}</div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <DollarSign className="w-3 h-3 text-muted-foreground" />
                      {formatCurrency(model.price)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Download className="w-3 h-3 text-muted-foreground" />
                      {model.downloads}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Star className="w-3 h-3 text-yellow-500" />
                      {model.rating}
                      <span className="text-xs text-muted-foreground">
                        ({model.likes})
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Calendar className="w-3 h-3 text-muted-foreground" />
                      {formatDate(model.createdAt)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button size="sm" variant="outline">
                        <Eye className="w-3 h-3" />
                      </Button>
                      <Button size="sm" variant="outline">
                        <CheckCircle className="w-3 h-3" />
                      </Button>
                      <Button size="sm" variant="outline">
                        <MoreHorizontal className="w-3 h-3" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
