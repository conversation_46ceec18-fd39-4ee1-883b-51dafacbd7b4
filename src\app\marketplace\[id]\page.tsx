import ClientModelDetail from '@/components/marketplace/client-model-detail';
import { Model } from '@/types/models';

// Function to fetch model data from API
async function getModelData(id: string): Promise<Model | null> {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || ''}/api/models/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'no-store', // Disable caching to get the latest data
    });

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error(`Failed to fetch model: ${response.statusText}`);
    }

    const data = await response.json() as any;

    // Перевірка різних можливих структур відповіді
    if (data.data && data.data.model) {
      return data.data.model as Model;
    } else if (data.model) {
      return data.model as Model;
    } else if (data.data) {
      return data.data as Model;
    } else {
      return data as Model;
    }
  } catch (error) {
    console.error('Error fetching model data:', error);
    return null;
  }
}

// Server component for fetching model data
export default async function ModelDetailPage(props: {
  params: Promise<{ id: string }>
}) {
  const params = await props.params;
  const modelId = params.id;
  const model = await getModelData(modelId);

  if (!model) {
    return (
      <div className="container mx-auto px-4 py-12 text-center">
        <p className="text-lg">Model not found</p>
      </div>
    );
  }

  return (
    <ClientModelDetail model={model} />
  );
}
