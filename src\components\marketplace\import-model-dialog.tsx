'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Download, ExternalLink, AlertCircle, CheckCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ScrapedModel, ModelSource, Model } from '@/types/models';
import { createModel } from '@/lib/api/models';

interface ImportModelDialogProps {
  onModelImported?: (model: Model) => void;
}

interface PlatformInfo {
  name: string;
  icon: string;
  urlPattern: RegExp;
  placeholder: string;
}

export const ImportModelDialog: React.FC<ImportModelDialogProps> = ({ onModelImported }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [url, setUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [previewModel, setPreviewModel] = useState<ScrapedModel | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [detectedPlatform, setDetectedPlatform] = useState<ModelSource | null>(null);

  // Platform configurations
  const platforms: Record<ModelSource, PlatformInfo> = {
    printables: {
      name: 'Printables',
      icon: '🖨️',
      urlPattern: /^https?:\/\/(www\.)?printables\.com\/model\/\d+/,
      placeholder: 'https://www.printables.com/model/123456-example-model',
    },
    makerworld: {
      name: 'MakerWorld',
      icon: '🌍',
      urlPattern: /^https?:\/\/(www\.)?makerworld\.com\/(en\/)?models\/\d+/,
      placeholder: 'https://makerworld.com/en/models/123456',
    },
    thangs: {
      name: 'Thangs',
      icon: '🔧',
      urlPattern: /^https?:\/\/(www\.)?thangs\.com\/designer\/[^\/]+\/model\/\d+/,
      placeholder: 'https://thangs.com/designer/user/model/123456',
    },
    local: { name: 'Local', icon: '', urlPattern: /.*/, placeholder: '' },
    thingiverse: { name: 'Thingiverse', icon: '', urlPattern: /.*/, placeholder: '' },
    myminifactory: { name: 'MyMiniFactory', icon: '', urlPattern: /.*/, placeholder: '' },
  };

  // Detect platform from URL
  const detectPlatform = (url: string): ModelSource | null => {
    for (const [platform, config] of Object.entries(platforms)) {
      if (config.urlPattern.test(url)) {
        return platform as ModelSource;
      }
    }
    return null;
  };

  const handleUrlChange = (value: string) => {
    setUrl(value);
    setError(null);
    setSuccess(null);
    setPreviewModel(null);

    // Detect platform
    const platform = detectPlatform(value);
    setDetectedPlatform(platform);
  };

  const handlePreview = async () => {
    if (!url.trim()) {
      setError('Please enter a model URL');
      return;
    }

    const platform = detectPlatform(url);
    if (!platform || !['printables', 'makerworld', 'thangs'].includes(platform)) {
      setError('Unsupported platform. Please use URLs from Printables, MakerWorld, or Thangs.');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/scraping/import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url,
          options: {
            includeFiles: true,
            includeImages: true,
            validateLicense: true,
          },
        }),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to import model');
      }

      // For preview, we need to fetch the scraped data
      // In a real implementation, the API would return the scraped model data
      setPreviewModel({
        title: result.data.model.title || 'Imported Model',
        description: 'Model imported successfully',
        summary: '',
        images: [],
        thumbnail: '',
        files: [],
        fileFormats: ['STL'],
        totalSize: 0,
        designer: { id: 'unknown', name: 'Unknown Designer' },
        tags: [],
        category: 'Other',
        license: {
          type: 'Custom',
          name: 'Custom License',
          detected: false,
          confidence: 0,
          allowCommercialUse: false,
          requireAttribution: true,
          allowDerivatives: false,
        },
        stats: { views: 0, downloads: 0, likes: 0, comments: 0 },
        platform,
        originalId: result.data.modelId,
        originalUrl: url,
        scrapedAt: new Date().toISOString(),
        isFree: true,
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleImport = async () => {
    if (!previewModel) return;

    setIsLoading(true);
    setError(null);

    try {
      const importedModel = await createModel(previewModel);
      setSuccess('Модель успішно імпортована!');
      onModelImported?.(importedModel);

      // Закриваємо діалог через 2 секунди
      setTimeout(() => {
        setIsOpen(false);
        resetForm();
      }, 2000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Помилка при імпорті моделі');
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setUrl('');
    setPreviewModel(null);
    setError(null);
    setSuccess(null);
  };

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      resetForm();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button variant="outline" className="gap-2">
          <Download className="h-4 w-4" />
          Імпортувати модель
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Import Model from External Platform</DialogTitle>
          <DialogDescription>
            Enter a URL from Printables, MakerWorld, or Thangs to import the model into your marketplace
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* URL Input */}
          <div className="space-y-2">
            <Label htmlFor="model-url">Model URL</Label>
            <div className="flex gap-2">
              <div className="flex-1">
                <Input
                  id="model-url"
                  placeholder={
                    detectedPlatform && platforms[detectedPlatform]
                      ? platforms[detectedPlatform].placeholder
                      : "https://www.printables.com/model/123456-example-model"
                  }
                  value={url}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleUrlChange(e.target.value)}
                  disabled={isLoading}
                />
                {detectedPlatform && platforms[detectedPlatform] && (
                  <div className="flex items-center gap-2 mt-1 text-sm text-gray-600">
                    <span>{platforms[detectedPlatform].icon}</span>
                    <span>Detected: {platforms[detectedPlatform].name}</span>
                  </div>
                )}
              </div>
              <Button
                onClick={handlePreview}
                disabled={isLoading || !url.trim()}
                variant="outline"
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  'Preview'
                )}
              </Button>
            </div>
          </div>

          {/* Supported Platforms Info */}
          <div className="text-sm text-gray-600">
            <p className="font-medium mb-2">Supported platforms:</p>
            <div className="flex flex-wrap gap-2">
              {(['printables', 'makerworld', 'thangs'] as ModelSource[]).map((platform) => (
                <Badge key={platform} variant="outline" className="gap-1">
                  <span>{platforms[platform].icon}</span>
                  {platforms[platform].name}
                </Badge>
              ))}
            </div>
          </div>

          {/* Error Alert */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Success Alert */}
          {success && (
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">{success}</AlertDescription>
            </Alert>
          )}

          {/* Model Preview */}
          {previewModel && (
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg">{previewModel.title}</CardTitle>
                    <CardDescription className="flex items-center gap-2 mt-1">
                      <span>by {previewModel.designer.name}</span>
                      <Badge variant="secondary" className="gap-1">
                        <span>{platforms[previewModel.platform]?.icon}</span>
                        {platforms[previewModel.platform]?.name || previewModel.platform}
                      </Badge>
                      {previewModel.isFree && (
                        <Badge variant="outline" className="text-green-600 border-green-600">
                          Free
                        </Badge>
                      )}
                    </CardDescription>
                  </div>
                  {previewModel.externalSource && (
                    <Button
                      variant="ghost"
                      size="sm"
                      asChild
                      className="gap-1"
                    >
                      <a
                        href={previewModel.externalSource.originalUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <ExternalLink className="h-3 w-3" />
                        Оригінал
                      </a>
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Thumbnail */}
                {previewModel.thumbnail && (
                  <div className="aspect-video w-full overflow-hidden rounded-lg bg-gray-100">
                    <img
                      src={previewModel.thumbnail}
                      alt={previewModel.title}
                      className="h-full w-full object-cover"
                    />
                  </div>
                )}

                {/* Description */}
                {previewModel.description && (
                  <div>
                    <h4 className="font-medium mb-2">Опис</h4>
                    <p className="text-sm text-gray-600 line-clamp-3">
                      {previewModel.description}
                    </p>
                  </div>
                )}

                {/* Stats */}
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Лайки:</span>
                    <span className="ml-1">{previewModel.likes.toLocaleString()}</span>
                  </div>
                  <div>
                    <span className="font-medium">Завантаження:</span>
                    <span className="ml-1">{previewModel.downloads.toLocaleString()}</span>
                  </div>
                  <div>
                    <span className="font-medium">Розмір:</span>
                    <span className="ml-1">{previewModel.fileSize}</span>
                  </div>
                </div>

                {/* Tags */}
                {previewModel.tags.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-2">Теги</h4>
                    <div className="flex flex-wrap gap-1">
                      {previewModel.tags.slice(0, 6).map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                      {previewModel.tags.length > 6 && (
                        <Badge variant="secondary" className="text-xs">
                          +{previewModel.tags.length - 6} більше
                        </Badge>
                      )}
                    </div>
                  </div>
                )}

                {/* License */}
                {previewModel.license && (
                  <div>
                    <h4 className="font-medium mb-2">Ліцензія</h4>
                    <div className="text-sm text-gray-600">
                      <p>{previewModel.license.name}</p>
                      <div className="flex gap-4 mt-1 text-xs">
                        <span className={previewModel.license.allowCommercialUse ? 'text-green-600' : 'text-red-600'}>
                          {previewModel.license.allowCommercialUse ? '✓' : '✗'} Комерційне використання
                        </span>
                        <span className={previewModel.license.requireAttribution ? 'text-orange-600' : 'text-green-600'}>
                          {previewModel.license.requireAttribution ? '!' : '✓'} Атрибуція {previewModel.license.requireAttribution ? 'обов\'язкова' : 'не потрібна'}
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          {previewModel && (
            <Button
              onClick={handleImport}
              disabled={isLoading}
              className="gap-2"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Download className="h-4 w-4" />
              )}
              Import Model
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
