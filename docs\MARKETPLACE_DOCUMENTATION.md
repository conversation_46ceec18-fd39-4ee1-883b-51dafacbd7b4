# Документація маркетплейсу 3D-моделей

## Зміст

1. [Вступ](#вступ)
2. [Архітектура маркетплейсу](#архітектура-маркетплейсу)
3. [Функціональність для покупців](#функціональність-для-покупців)
4. [Функціональність для продавців](#функціональність-для-продавців)
5. [Технічні компоненти](#технічні-компоненти)
6. [API маркетплейсу](#api-маркетплейсу)
7. [Інтеграція з платіжними системами](#інтеграція-з-платіжними-системами)
8. [Система підписок (Memberships)](#система-підписок-memberships)
9. [Захист інтелектуальної власності](#захист-інтелектуальної-власності)
10. [Розгортання та масштабування](#розгортання-та-масштабування)

## Вступ

Маркетплейс 3D-моделей - це платформа для купівлі, продажу та обміну 3D-моделями для 3D-друку та інших цілей. Платформа дозволяє дизайнерам монетизувати свої творіння, а покупцям - отримати доступ до якісних 3D-моделей для своїх проектів.

### Ключові особливості

- **Купівля та продаж 3D-моделей**: Користувачі можуть купувати та продавати 3D-моделі з різними ліцензіями
- **Система підписок**: Дизайнери можуть створювати плани підписок для своїх шанувальників
- **Інтерактивний 3D-перегляд**: Перегляд моделей перед покупкою
- **Замовлення друку**: Можливість замовити друк моделі через платформу
- **Пошук локальних друкарень**: Знаходження найближчих 3D-друкарень

## Архітектура маркетплейсу

Маркетплейс побудований на основі сучасного стеку технологій:

- **Frontend**: Next.js 14, React 18, TypeScript, Tailwind CSS
- **3D-візуалізація**: Three.js, React Three Fiber
- **Backend**: Next.js API Routes, Server Components
- **База даних**: Supabase (PostgreSQL)
- **Аутентифікація**: NextAuth.js
- **Платежі**: Stripe
- **Управління станом**: Zustand

### Структура проекту

```
3d-marketplace/
├── docs/                 # Документація
├── public/               # Статичні файли
├── src/
│   ├── app/              # Next.js App Router сторінки
│   │   ├── auth/         # Аутентифікація
│   │   ├── marketplace/  # Сторінки маркетплейсу
│   │   └── models/       # Сторінки моделей
│   ├── components/       # React компоненти
│   │   └── 3d-viewer/    # 3D-переглядач
│   └── lib/              # Утиліти та API
```

## Функціональність для покупців

### Перегляд та пошук моделей

Сторінка маркетплейсу (`/marketplace`) дозволяє користувачам переглядати доступні 3D-моделі з можливістю фільтрації за:

- Категоріями
- Ціною (безкоштовні/платні)
- Популярністю
- Кількістю завантажень
- Новизною

```tsx
// Приклад фільтрації моделей
const filteredModels = MOCK_MODELS.filter(model => {
  const categoryMatch = selectedCategory === 'All' || model.category === selectedCategory;
  const priceMatch = 
    priceFilter === 'all' || 
    (priceFilter === 'free' && model.price === 0) || 
    (priceFilter === 'paid' && model.price > 0);
  
  return categoryMatch && priceMatch;
});
```

### Деталі моделі

Сторінка деталей моделі (`/marketplace/[id]`) надає повну інформацію про модель:

- Зображення та 3D-перегляд
- Опис та теги
- Інформація про дизайнера
- Ціна та ліцензія
- Рекомендовані налаштування друку
- Відгуки користувачів

### Покупка та завантаження

Процес покупки включає:

1. Додавання моделі до кошика
2. Оформлення замовлення через Stripe
3. Отримання доступу до файлів моделі
4. Можливість завантаження в різних форматах (STL, OBJ, 3MF, тощо)

### Замовлення друку

Користувачі можуть замовити друк моделі безпосередньо через платформу:

- Вибір матеріалу та кольору
- Налаштування параметрів друку
- Розрахунок вартості
- Доставка надрукованої моделі

## Функціональність для продавців

### Завантаження моделей

Сторінка завантаження моделей (`/models/upload`) дозволяє дизайнерам:

1. Завантажувати файли 3D-моделей (STL, OBJ, 3MF, GLTF, GLB)
2. Додавати зображення для попереднього перегляду
3. Вказувати деталі моделі (назва, опис, категорія, теги)
4. Встановлювати ціну та ліцензію
5. Додавати рекомендовані налаштування друку

```tsx
// Приклад форми завантаження моделі
<form onSubmit={handleSubmit}>
  {/* Завантаження файлів */}
  <input
    type="file"
    onChange={handleModelFileChange}
    accept=".stl,.obj,.3mf,.gltf,.glb"
    multiple
  />
  
  {/* Деталі моделі */}
  <input
    type="text"
    value={modelName}
    onChange={(e) => setModelName(e.target.value)}
    placeholder="Назва моделі"
    required
  />
  
  {/* Ціна та ліцензія */}
  <input
    type="number"
    value={price}
    onChange={(e) => setPrice(e.target.value)}
    min="0"
    step="0.01"
    required
  />
</form>
```

### Управління моделями

Панель керування дозволяє продавцям:

- Переглядати завантажені моделі
- Редагувати інформацію про моделі
- Відстежувати продажі та статистику
- Отримувати відгуки від покупців

### Створення підписок

Продавці можуть створювати різні плани підписок:

- Щомісячні релізи нових моделей
- Ексклюзивний доступ до колекцій
- Різні рівні підписок з різними перевагами

## Технічні компоненти

### 3D-переглядач

Компонент 3D-переглядача використовує Three.js та React Three Fiber для інтерактивного перегляду моделей:

- Обертання моделі
- Масштабування
- Зміна освітлення
- Зміна фону

### Система аутентифікації

Аутентифікація реалізована за допомогою NextAuth.js:

- Реєстрація та вхід користувачів
- Соціальна аутентифікація (Google, Facebook, GitHub)
- Управління сесіями
- Захист маршрутів

### Система платежів

Інтеграція з Stripe для обробки платежів:

- Одноразові покупки
- Підписки з регулярними платежами
- Управління платіжними методами
- Виплати продавцям

## API маркетплейсу

### Моделі

```typescript
// GET /api/models - отримання списку моделей
// GET /api/models/:id - отримання деталей моделі
// POST /api/models - створення нової моделі
// PUT /api/models/:id - оновлення моделі
// DELETE /api/models/:id - видалення моделі
```

### Користувачі

```typescript
// GET /api/users/:id - отримання профілю користувача
// GET /api/users/:id/models - отримання моделей користувача
// PUT /api/users/:id - оновлення профілю
```

### Замовлення

```typescript
// POST /api/orders - створення нового замовлення
// GET /api/orders/:id - отримання деталей замовлення
// GET /api/users/:id/orders - отримання замовлень користувача
```

## Інтеграція з платіжними системами

### Stripe

Інтеграція з Stripe для обробки платежів:

```typescript
// Приклад створення платежу
const createPayment = async (amount, currency, description) => {
  const paymentIntent = await stripe.paymentIntents.create({
    amount,
    currency,
    description,
    automatic_payment_methods: { enabled: true },
  });
  
  return paymentIntent;
};
```

## Система підписок (Memberships)

### Типи підписок

- **Базова**: Доступ до основних моделей
- **Преміум**: Доступ до всіх моделей та ексклюзивного контенту
- **Про**: Доступ до всіх моделей, ексклюзивного контенту та комерційної ліцензії

### Управління підписками

- Створення та редагування планів підписок
- Відстеження активних підписників
- Автоматичне надання доступу до контенту

## Захист інтелектуальної власності

### Ліцензування

Підтримуються різні типи ліцензій:

- Стандартна (тільки для особистого використання)
- Комерційна (особисте та комерційне використання)
- Creative Commons
- Власні ліцензії

### Захист від несанкціонованого доступу

- Водяні знаки на зображеннях
- Захищені посилання для завантаження
- Відстеження порушень авторських прав

## Розгортання та масштабування

### Розгортання на Cloudflare

Проект налаштований для розгортання на Cloudflare Pages:

- Автоматичне розгортання з GitHub
- Глобальна мережа доставки контенту (CDN)
- Захист від DDoS-атак

### Масштабування

- Горизонтальне масштабування серверів
- Кешування та оптимізація запитів
- Оптимізація зберігання та доставки 3D-моделей
