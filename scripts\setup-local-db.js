#!/usr/bin/env node

/**
 * Скрипт для налаштування локального середовища розробки з Cloudflare D1
 * Створює локальну базу даних D1 та застосовує схему
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Шлях до файлу схеми
const schemaPath = path.join(__dirname, '..', 'schema.sql');

// Перевірка наявності файлу схеми
if (!fs.existsSync(schemaPath)) {
  console.error('Schema file not found:', schemaPath);
  process.exit(1);
}

// Перевірка наявності wrangler.toml
const wranglerPath = path.join(__dirname, '..', 'wrangler.toml');
if (!fs.existsSync(wranglerPath)) {
  console.error('wrangler.toml not found:', wranglerPath);
  process.exit(1);
}

// Читання wrangler.toml
const wranglerConfig = fs.readFileSync(wranglerPath, 'utf8');

// Отримання назви бази даних
const databaseNameMatch = wranglerConfig.match(/database_name\s*=\s*"([^"]+)"/);
if (!databaseNameMatch) {
  console.error('Database name not found in wrangler.toml');
  process.exit(1);
}

const databaseName = databaseNameMatch[1];

console.log(`Setting up local D1 database: ${databaseName}`);

try {
  // Створення локальної бази даних
  console.log('Creating local database...');
  execSync(`npx wrangler d1 create ${databaseName} --local`, { stdio: 'inherit' });
  
  // Застосування схеми до локальної бази даних
  console.log('Applying schema...');
  execSync(`npx wrangler d1 execute ${databaseName} --local --file=${schemaPath}`, { stdio: 'inherit' });
  
  console.log('Local database setup complete!');
  console.log('\nTo start the development server with D1:');
  console.log(`npx wrangler pages dev --d1=${databaseName} .next`);
} catch (error) {
  console.error('Error setting up local database:', error.message);
  process.exit(1);
}
