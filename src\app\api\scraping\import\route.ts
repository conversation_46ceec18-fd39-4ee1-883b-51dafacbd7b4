/**
 * General model import API endpoint
 * Supports importing from multiple platforms: Printables, MakerWorld, Thangs
 */

import { NextRequest, NextResponse } from 'next/server';
import { printablesScraper } from '@/lib/api/printables';
import { makerWorldScraper } from '@/lib/api/makerworld';
import { thangsScraper } from '@/lib/api/thangs';
import { platformRateLimiters, RateLimitError } from '@/lib/scraping/rate-limiter';
import { DataNormalizer } from '@/lib/scraping/data-normalizer';
import { ScrapedModel, ModelSource } from '@/types/models';

interface ImportRequest {
  url: string;
  options?: {
    includeFiles?: boolean;
    includeImages?: boolean;
    validateLicense?: boolean;
    autoPublish?: boolean;
  };
}

interface ImportResponse {
  success: boolean;
  data?: {
    modelId: string;
    status: 'imported' | 'failed';
    model: any;
  };
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

/**
 * Detect platform from URL
 */
function detectPlatform(url: string): ModelSource | null {
  if (printablesScraper.validateUrl(url)) return 'printables';
  if (makerWorldScraper.validateUrl(url)) return 'makerworld';
  if (thangsScraper.validateUrl(url)) return 'thangs';
  return null;
}

/**
 * Get appropriate scraper for platform
 */
function getScraper(platform: ModelSource) {
  switch (platform) {
    case 'printables':
      return printablesScraper;
    case 'makerworld':
      return makerWorldScraper;
    case 'thangs':
      return thangsScraper;
    default:
      throw new Error(`Unsupported platform: ${platform}`);
  }
}

/**
 * POST /api/scraping/import
 * Import a model from external platform
 */
export async function POST(request: NextRequest): Promise<NextResponse<ImportResponse>> {
  try {
    const body: ImportRequest = await request.json();
    const { url, options = {} } = body;

    // Validate input
    if (!url || typeof url !== 'string') {
      return NextResponse.json({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'URL is required and must be a string',
        },
      }, { status: 400 });
    }

    // Detect platform
    const platform = detectPlatform(url);
    if (!platform) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'UNSUPPORTED_PLATFORM',
          message: 'URL is not from a supported platform (Printables, MakerWorld, Thangs)',
          details: { url },
        },
      }, { status: 400 });
    }

    // Check rate limits
    try {
      await platformRateLimiters.consume(platform, 'default');
    } catch (error) {
      if (error instanceof RateLimitError) {
        return NextResponse.json({
          success: false,
          error: {
            code: 'RATE_LIMIT_EXCEEDED',
            message: `Rate limit exceeded for ${platform}. Try again in ${Math.ceil(error.rateLimitInfo.msBeforeNext / 1000)} seconds.`,
            details: {
              platform,
              retryAfter: Math.ceil(error.rateLimitInfo.msBeforeNext / 1000),
              remainingPoints: error.rateLimitInfo.remainingPoints,
            },
          },
        }, { status: 429 });
      }
      throw error;
    }

    // Get scraper and scrape model
    const scraper = getScraper(platform);
    let scrapedModel: ScrapedModel;

    try {
      scrapedModel = await scraper.scrapeModel(url);
    } catch (error: any) {
      return NextResponse.json({
        success: false,
        error: {
          code: error.code || 'SCRAPING_FAILED',
          message: error.message || 'Failed to scrape model',
          details: {
            platform,
            url,
            originalError: error.message,
          },
        },
      }, { status: 500 });
    }

    // Validate scraped data
    const validation = DataNormalizer.validateScrapedModel(scrapedModel);
    if (!validation.isValid) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'INVALID_SCRAPED_DATA',
          message: 'Scraped data failed validation',
          details: {
            errors: validation.errors,
            platform,
            url,
          },
        },
      }, { status: 500 });
    }

    // Sanitize scraped data
    const sanitizedModel = DataNormalizer.sanitizeScrapedModel(scrapedModel);

    // Convert to internal format
    const internalModel = DataNormalizer.convertToInternalModel(sanitizedModel);

    // TODO: Save to database
    // const savedModel = await saveModelToDatabase(internalModel);

    // Return success response
    return NextResponse.json({
      success: true,
      data: {
        modelId: internalModel.id,
        status: 'imported' as const,
        model: {
          id: internalModel.id,
          title: internalModel.title,
          platform: sanitizedModel.platform,
          originalUrl: sanitizedModel.originalUrl,
          license: internalModel.license,
          importedAt: sanitizedModel.scrapedAt,
        },
      },
    });

  } catch (error) {
    console.error('Import API error:', error);
    
    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An unexpected error occurred during import',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      },
    }, { status: 500 });
  }
}

/**
 * GET /api/scraping/import
 * Get import status or health check
 */
export async function GET(): Promise<NextResponse> {
  try {
    const rateLimitStatus = await platformRateLimiters.getAllRemainingPoints();
    
    return NextResponse.json({
      success: true,
      data: {
        status: 'operational',
        supportedPlatforms: ['printables', 'makerworld', 'thangs'],
        rateLimits: {
          printables: {
            remaining: rateLimitStatus.printables || 0,
            limit: 10,
          },
          makerworld: {
            remaining: rateLimitStatus.makerworld || 0,
            limit: 15,
          },
          thangs: {
            remaining: rateLimitStatus.thangs || 0,
            limit: 12,
          },
        },
      },
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: {
        code: 'HEALTH_CHECK_FAILED',
        message: 'Failed to get import service status',
      },
    }, { status: 500 });
  }
}

/**
 * OPTIONS /api/scraping/import
 * CORS preflight
 */
export async function OPTIONS(): Promise<NextResponse> {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
