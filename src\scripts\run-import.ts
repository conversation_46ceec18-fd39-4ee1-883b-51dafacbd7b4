#!/usr/bin/env tsx

/**
 * Скрипт для запуску імпорту популярних моделей
 * Використання: npm run import-popular [type] [platform]
 */

import { popularModelsImporter } from './import-popular-models';

async function main() {
  const args = process.argv.slice(2);
  const type = args[0] || 'sample';
  const platform = args[1];

  console.log('🚀 3D Marketplace - Імпорт популярних моделей');
  console.log('=' .repeat(50));

  try {
    switch (type) {
      case 'all':
        console.log('📥 Запуск повного імпорту всіх популярних моделей...');
        await popularModelsImporter.importAllPopularModels();
        break;

      case 'sample':
        console.log('🎲 Запуск імпорту зразків (3 моделі з кожної платформи)...');
        await popularModelsImporter.importSampleModels(3);
        break;

      case 'platform':
        if (!platform) {
          console.error('❌ Помилка: Вкажіть платформу для імпорту');
          console.log('Доступні платформи: thingiverse, myminifactory, printables, thangs, makerworld');
          process.exit(1);
        }
        console.log(`🎯 Запуск імпорту з платформи: ${platform}`);
        await popularModelsImporter.importFromPlatform(platform as any);
        break;

      default:
        console.error(`❌ Невідомий тип імпорту: ${type}`);
        console.log('Доступні типи: all, sample, platform');
        process.exit(1);
    }

    console.log('\n🎉 Імпорт успішно завершено!');
    console.log('📊 Перевірте дашборд моніторингу для детальної статистики');

  } catch (error) {
    console.error('💥 Помилка під час імпорту:', error);
    process.exit(1);
  }
}

// Запуск скрипту
if (require.main === module) {
  main().catch(console.error);
}
