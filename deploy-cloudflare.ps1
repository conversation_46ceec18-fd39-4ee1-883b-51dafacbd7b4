# 🚀 Cloudflare Deployment Script for 3D Marketplace (PowerShell)
# This script deploys your entire 3D marketplace to Cloudflare

Write-Host "🎨 3D Marketplace - Cloudflare Deployment" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host ""

# Helper functions
function Write-Step {
    param($Message)
    Write-Host "📦 $Message" -ForegroundColor Blue
}

function Write-Success {
    param($Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Warning {
    param($Message)
    Write-Host "⚠️  $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

# Check if wrangler is installed
function Test-Wrangler {
    Write-Step "Checking Wrangler CLI..."
    try {
        $null = Get-Command wrangler -ErrorAction Stop
        Write-Success "Wrangler CLI is installed"
        return $true
    }
    catch {
        Write-Warning "Wrangler CLI not found. Installing..."
        try {
            npm install -g wrangler
            Write-Success "Wrangler CLI installed successfully"
            return $true
        }
        catch {
            Write-Error "Failed to install Wrangler CLI"
            return $false
        }
    }
}

# Check authentication
function Test-Auth {
    Write-Step "Checking Cloudflare authentication..."
    try {
        $result = wrangler whoami 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Authenticated with Cloudflare"
            return $true
        }
        else {
            Write-Error "Not authenticated with Cloudflare"
            Write-Host "Please run: wrangler login" -ForegroundColor Yellow
            return $false
        }
    }
    catch {
        Write-Error "Authentication check failed"
        return $false
    }
}

# Build the project
function Build-Project {
    Write-Step "Building Next.js application..."
    try {
        npm run build
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Build completed successfully"
            return $true
        }
        else {
            Write-Error "Build failed"
            return $false
        }
    }
    catch {
        Write-Error "Build process failed"
        return $false
    }
}

# Create Cloudflare resources
function New-CloudflareResources {
    Write-Step "Creating Cloudflare resources..."
    
    # Create D1 Database
    Write-Step "Creating D1 database..."
    try {
        wrangler d1 create marketplace-db 2>$null
        Write-Success "D1 database created"
    }
    catch {
        Write-Warning "Database might already exist"
    }
    
    # Create R2 bucket
    Write-Step "Creating R2 storage bucket..."
    try {
        wrangler r2 bucket create marketplace-storage 2>$null
        Write-Success "R2 bucket created"
    }
    catch {
        Write-Warning "Bucket might already exist"
    }
    
    # Create KV namespace
    Write-Step "Creating KV namespace..."
    try {
        wrangler kv:namespace create "CACHE_KV" 2>$null
        Write-Success "KV namespace created"
    }
    catch {
        Write-Warning "KV namespace might already exist"
    }
    
    Write-Success "Resources creation completed"
    return $true
}

# Deploy to Cloudflare Pages
function Deploy-Pages {
    Write-Step "Deploying to Cloudflare Pages..."
    
    try {
        wrangler pages deploy .next --project-name=3d-marketplace --compatibility-date=2023-10-30
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Deployment to Cloudflare Pages completed"
            return $true
        }
        else {
            Write-Error "Deployment failed"
            return $false
        }
    }
    catch {
        Write-Error "Deployment process failed"
        return $false
    }
}

# Set environment variables
function Set-EnvironmentVariables {
    Write-Step "Setting environment variables..."
    
    try {
        # Set secrets
        "your-secret-key-here" | wrangler pages secret put NEXTAUTH_SECRET --project-name=3d-marketplace 2>$null
        "production" | wrangler pages secret put NODE_ENV --project-name=3d-marketplace 2>$null
        
        Write-Success "Environment variables set"
        return $true
    }
    catch {
        Write-Warning "Some environment variables could not be set"
        return $true
    }
}

# Main deployment function
function Start-Deployment {
    Write-Host "Starting deployment process..." -ForegroundColor Cyan
    Write-Host ""
    
    # Step 1: Check prerequisites
    if (-not (Test-Wrangler)) {
        exit 1
    }
    
    if (-not (Test-Auth)) {
        exit 1
    }
    
    # Step 2: Build project
    if (-not (Build-Project)) {
        exit 1
    }
    
    # Step 3: Create Cloudflare resources
    if (-not (New-CloudflareResources)) {
        exit 1
    }
    
    # Step 4: Deploy to Pages
    if (-not (Deploy-Pages)) {
        exit 1
    }
    
    # Step 5: Set environment variables
    Set-EnvironmentVariables
    
    # Success message
    Write-Host ""
    Write-Host "🎉 Deployment completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📱 Your 3D Marketplace is now live at:" -ForegroundColor Cyan
    Write-Host "   https://3d-marketplace.pages.dev" -ForegroundColor White
    Write-Host ""
    Write-Host "🔧 Next steps:" -ForegroundColor Cyan
    Write-Host "   1. Visit your site and test functionality" -ForegroundColor White
    Write-Host "   2. Go to /admin/scraper to generate test data" -ForegroundColor White
    Write-Host "   3. Check /marketplace to see your models" -ForegroundColor White
    Write-Host "   4. Configure custom domain if needed" -ForegroundColor White
    Write-Host ""
    Write-Host "📚 Useful commands:" -ForegroundColor Cyan
    Write-Host "   wrangler pages deployment list --project-name=3d-marketplace" -ForegroundColor White
    Write-Host "   wrangler d1 info marketplace-db" -ForegroundColor White
    Write-Host "   wrangler r2 bucket list" -ForegroundColor White
    Write-Host ""
}

# Run deployment
Start-Deployment
