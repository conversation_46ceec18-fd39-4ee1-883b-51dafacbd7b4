import { NextRequest, NextResponse } from 'next/server';
import { cloudflareMonitoring } from '@/lib/observability/cloudflare-monitoring';
import { jobQueueManager } from '@/lib/queue/job-queue-manager';
import { errorHandler } from '@/lib/error-handling/enhanced-error-handler';

/**
 * GET /api/monitoring/metrics
 * Отримує метрики системи для дашборду
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '-24 hours';
    const platform = searchParams.get('platform');

    // Отримання метрик скрапінгу
    const scrapingMetrics = await cloudflareMonitoring.getScrapingMetrics(
      platform as any, 
      timeRange
    );

    // Отримання метрик черги завдань
    const jobQueueMetrics = await cloudflareMonitoring.getJobQueueMetrics();

    // Отримання статистики черги завдань
    const queueStats = jobQueueManager.getQueueStats();

    // Отримання статистики помилок
    const errorStats = errorHandler.getErrorStats();

    // Загальні метрики системи
    const systemMetrics = {
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      timestamp: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      data: {
        scraping: scrapingMetrics,
        jobQueue: {
          ...jobQueueMetrics,
          stats: queueStats
        },
        errors: errorStats,
        system: systemMetrics,
        summary: {
          totalPlatforms: scrapingMetrics.length,
          totalActiveJobs: queueStats.processing,
          totalErrors: errorStats.total,
          healthScore: calculateHealthScore(scrapingMetrics, jobQueueMetrics, errorStats)
        }
      }
    });

  } catch (error) {
    const enhancedError = await errorHandler.handleError(error as Error, {
      url: request.url,
      userAgent: request.headers.get('user-agent') || undefined
    });

    return NextResponse.json(
      { 
        success: false, 
        error: { 
          message: enhancedError.userMessage,
          code: enhancedError.code,
          category: enhancedError.category
        }
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/monitoring/metrics
 * Записує кастомну метрику
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json() as any;
    const { name, value, unit, tags } = body;

    if (!name || typeof value !== 'number') {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: 'Name and numeric value are required',
            code: 'INVALID_METRIC_DATA'
          }
        },
        { status: 400 }
      );
    }

    await cloudflareMonitoring.recordMetric({
      name,
      value,
      unit,
      tags
    });

    return NextResponse.json({
      success: true,
      data: {
        message: 'Metric recorded successfully',
        metric: { name, value, unit, tags }
      }
    });

  } catch (error) {
    const enhancedError = await errorHandler.handleError(error as Error, {
      url: request.url,
      userAgent: request.headers.get('user-agent') || undefined
    });

    return NextResponse.json(
      { 
        success: false, 
        error: { 
          message: enhancedError.userMessage,
          code: enhancedError.code,
          category: enhancedError.category
        }
      },
      { status: 500 }
    );
  }
}

/**
 * Розрахунок загального показника здоров'я системи
 */
function calculateHealthScore(
  scrapingMetrics: any[], 
  jobQueueMetrics: any, 
  errorStats: any
): number {
  let score = 100;

  // Зменшення за помилки скрапінгу
  scrapingMetrics.forEach(platform => {
    const errorRate = platform.failedRequests / (platform.totalRequests || 1);
    score -= errorRate * 20; // До 20 балів за платформу
  });

  // Зменшення за проблеми з чергою
  const queueErrorRate = jobQueueMetrics.failedJobs / (jobQueueMetrics.totalJobs || 1);
  score -= queueErrorRate * 30;

  // Зменшення за загальні помилки
  if (errorStats.total > 0) {
    const criticalErrors = errorStats.bySeverity.critical || 0;
    const highErrors = errorStats.bySeverity.high || 0;
    
    score -= criticalErrors * 10;
    score -= highErrors * 5;
  }

  return Math.max(0, Math.min(100, Math.round(score)));
}
