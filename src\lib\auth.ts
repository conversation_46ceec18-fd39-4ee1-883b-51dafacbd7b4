import { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import GoogleProvider from "next-auth/providers/google";
import GitHubProvider from "next-auth/providers/github";
import { D1Adapter } from "./auth/d1-adapter";
import { getDb, queryOne } from "./db";
import { compare, hash } from "bcryptjs";

export const authOptions: NextAuthOptions = {
  // Налаштування адаптера для Cloudflare D1 (якщо доступно)
  // adapter: getDb() ? D1Adapter(getDb()!) : undefined,

  // Налаштування сесій
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 днів
  },

  // Налаштування для Cloudflare
  secret: process.env.NEXTAUTH_SECRET,

  // Налаштування сторінок аутентифікації
  pages: {
    signIn: "/auth/signin",
    signOut: "/auth/signout",
    error: "/auth/error",
    verifyRequest: "/auth/verify-request",
    newUser: "/auth/new-user",
  },

  // Налаштування провайдерів аутентифікації
  providers: [
    // Аутентифікація за допомогою облікових даних
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        // Перевірка, чи доступна база даних
        const db = getDb();
        if (!db) {
          console.warn('База даних недоступна. Використовується тестовий режим автентифікації.');

          // В режимі розробки дозволяємо вхід з тестовими обліковими даними
          if (credentials.email === '<EMAIL>' && credentials.password === 'password') {
            return {
              id: '1',
              email: '<EMAIL>',
              name: 'Тестовий користувач',
              image: null,
            };
          }

          return null;
        }

        // Пошук користувача за електронною поштою
        const user = await queryOne<{
          id: string;
          email: string;
          password: string;
          name: string | null;
          avatar_url: string | null;
        }>(
          "SELECT id, email, password, name, avatar_url FROM users WHERE email = ?",
          [credentials.email]
        );

        if (!user || !user.password) {
          return null;
        }

        // Перевірка пароля
        const isPasswordValid = await compare(credentials.password, user.password);

        if (!isPasswordValid) {
          return null;
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          image: user.avatar_url,
        };
      },
    }),

    // Аутентифікація через Google
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
    }),

    // Аутентифікація через GitHub
    GitHubProvider({
      clientId: process.env.GITHUB_ID as string,
      clientSecret: process.env.GITHUB_SECRET as string,
    }),
  ],

  // Налаштування зворотних викликів
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
      }
      return token;
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.id as string;
      }
      return session;
    },
  },
};

/**
 * Хешує пароль
 * @param password Пароль для хешування
 * @returns Хешований пароль
 */
export async function hashPassword(password: string): Promise<string> {
  return await hash(password, 12);
}

/**
 * Перевіряє пароль
 * @param password Пароль для перевірки
 * @param hashedPassword Хешований пароль
 * @returns true, якщо пароль правильний
 */
export async function verifyPassword(
  password: string,
  hashedPassword: string
): Promise<boolean> {
  return await compare(password, hashedPassword);
}
