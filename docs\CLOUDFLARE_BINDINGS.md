# Використання Cloudflare Bindings MCP

Цей документ описує, як використовувати Cloudflare Bindings MCP (Magic Cloudflare Platform) у проекті 3D-маркетплейсу.

## Огляд

Cloudflare Bindings MCP - це інтеграція, яка дозволяє легко використовувати сервіси Cloudflare (D1, R2, KV, Workers, Hyperdrive тощо) у вашому Next.js проекті. Вона забезпечує:

- Типізований доступ до Cloudflare сервісів
- Спрощену інтеграцію з Next.js
- Єдиний інтерфейс для роботи з різними сервісами Cloudflare

## Налаштування

### 1. Встановлення залежностей

```bash
npm install @cloudflare/workers-types
```

### 2. Налаштування wrangler.toml

Файл `wrangler.toml` повинен містити налаштування для всіх сервісів Cloudflare, які ви хочете використовувати:

```toml
# Configure D1 database
[[d1_databases]]
binding = "DB" # Доступно як env.DB
database_name = "marketplace_db"
database_id = "your-database-id"

# Configure R2 storage
[[r2_buckets]]
binding = "STORAGE" # Доступно як env.STORAGE
bucket_name = "marketplace-storage"

# Configure KV namespace
[[kv_namespaces]]
binding = "KV" # Доступно як env.KV
id = "your-kv-namespace-id"

# Configure Worker service binding
[[services]]
binding = "WORKER" # Доступно як env.WORKER
service = "your-worker-name"

# Configure Hyperdrive
[[hyperdrive]]
binding = "HYPERDRIVE" # Доступно як env.HYPERDRIVE
id = "your-hyperdrive-id"
```

### 3. Створення сервісів Cloudflare

```bash
# Створення бази даних D1
npm run db:create

# Створення бакета R2
npm run r2:create

# Створення KV namespace
npm run kv:create

# Створення Worker
npm run worker:create

# Створення Hyperdrive
npm run hyperdrive:create
```

## Структура проекту

```plaintext
src/lib/cloudflare/
├── index.ts       # Головний файл для експорту
├── d1.ts          # Утиліти для роботи з D1
├── r2.ts          # Утиліти для роботи з R2
├── kv.ts          # Утиліти для роботи з KV
├── workers.ts     # Утиліти для роботи з Workers
├── hyperdrive.ts  # Утиліти для роботи з Hyperdrive
├── middleware.ts  # Middleware для API маршрутів
└── types.ts       # Типи для Cloudflare Bindings
```

## Використання в серверних компонентах

### D1 (База даних)

```typescript
import { query, queryOne, transaction, queryWithPagination } from '@/lib/cloudflare/d1';

// Отримання списку моделей
const models = await query('SELECT * FROM models');

// Отримання однієї моделі
const model = await queryOne('SELECT * FROM models WHERE id = ?', [modelId]);

// Виконання транзакції
const result = await transaction(async (db) => {
  await db.prepare('INSERT INTO users (id, name) VALUES (?, ?)').bind(userId, name).run();
  await db.prepare('INSERT INTO profiles (user_id) VALUES (?)').bind(userId).run();
  return { success: true };
});

// Запит з пагінацією
const paginatedResult = await queryWithPagination(
  'SELECT * FROM models WHERE category = ?',
  [category],
  page,
  limit
);
```

### R2 (Зберігання файлів)

```typescript
import { uploadFile, getFile, deleteFile, getFileUrl, generateFileKey } from '@/lib/cloudflare/r2';

// Генерація ключа для файлу
const fileKey = generateFileKey(file.name, 'models');

// Завантаження файлу
const result = await uploadFile(fileKey, file, { userId, modelId });

// Отримання файлу
const fileObject = await getFile(fileKey);
if (fileObject) {
  const content = await fileObject.text(); // або fileObject.arrayBuffer(), fileObject.json()
}

// Видалення файлу
await deleteFile(fileKey);

// Отримання URL файлу
const fileUrl = getFileUrl(fileKey);
```

### KV (Ключ-значення сховище)

```typescript
import { setValue, getValue, getJSON, deleteValue, listKeys, getAllByPrefix } from '@/lib/cloudflare/kv';

// Запис значення
await setValue('user:123', { name: 'John', email: '<EMAIL>' });

// Отримання значення як рядка
const value = await getValue('user:123');

// Отримання значення як JSON
const user = await getJSON('user:123');

// Видалення значення
await deleteValue('user:123');

// Отримання списку ключів
const keys = await listKeys({ prefix: 'user:' });

// Отримання всіх значень за префіксом
const allUsers = await getAllByPrefix('user:');
```

### Workers

```typescript
import { fetchWorker, fetchWorkerJSON, getWorkerData, postWorkerData } from '@/lib/cloudflare/workers';

// Виконання запиту до Worker
const response = await fetchWorker('/api/data');

// Отримання даних як JSON
const data = await fetchWorkerJSON('/api/data');

// GET-запит
const getResponse = await getWorkerData('/api/data');

// POST-запит
const postResponse = await postWorkerData('/api/data', JSON.stringify({ name: 'John' }));
```

### Hyperdrive

```typescript
import { queryHyperdrive, queryHyperdriveOne, hyperdriveTransaction } from '@/lib/cloudflare/hyperdrive';

// Виконання запиту
const results = await queryHyperdrive('SELECT * FROM users');

// Отримання одного запису
const user = await queryHyperdriveOne('SELECT * FROM users WHERE id = $1', [userId]);

// Виконання транзакції
const result = await hyperdriveTransaction(async (hyperdrive) => {
  await hyperdrive.query('INSERT INTO users (id, name) VALUES ($1, $2)', [userId, name]);
  return { success: true };
});
```

## Використання в API маршрутах

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { withApiMiddleware } from '@/lib/cloudflare/middleware';

export const GET = withApiMiddleware(async (
  _request: NextRequest,
  { db, storage, kv, worker, hyperdrive }
) => {
  // Запит до D1
  const models = await db.prepare('SELECT * FROM models').all();

  // Запит до R2
  const files = await storage.list({ limit: 10 });

  // Запит до KV
  const value = await kv.get('key', { type: 'json' });

  // Запит до Worker
  const workerResponse = await worker.fetch(new Request('http://worker/api/data'));

  // Запит до Hyperdrive
  const hyperdriveResult = await hyperdrive.query('SELECT * FROM users');

  return NextResponse.json({
    success: true,
    data: {
      models: models.results,
      files: files.objects,
      kv: value,
      worker: await workerResponse.json(),
      hyperdrive: hyperdriveResult.rows,
    }
  });
});
```

## Локальна розробка

Для локальної розробки з Cloudflare Bindings використовуйте наступну команду:

```bash
# Запуск з D1 та R2
npm run dev:cloudflare

# Запуск з усіма сервісами
npm run dev:full
```

## Транзакції в D1

Для виконання транзакцій в D1 використовуйте функцію `transaction`:

```typescript
import { transaction } from '@/lib/cloudflare/d1';

const result = await transaction(async (db) => {
  // Виконання запитів в транзакції
  await db.prepare('INSERT INTO users (id, name) VALUES (?, ?)').bind(userId, name).run();
  await db.prepare('INSERT INTO profiles (user_id) VALUES (?)').bind(userId).run();

  return { success: true };
});
```

## Пагінація в D1

Для виконання запитів з пагінацією використовуйте функцію `queryWithPagination`:

```typescript
import { queryWithPagination } from '@/lib/cloudflare/d1';

const result = await queryWithPagination(
  'SELECT * FROM models WHERE category = ?',
  [category],
  page,
  limit
);

// result містить:
// - data: масив записів
// - total: загальна кількість записів
// - page: поточна сторінка
// - limit: кількість записів на сторінці
// - totalPages: загальна кількість сторінок
```

## Робота з файлами в R2

### Завантаження файлу

```typescript
import { uploadFile, generateFileKey } from '@/lib/cloudflare/r2';

// Генерація унікального ключа для файлу
const fileKey = generateFileKey(file.name, 'models');

// Завантаження файлу
const result = await uploadFile(fileKey, file, {
  userId: user.id,
  modelId: model.id,
});

// Отримання URL файлу
const fileUrl = getFileUrl(fileKey);
```

### Отримання файлу

```typescript
import { getFile } from '@/lib/cloudflare/r2';

const file = await getFile('path/to/file.jpg');

if (file) {
  // Отримання вмісту файлу
  const content = await file.text(); // або file.arrayBuffer(), file.json()
}
```

### Видалення файлу

```typescript
import { deleteFile } from '@/lib/cloudflare/r2';

await deleteFile('path/to/file.jpg');
```

## Розгортання на Cloudflare Pages

Для розгортання проекту на Cloudflare Pages з підтримкою Bindings:

```bash
npm run deploy
```

## Додаткові ресурси

- [Документація Cloudflare D1](https://developers.cloudflare.com/d1/)
- [Документація Cloudflare R2](https://developers.cloudflare.com/r2/)
- [Документація Cloudflare KV](https://developers.cloudflare.com/kv/)
- [Документація Cloudflare Workers](https://developers.cloudflare.com/workers/)
- [Документація Cloudflare Hyperdrive](https://developers.cloudflare.com/hyperdrive/)
- [Документація Cloudflare Pages](https://developers.cloudflare.com/pages/)
