'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  Download, 
  Users, 
  Package, 
  DollarSign, 
  Heart,
  Eye,
  Calendar,
  Star
} from 'lucide-react';

interface MarketplaceStats {
  totalModels: number;
  totalDownloads: number;
  totalUsers: number;
  totalRevenue: number;
  freeModels: number;
  paidModels: number;
  featuredModels: number;
  totalViews: number;
  totalLikes: number;
  averageRating: number;
  topCategories: Array<{
    name: string;
    count: number;
    percentage: number;
  }>;
  recentActivity: Array<{
    type: 'upload' | 'download' | 'purchase';
    modelName: string;
    userName: string;
    timestamp: string;
  }>;
  monthlyGrowth: {
    models: number;
    downloads: number;
    users: number;
    revenue: number;
  };
}

export default function MarketplaceStats() {
  const [stats, setStats] = useState<MarketplaceStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/marketplace/stats');
        if (!response.ok) {
          throw new Error('Failed to fetch stats');
        }
        const data = await response.json();
        setStats(data.data);
      } catch (error) {
        console.error('Error fetching marketplace stats:', error);
        setError('Failed to load statistics');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {Array.from({ length: 8 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-20"></div>
                  <div className="h-6 bg-gray-200 rounded w-16"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error || !stats) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">{error || 'No statistics available'}</p>
      </div>
    );
  }

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Основна статистика */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-blue-100 rounded-lg">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Всього моделей</p>
                <p className="text-2xl font-bold text-gray-900">{formatNumber(stats.totalModels)}</p>
                {stats.monthlyGrowth.models > 0 && (
                  <p className="text-xs text-green-600 flex items-center">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    +{stats.monthlyGrowth.models} цього місяця
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-green-100 rounded-lg">
                <Download className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Завантажень</p>
                <p className="text-2xl font-bold text-gray-900">{formatNumber(stats.totalDownloads)}</p>
                {stats.monthlyGrowth.downloads > 0 && (
                  <p className="text-xs text-green-600 flex items-center">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    +{formatNumber(stats.monthlyGrowth.downloads)} цього місяця
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-purple-100 rounded-lg">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Користувачів</p>
                <p className="text-2xl font-bold text-gray-900">{formatNumber(stats.totalUsers)}</p>
                {stats.monthlyGrowth.users > 0 && (
                  <p className="text-xs text-green-600 flex items-center">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    +{stats.monthlyGrowth.users} цього місяця
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-yellow-100 rounded-lg">
                <DollarSign className="h-6 w-6 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Дохід</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.totalRevenue)}</p>
                {stats.monthlyGrowth.revenue > 0 && (
                  <p className="text-xs text-green-600 flex items-center">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    +{formatCurrency(stats.monthlyGrowth.revenue)} цього місяця
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Додаткова статистика */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Безкоштовні</p>
                <p className="text-xl font-bold text-green-600">{formatNumber(stats.freeModels)}</p>
              </div>
              <Badge variant="outline" className="text-green-600 border-green-600">
                {((stats.freeModels / stats.totalModels) * 100).toFixed(1)}%
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Платні</p>
                <p className="text-xl font-bold text-blue-600">{formatNumber(stats.paidModels)}</p>
              </div>
              <Badge variant="outline" className="text-blue-600 border-blue-600">
                {((stats.paidModels / stats.totalModels) * 100).toFixed(1)}%
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-red-100 rounded-lg">
                <Heart className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Лайків</p>
                <p className="text-xl font-bold text-gray-900">{formatNumber(stats.totalLikes)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-indigo-100 rounded-lg">
                <Eye className="h-5 w-5 text-indigo-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Переглядів</p>
                <p className="text-xl font-bold text-gray-900">{formatNumber(stats.totalViews)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Топ категорії */}
      <Card>
        <CardHeader>
          <CardTitle>Популярні категорії</CardTitle>
          <CardDescription>Розподіл моделей за категоріями</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {stats.topCategories.map((category, index) => (
              <div key={category.name} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-sm font-medium">
                    {index + 1}
                  </div>
                  <span className="font-medium">{category.name}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">{category.count} моделей</span>
                  <Badge variant="secondary">{category.percentage.toFixed(1)}%</Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Остання активність */}
      <Card>
        <CardHeader>
          <CardTitle>Остання активність</CardTitle>
          <CardDescription>Нещодавні дії в маркетплейсі</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {stats.recentActivity.slice(0, 5).map((activity, index) => (
              <div key={index} className="flex items-center space-x-4">
                <div className={`p-2 rounded-lg ${
                  activity.type === 'upload' ? 'bg-blue-100' :
                  activity.type === 'download' ? 'bg-green-100' : 'bg-yellow-100'
                }`}>
                  {activity.type === 'upload' && <Package className="h-4 w-4 text-blue-600" />}
                  {activity.type === 'download' && <Download className="h-4 w-4 text-green-600" />}
                  {activity.type === 'purchase' && <DollarSign className="h-4 w-4 text-yellow-600" />}
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium">
                    {activity.userName} {
                      activity.type === 'upload' ? 'завантажив' :
                      activity.type === 'download' ? 'скачав' : 'купив'
                    } "{activity.modelName}"
                  </p>
                  <p className="text-xs text-gray-500 flex items-center">
                    <Calendar className="h-3 w-3 mr-1" />
                    {new Date(activity.timestamp).toLocaleString('uk-UA')}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
