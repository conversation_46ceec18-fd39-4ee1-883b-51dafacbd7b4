# 🚀 Deployment Guide: 3D Marketplace Scraping System

## 📋 Pre-Deployment Checklist

### ✅ System Verification
- [ ] All tests passing (`npm run test:scraping`)
- [ ] System tests successful (`npm run test:system`)
- [ ] Build process completes without errors (`npm run build`)
- [ ] TypeScript compilation successful
- [ ] No critical security vulnerabilities

### ✅ Environment Preparation
- [ ] Production environment variables configured
- [ ] Database connections tested
- [ ] External API access verified
- [ ] CDN and storage configured
- [ ] Monitoring and logging setup

## 🌐 Deployment Options

### Option 1: Vercel (Recommended)

**Advantages:**
- Seamless Next.js integration
- Automatic deployments from Git
- Built-in performance monitoring
- Global CDN included
- Serverless functions support

**Steps:**
1. **Connect Repository**
   ```bash
   # Push to GitHub if not already done
   git push origin main
   ```

2. **Vercel Setup**
   - Visit [vercel.com](https://vercel.com)
   - Import your GitHub repository
   - Configure project settings

3. **Environment Variables**
   ```env
   # Required for production
   NEXTAUTH_SECRET=your-production-secret
   NEXTAUTH_URL=https://your-domain.vercel.app
   
   # Database
   DATABASE_URL=your-production-database-url
   
   # Scraping Configuration
   RATE_LIMIT_PRINTABLES=10
   RATE_LIMIT_MAKERWORLD=15
   RATE_LIMIT_THANGS=12
   SCRAPING_TIMEOUT=30000
   SCRAPING_USER_AGENT=3D-Marketplace-Bot/1.0
   
   # Optional: External Services
   REDIS_URL=your-redis-url
   CLOUDFLARE_API_TOKEN=your-cloudflare-token
   ```

4. **Deploy**
   ```bash
   # Automatic deployment on push
   git push origin main
   ```

### Option 2: Cloudflare Pages

**Advantages:**
- Excellent performance
- Built-in security features
- Integration with Cloudflare services
- Cost-effective

**Steps:**
1. **Build Configuration**
   ```bash
   # Build command
   npm run build
   
   # Output directory
   .next
   ```

2. **Cloudflare Setup**
   - Connect GitHub repository
   - Configure build settings
   - Set environment variables

3. **Wrangler Configuration** (if using Cloudflare Workers)
   ```toml
   # wrangler.toml
   name = "3d-marketplace"
   compatibility_date = "2024-01-01"
   
   [env.production]
   vars = { NODE_ENV = "production" }
   ```

### Option 3: Self-Hosted (Docker)

**Advantages:**
- Full control over infrastructure
- Custom configuration options
- Cost control for large scale

**Steps:**
1. **Create Dockerfile**
   ```dockerfile
   FROM node:18-alpine
   
   WORKDIR /app
   COPY package*.json ./
   RUN npm ci --only=production
   
   COPY . .
   RUN npm run build
   
   EXPOSE 3000
   CMD ["npm", "start"]
   ```

2. **Docker Compose**
   ```yaml
   version: '3.8'
   services:
     app:
       build: .
       ports:
         - "3000:3000"
       environment:
         - NODE_ENV=production
         - DATABASE_URL=${DATABASE_URL}
       depends_on:
         - redis
         - postgres
   
     redis:
       image: redis:alpine
       ports:
         - "6379:6379"
   
     postgres:
       image: postgres:14
       environment:
         POSTGRES_DB: marketplace
         POSTGRES_USER: ${DB_USER}
         POSTGRES_PASSWORD: ${DB_PASSWORD}
   ```

3. **Deploy**
   ```bash
   docker-compose up -d
   ```

## 🔧 Environment Configuration

### Required Environment Variables

```env
# Application
NODE_ENV=production
NEXTAUTH_SECRET=your-super-secret-key-min-32-chars
NEXTAUTH_URL=https://your-production-domain.com

# Database
DATABASE_URL=postgresql://user:password@host:port/database

# Scraping System
RATE_LIMIT_PRINTABLES=10
RATE_LIMIT_MAKERWORLD=15
RATE_LIMIT_THANGS=12
SCRAPING_TIMEOUT=30000
SCRAPING_RETRY_ATTEMPTS=3
SCRAPING_RETRY_DELAY=1000
SCRAPING_USER_AGENT=3D-Marketplace-Bot/1.0

# External Platform URLs
PRINTABLES_BASE_URL=https://www.printables.com
MAKERWORLD_BASE_URL=https://makerworld.com
THANGS_BASE_URL=https://thangs.com
```

### Optional Environment Variables

```env
# Caching (Redis)
REDIS_URL=redis://localhost:6379

# Monitoring
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=info

# Performance
MAX_CONCURRENT_REQUESTS=3
BATCH_SIZE_LIMIT=50

# Security
CORS_ORIGIN=https://your-domain.com
API_RATE_LIMIT=100
```

## 📊 Post-Deployment Verification

### 1. Health Checks

```bash
# Check application health
curl https://your-domain.com/api/scraping/health

# Expected response
{
  "success": true,
  "data": {
    "status": "operational",
    "platforms": {
      "printables": { "status": "operational", "responseTime": 250 },
      "makerworld": { "status": "operational", "responseTime": 180 },
      "thangs": { "status": "operational", "responseTime": 200 }
    }
  }
}
```

### 2. Functionality Tests

```bash
# Test single import
curl -X POST https://your-domain.com/api/scraping/import \
  -H "Content-Type: application/json" \
  -d '{"url": "https://www.printables.com/model/123456", "dryRun": true}'

# Test batch import
curl -X POST https://your-domain.com/api/scraping/batch \
  -H "Content-Type: application/json" \
  -d '{"urls": ["url1", "url2"], "options": {"dryRun": true}}'
```

### 3. Performance Verification

```bash
# Load testing with Apache Bench
ab -n 100 -c 10 https://your-domain.com/api/scraping/health

# Expected metrics
# - Response time < 2 seconds
# - Success rate > 99%
# - No memory leaks
```

## 🔍 Monitoring Setup

### 1. Application Monitoring

**Recommended Tools:**
- **Vercel Analytics** (if using Vercel)
- **Cloudflare Analytics** (if using Cloudflare)
- **Sentry** for error tracking
- **DataDog** for comprehensive monitoring

**Key Metrics to Monitor:**
- Response times
- Error rates
- Memory usage
- CPU utilization
- Database connections

### 2. Custom Health Monitoring

```javascript
// Add to your monitoring system
const healthCheck = async () => {
  try {
    const response = await fetch('/api/scraping/health');
    const data = await response.json();
    
    // Alert if any platform is down
    Object.entries(data.data.platforms).forEach(([platform, status]) => {
      if (status.status !== 'operational') {
        alert(`Platform ${platform} is ${status.status}`);
      }
    });
  } catch (error) {
    alert('Health check failed', error);
  }
};

// Run every 5 minutes
setInterval(healthCheck, 5 * 60 * 1000);
```

### 3. Log Aggregation

```javascript
// Structured logging
const logger = {
  info: (message, meta) => console.log(JSON.stringify({ level: 'info', message, meta, timestamp: new Date().toISOString() })),
  error: (message, error) => console.error(JSON.stringify({ level: 'error', message, error: error.message, stack: error.stack, timestamp: new Date().toISOString() })),
  warn: (message, meta) => console.warn(JSON.stringify({ level: 'warn', message, meta, timestamp: new Date().toISOString() }))
};
```

## 🔒 Security Considerations

### 1. API Security

```javascript
// Rate limiting middleware
import rateLimit from 'express-rate-limit';

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP'
});
```

### 2. Input Validation

```javascript
// URL validation
const validateUrl = (url) => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

// Sanitize input
const sanitizeInput = (input) => {
  return input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
};
```

### 3. CORS Configuration

```javascript
// next.config.js
module.exports = {
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Origin', value: process.env.CORS_ORIGIN || '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET, POST, OPTIONS' },
          { key: 'Access-Control-Allow-Headers', value: 'Content-Type, Authorization' },
        ],
      },
    ];
  },
};
```

## 🚨 Troubleshooting

### Common Issues

1. **Build Failures**
   ```bash
   # Clear cache and rebuild
   rm -rf .next node_modules
   npm install
   npm run build
   ```

2. **Environment Variable Issues**
   ```bash
   # Verify environment variables
   printenv | grep -E "(NEXTAUTH|DATABASE|SCRAPING)"
   ```

3. **Database Connection Issues**
   ```bash
   # Test database connection
   npx prisma db push
   npx prisma generate
   ```

4. **Rate Limiting Issues**
   ```bash
   # Check rate limit status
   curl https://your-domain.com/api/scraping/health
   ```

### Performance Issues

1. **Slow Response Times**
   - Check database query performance
   - Verify external API response times
   - Monitor memory usage

2. **High Error Rates**
   - Check application logs
   - Verify external platform availability
   - Review rate limiting configuration

## 📈 Scaling Considerations

### Horizontal Scaling
- Use load balancers for multiple instances
- Implement session affinity if needed
- Consider microservices architecture

### Vertical Scaling
- Monitor resource usage
- Optimize database queries
- Implement caching strategies

### Database Scaling
- Use read replicas for read-heavy workloads
- Implement connection pooling
- Consider database sharding for large datasets

---

## ✅ Deployment Checklist

### Pre-Deployment
- [ ] All tests passing
- [ ] Environment variables configured
- [ ] Database migrations completed
- [ ] Security review completed
- [ ] Performance testing completed

### Deployment
- [ ] Application deployed successfully
- [ ] Health checks passing
- [ ] Monitoring configured
- [ ] Logging setup completed
- [ ] Backup procedures in place

### Post-Deployment
- [ ] Functionality verified
- [ ] Performance metrics within targets
- [ ] Error rates acceptable
- [ ] Team notified of deployment
- [ ] Documentation updated

---

**Deployment Status**: ✅ **READY FOR PRODUCTION**  
**Recommended Platform**: 🚀 **Vercel** (for ease of use) or **Cloudflare Pages** (for performance)
