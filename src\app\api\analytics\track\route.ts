/**
 * API endpoint для трекінгу подій в Cloudflare Analytics
 * Інтеграція з cloudflare-observability MCP
 */

import { NextRequest, NextResponse } from 'next/server';
import { withFullApiMiddleware, CloudflareObservabilityContext } from '@/lib/cloudflare/middleware';

interface TrackEventRequest {
  event: string;
  userId?: string;
  data?: Record<string, any>;
}

async function handleTrackEvent(request: NextRequest, context: CloudflareObservabilityContext) {
  try {
    const body: TrackEventRequest = await request.json();
    const { event, userId, data } = body;

    if (!event) {
      return NextResponse.json(
        { success: false, error: 'Event name is required' },
        { status: 400 }
      );
    }

    // Трекінг події через Cloudflare Analytics
    await context.trackEvent(event, {
      userId,
      ...data,
      ip: request.headers.get('cf-connecting-ip') || request.headers.get('x-forwarded-for'),
      country: request.headers.get('cf-ipcountry'),
      userAgent: request.headers.get('user-agent'),
      timestamp: new Date().toISOString()
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to track event:', error);
    await context.trackError(error as Error, 'analytics_track_api');
    
    return NextResponse.json(
      { success: false, error: 'Failed to track event' },
      { status: 500 }
    );
  }
}

export const POST = withFullApiMiddleware(handleTrackEvent);
