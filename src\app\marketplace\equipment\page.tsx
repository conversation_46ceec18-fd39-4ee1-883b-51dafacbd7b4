'use client';

import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import Link from 'next/link';
import { useEffect, useState } from 'react';

import { EQUIPMENT_DATA } from '@/app/constants/equipment-data';

// Використовуємо дані з константного файлу
const MOCK_EQUIPMENT = EQUIPMENT_DATA;

// Категорії обладнання
const EQUIPMENT_CATEGORIES = [
  'All',
  'FDM Printers',
  'Resin Printers',
  'Accessories',
  'Replacement Parts',
  'Upgrades',
  'Tools',
];

// Популярні теги
const POPULAR_TAGS = [
  'fdm', 'resin', 'beginner', 'professional', 'high-detail',
  'budget', 'reliable', 'fast', 'multi-material', 'accessories'
];

export default function EquipmentPage() {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [sortBy, setSortBy] = useState('popular');
  const [priceFilter, setPriceFilter] = useState('all');
  const [priceRange, setPriceRange] = useState([0, 1500]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredEquipment, setFilteredEquipment] = useState(MOCK_EQUIPMENT);

  // Фільтрація обладнання при зміні фільтрів
  useEffect(() => {
    const filtered = MOCK_EQUIPMENT.filter(equipment => {
      // Фільтр за категорією
      const categoryMatch = selectedCategory === 'All' || equipment.category === selectedCategory;

      // Фільтр за ціною
      const priceMatch = equipment.price >= priceRange[0] && equipment.price <= priceRange[1];

      // Фільтр за пошуком
      const searchMatch =
        equipment.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        equipment.manufacturer.toLowerCase().includes(searchQuery.toLowerCase()) ||
        equipment.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

      return categoryMatch && priceMatch && searchMatch;
    });

    // Сортування
    const sorted = [...filtered].sort((a, b) => {
      if (sortBy === 'popular') return b.likes - a.likes;
      if (sortBy === 'price-low') return a.price - b.price;
      if (sortBy === 'price-high') return b.price - a.price;
      if (sortBy === 'newest') return 0; // В реальному додатку тут буде сортування за датою
      return 0;
    });

    setFilteredEquipment(sorted);
  }, [selectedCategory, sortBy, priceRange, searchQuery]);

  return (
    <main className="flex min-h-screen flex-col bg-background">
      {/* Заголовок сторінки */}
      <section className="bg-primary/5 py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl font-bold text-center mb-4">Обладнання для 3D-друку</h1>
          <p className="text-xl text-center text-muted-foreground mb-8">
            Знайдіть ідеальний 3D-принтер та аксесуари для ваших проектів
          </p>
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <Input
                type="text"
                placeholder="Пошук принтерів, виробників або аксесуарів..."
                className="w-full pl-10 py-6 text-lg"
                value={searchQuery}
                onChange={(e) => {
                  // @ts-ignore - React 19 type issues
                  setSearchQuery(e.currentTarget.value);
                }}
              />
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
          </div>
        </div>
      </section>

      {/* Основний контент */}
      <section className="container mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Сайдбар з фільтрами */}
          <aside className="w-full lg:w-1/4 space-y-6">
            <div className="bg-card rounded-lg p-6 shadow-sm">
              <h2 className="text-xl font-semibold mb-4">Категорії</h2>
              <div className="space-y-2">
                {EQUIPMENT_CATEGORIES.map((category) => (
                  <button
                    key={category}
                    className={`block w-full text-left px-3 py-2 rounded-md transition-colors ${
                      selectedCategory === category
                        ? 'bg-primary text-primary-foreground'
                        : 'hover:bg-muted'
                    }`}
                    onClick={() => setSelectedCategory(category)}
                  >
                    {category}
                  </button>
                ))}
              </div>
            </div>

            <div className="bg-card rounded-lg p-6 shadow-sm">
              <h2 className="text-xl font-semibold mb-4">Ціна</h2>
              <div className="space-y-4">
                <Slider
                  defaultValue={[0, 1500]}
                  max={1500}
                  step={10}
                  value={priceRange}
                  onValueChange={setPriceRange}
                />
                <div className="flex justify-between">
                  <span>${priceRange[0]}</span>
                  <span>${priceRange[1]}</span>
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg p-6 shadow-sm">
              <h2 className="text-xl font-semibold mb-4">Популярні теги</h2>
              <div className="flex flex-wrap gap-2">
                {POPULAR_TAGS.map((tag) => (
                  <Badge
                    key={tag}
                    variant="outline"
                    className="cursor-pointer hover:bg-primary hover:text-primary-foreground"
                    onClick={() => setSearchQuery(tag)}
                  >
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          </aside>

          {/* Основний контент */}
          <div className="w-full lg:w-3/4">
            <div className="flex justify-between items-center mb-6">
              <p className="text-muted-foreground">
                Знайдено {filteredEquipment.length} товарів
              </p>
              <div className="flex items-center gap-2">
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Сортувати за" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="popular">Популярні</SelectItem>
                    <SelectItem value="newest">Нові</SelectItem>
                    <SelectItem value="price-low">Ціна: від низької до високої</SelectItem>
                    <SelectItem value="price-high">Ціна: від високої до низької</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Сітка обладнання */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredEquipment.map((equipment) => (
                <Link href={`/marketplace/equipment/${equipment.id}`} key={equipment.id}>
                  <Card className="overflow-hidden h-full hover:shadow-md transition-shadow">
                    <div className="aspect-square relative">
                      <img
                        src={equipment.thumbnail}
                        alt={equipment.title}
                        className="w-full h-full object-cover"
                      />
                      <Badge className="absolute top-2 left-2 bg-blue-500">${equipment.price}</Badge>
                      {equipment.isFeatured && (
                        <Badge className="absolute top-2 right-2 bg-amber-500">Популярне</Badge>
                      )}
                    </div>
                    <div className="p-4">
                      <h3 className="font-semibold text-lg mb-1">{equipment.title}</h3>
                      <p className="text-muted-foreground text-sm mb-2">
                        Виробник: {equipment.manufacturer}
                      </p>
                      <p className="text-sm line-clamp-2 mb-3">{equipment.description}</p>
                      <div className="flex flex-wrap gap-1">
                        {equipment.tags.slice(0, 3).map((tag) => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </Card>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
