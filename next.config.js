/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  typescript: {
    // Temporarily ignore TypeScript errors during build
    ignoreBuildErrors: true,
  },
  eslint: {
    // Temporarily ignore ESLint errors during build
    ignoreDuringBuilds: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
      },
      {
        protocol: 'https',
        hostname: 'pub-marketplace-storage.r2.dev',
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
      },
      {
        protocol: 'https',
        hostname: 'cdn.makerworld.com',
      },
      {
        protocol: 'https',
        hostname: 'randomuser.me',
      },
      {
        protocol: 'https',
        hostname: 'cdn.thangs.com',
      },
      {
        protocol: 'https',
        hostname: 'media.printables.com',
      },
      {
        protocol: 'https',
        hostname: 'cdn.printables.com',
      },
      {
        protocol: 'https',
        hostname: 'files.printables.com',
      }
    ],
    unoptimized: process.env.NODE_ENV === 'production', // Required for Cloudflare Pages
  },
  // Cloudflare Pages specific configuration
  experimental: {
    optimizeCss: false, // Disabled due to critters dependency issue
    optimizeServerReact: true,
    serverMinification: true,
  },
  // Bundle optimization for Cloudflare
  output: 'standalone',
  // Disable webpack5 buffer polyfill for Cloudflare compatibility
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
      };
    }

    // Optimize bundle size for Cloudflare Workers
    if (isServer) {
      config.optimization = {
        ...config.optimization,
        minimize: true,
        sideEffects: false,
      };

      // Exclude large dependencies from server bundle
      config.externals = [
        ...config.externals || [],
        // Add externals to reduce bundle size
        'canvas',
        'sharp',
        '@next/swc-win32-x64-msvc',
        '@next/swc-linux-x64-gnu',
        '@next/swc-darwin-x64',
        '@next/swc-darwin-arm64',
      ];
    }

    return config;
  },
};

module.exports = nextConfig;
