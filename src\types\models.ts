// Data types for models

export interface PrintSettings {
  material: string;
  layerHeight: string;
  infill: string;
  supports: string;
  rafts: string;
  printTime: string;
}

export interface Designer {
  id?: string;
  name: string;
  avatar?: string;
  models?: number;
  followers?: number;
}

// Нові типи для зовнішніх джерел
export type ModelSource = 'local' | 'printables' | 'thingiverse' | 'myminifactory' | 'thangs' | 'makerworld';

export interface ExternalSource {
  platform: ModelSource;
  originalId: string;
  originalUrl: string;
  importedAt: string;
  lastSyncAt?: string;
}

export interface License {
  type: 'CC0' | 'CC-BY' | 'CC-BY-SA' | 'CC-BY-NC' | 'CC-BY-NC-SA' | 'GPL' | 'MIT' | 'Custom' | 'Commercial';
  name: string;
  url?: string;
  description?: string;
  allowCommercialUse: boolean;
  requireAttribution: boolean;
  allowDerivatives: boolean;
}

export interface ModelImage {
  id: string;
  url: string;
  alt?: string;
}

export interface ModelFile {
  id: string;
  format: string;
  url: string;
  size: string;
}

export interface Model {
  id: string;
  title: string;
  description?: string;
  thumbnail: string;
  designer: Designer;
  price: number;
  category: string;
  likes: number;
  downloads: number;
  tags: string[];
  isFeatured?: boolean;
  images: string[];
  designerAvatar?: string;
  designerModels?: number;
  designerFollowers?: number;
  fileFormats: string[];
  fileSize: string;
  printSettings: PrintSettings;
  createdAt?: string;
  updatedAt?: string;
  downloadUrl?: string; // Added downloadUrl
  reviewCount?: number; // Added reviewCount
  // Нові поля для зовнішніх джерел
  source: ModelSource;
  externalSource?: ExternalSource;
  license?: License;
  isFree?: boolean;
  originalPrice?: number; // Оригінальна ціна з зовнішнього джерела
}

export interface ModelsResponse {
  models: Model[];
  pagination?: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

export interface ModelResponse {
  model: Model;
}

export interface ModelsQueryParams {
  page?: number;
  limit?: number;
  category?: string;
  search?: string;
  tags?: string[];
  minPrice?: number;
  maxPrice?: number;
  sortBy?: 'newest' | 'popular' | 'downloads' | 'price_asc' | 'price_desc' | 'oldest' | 'name';
  source?: ModelSource;
  license?: string;
  isFree?: boolean;
  dateRange?: 'all' | 'today' | 'week' | 'month' | 'year';
  minDownloads?: number;
  fileFormats?: string[];
}

// Scraping-specific types
export interface ScrapedImage {
  id: string;
  url: string;
  alt?: string;
  width?: number;
  height?: number;
}

export interface ScrapedFile {
  id: string;
  name: string;
  url: string;
  downloadUrl: string;
  size: number;
  format: string;
}

export interface ScrapedDesigner {
  id: string;
  name: string;
  username?: string;
  avatar?: string;
  profileUrl?: string;
  verified?: boolean;
}

export interface ScrapedLicense {
  type: License['type'];
  name: string;
  url?: string;
  description?: string;
  allowCommercialUse: boolean;
  requireAttribution: boolean;
  allowDerivatives: boolean;
  detected: boolean;
  confidence: number; // 0-1 confidence score
}

export interface CommunityStats {
  views: number;
  downloads: number;
  likes: number;
  comments: number;
  favorites?: number;
  shares?: number;
}

export interface MaterialInfo {
  recommended: string[];
  tested: string[];
  settings: Record<string, any>;
}

export interface ScrapedModel {
  // Basic Information
  title: string;
  description: string;
  summary?: string;

  // Media
  images: ScrapedImage[];
  thumbnail: string;

  // Files
  files: ScrapedFile[];
  fileFormats: string[];
  totalSize: number;

  // Designer
  designer: ScrapedDesigner;

  // Metadata
  tags: string[];
  category: string;
  license: ScrapedLicense;

  // Statistics
  stats: CommunityStats;

  // Platform-specific
  platform: ModelSource;
  originalId: string;
  originalUrl: string;
  scrapedAt: string;

  // Print Settings
  printSettings?: PrintSettings;
  materialInfo?: MaterialInfo;

  // Pricing
  isFree: boolean;
  price?: number;
  currency?: string;
}

export interface ScrapingJob {
  id: string;
  url: string;
  platform: ModelSource;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  priority: number;
  attempts: number;
  maxAttempts: number;
  result?: ScrapedModel;
  error?: string;
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
}

export interface BatchImportJob {
  id: string;
  urls: string[];
  status: 'pending' | 'processing' | 'completed' | 'failed';
  total: number;
  completed: number;
  failed: number;
  results: Array<{
    url: string;
    status: 'success' | 'failed';
    modelId?: string;
    error?: string;
  }>;
  createdAt: string;
  completedAt?: string;
}

export interface PlatformHealth {
  platform: ModelSource;
  status: 'operational' | 'degraded' | 'down';
  lastCheck: string;
  responseTime: number;
  rateLimitRemaining: number;
  errorRate: number;
}

export interface ScrapingError {
  code: string;
  message: string;
  platform?: ModelSource;
  url?: string;
  details?: Record<string, any>;
  retryAfter?: number;
}
