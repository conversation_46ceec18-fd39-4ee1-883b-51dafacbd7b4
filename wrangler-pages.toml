name = "3d-marketplace"
compatibility_date = "2024-11-01"
compatibility_flags = ["nodejs_compat"]
pages_build_output_dir = "dist"

[vars]
NODE_ENV = "production"
NEXTAUTH_URL = "https://3d-marketplace.pages.dev"
NEXTAUTH_SECRET = "your-nextauth-secret-here"

# Scraping System Configuration
RATE_LIMIT_PRINTABLES = "10"
RATE_LIMIT_MAKERWORLD = "15"
RATE_LIMIT_THANGS = "12"
SCRAPING_TIMEOUT = "30000"
SCRAPING_RETRY_ATTEMPTS = "3"
SCRAPING_RETRY_DELAY = "1000"
SCRAPING_USER_AGENT = "3D-Marketplace-Bot/1.0"

# Platform URLs
PRINTABLES_BASE_URL = "https://www.printables.com"
MAKERWORLD_BASE_URL = "https://makerworld.com"
THANGS_BASE_URL = "https://thangs.com"

# D1 Database binding
[[d1_databases]]
binding = "DB"
database_name = "marketplace_db"
database_id = "8aad55c8-39fa-4432-b730-323680364383"

# R2 Storage binding
[[r2_buckets]]
binding = "STORAGE"
bucket_name = "3d-marketplace-r2"

# KV Storage binding
[[kv_namespaces]]
binding = "KV"
id = "9711cfa19bd04ce4afbd8b28bd051f7b"

# Preview environment
[env.preview]
name = "3d-marketplace-preview"

[env.preview.vars]
NODE_ENV = "development"
NEXTAUTH_URL = "https://preview.3d-marketplace.pages.dev"
NEXTAUTH_SECRET = "your-nextauth-secret-here"

# Scraping System Configuration (Preview)
RATE_LIMIT_PRINTABLES = "5"
RATE_LIMIT_MAKERWORLD = "8"
RATE_LIMIT_THANGS = "6"
SCRAPING_TIMEOUT = "30000"
SCRAPING_RETRY_ATTEMPTS = "2"
SCRAPING_RETRY_DELAY = "1000"
SCRAPING_USER_AGENT = "3D-Marketplace-Bot/1.0-preview"

# Platform URLs (Preview)
PRINTABLES_BASE_URL = "https://www.printables.com"
MAKERWORLD_BASE_URL = "https://makerworld.com"
THANGS_BASE_URL = "https://thangs.com"

# D1 Database binding for preview
[[env.preview.d1_databases]]
binding = "DB"
database_name = "marketplace_db"
database_id = "8aad55c8-39fa-4432-b730-323680364383"

# R2 Storage binding for preview
[[env.preview.r2_buckets]]
binding = "STORAGE"
bucket_name = "3d-marketplace-r2"

# KV Storage binding for preview
[[env.preview.kv_namespaces]]
binding = "KV"
id = "9711cfa19bd04ce4afbd8b28bd051f7b"
