/**
 * Типи для роботи з Cloudflare D1
 */

/**
 * Результат запиту до D1
 */
export type D1Result<T = unknown> = {
  results: T[];
  success: boolean;
  meta: {
    duration: number;
    rows_read: number;
    rows_written: number;
  };
};

/**
 * Результат виконання запиту до D1 без повернення даних
 */
export type D1ExecResult = {
  success: boolean;
  meta: {
    duration: number;
    rows_written: number;
  };
};

/**
 * Інтерфейс для D1Database
 */
export interface D1Database {
  prepare: (query: string) => D1PreparedStatement;
  dump: () => Promise<ArrayBuffer>;
  batch: (statements: D1PreparedStatement[]) => Promise<D1Result[]>;
  exec: (query: string) => Promise<D1ExecResult>;
}

/**
 * Інтерфейс для D1PreparedStatement
 */
export interface D1PreparedStatement {
  bind: (...values: any[]) => D1PreparedStatement;
  first: <T = unknown>(colName?: string) => Promise<T | null>;
  run: () => Promise<D1ExecResult>;
  all: <T = unknown>() => Promise<D1Result<T>>;
  raw: <T = unknown>() => Promise<T[]>;
}

/**
 * Параметри для запиту до бази даних
 */
export type QueryParams = any[];

/**
 * Опції для транзакції
 */
export interface TransactionOptions {
  timeout?: number;
  rollbackOnError?: boolean;
}

/**
 * Функція для виконання транзакції
 */
export type TransactionCallback<T> = (tx: D1Database) => Promise<T>;

/**
 * Опції для пагінації
 */
export interface PaginationOptions {
  page?: number;
  limit?: number;
}

/**
 * Результат з пагінацією
 */
export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}
