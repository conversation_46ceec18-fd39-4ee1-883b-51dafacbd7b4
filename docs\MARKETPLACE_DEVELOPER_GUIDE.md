# Посібник розробника маркетплейсу 3D-моделей

## Зміст

1. [Вступ](#вступ)
2. [Архітектура проекту](#архітектура-проекту)
3. [Налаштування середовища розробки](#налаштування-середовища-розробки)
4. [Структура проекту](#структура-проекту)
5. [Компоненти маркетплейсу](#компоненти-маркетплейсу)
6. [API маркетплейсу](#api-маркетплейсу)
7. [Інтеграція з платіжними системами](#інтеграція-з-платіжними-системами)
8. [3D-візуалізація](#3d-візуалізація)
9. [Тестування](#тестування)
10. [Розгортання](#розгортання)
11. [Внесок у проект](#внесок-у-проект)

## Вступ

Цей посібник призначений для розробників, які хочуть зрозуміти архітектуру маркетплейсу 3D-моделей, внести свій внесок у проект або інтегруватися з ним. Маркетплейс побудований на основі сучасного стеку технологій, включаючи Next.js, React, TypeScript, Tailwind CSS, Three.js та інші.

## Архітектура проекту

Маркетплейс 3D-моделей побудований на основі наступних технологій:

- **Frontend**: Next.js 14 з App Router, React 18, TypeScript
- **Стилізація**: Tailwind CSS, shadcn/ui
- **3D-візуалізація**: Three.js, React Three Fiber
- **Backend**: Next.js API Routes, Server Components
- **База даних**: Supabase (PostgreSQL)
- **Аутентифікація**: NextAuth.js
- **Платежі**: Stripe
- **Управління станом**: Zustand
- **Розгортання**: Cloudflare Pages

### Діаграма архітектури

```
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
|  Клієнт (браузер) <---->  Next.js Server  <---->  Supabase (БД)   |
|                  |     |                  |     |                  |
+------------------+     +------------------+     +------------------+
                                 ^
                                 |
                                 v
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
|  Stripe (платежі) |     |  Cloudflare R2   |     |  NextAuth.js    |
|                  |     |  (зберігання)    |     |  (аутентифікація)|
+------------------+     +------------------+     +------------------+
```

## Налаштування середовища розробки

### Вимоги

- Node.js 18.0.0 або новіше
- npm або yarn
- Git

### Клонування репозиторію

```bash
git clone https://github.com/yourusername/3d-marketplace.git
cd 3d-marketplace
```

### Встановлення залежностей

```bash
npm install
# або
yarn install
```

### Налаштування змінних середовища

Створіть файл `.env.local` на основі `.env.example`:

```bash
cp .env.example .env.local
```

Відредагуйте файл `.env.local` та додайте необхідні змінні середовища:

```
# База даних
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# Аутентифікація
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret

# Stripe
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret

# Cloudflare R2
CLOUDFLARE_ACCOUNT_ID=your-cloudflare-account-id
CLOUDFLARE_ACCESS_KEY_ID=your-cloudflare-access-key-id
CLOUDFLARE_SECRET_ACCESS_KEY=your-cloudflare-secret-access-key
```

### Запуск проекту в режимі розробки

```bash
npm run dev
# або
yarn dev
```

Проект буде доступний за адресою [http://localhost:3000](http://localhost:3000).

## Структура проекту

```
3d-marketplace/
├── docs/                 # Документація
├── public/               # Статичні файли
├── src/
│   ├── app/              # Next.js App Router сторінки
│   │   ├── api/          # API маршрути
│   │   ├── auth/         # Сторінки аутентифікації
│   │   ├── marketplace/  # Сторінки маркетплейсу
│   │   └── models/       # Сторінки моделей
│   ├── components/       # React компоненти
│   │   ├── ui/           # UI компоненти
│   │   └── 3d-viewer/    # 3D-переглядач
│   ├── lib/              # Утиліти та API
│   │   ├── api/          # API клієнти
│   │   ├── auth/         # Аутентифікація
│   │   ├── db/           # Доступ до бази даних
│   │   └── utils/        # Утиліти
│   ├── store/            # Zustand сховища
│   └── types/            # TypeScript типи
├── .env.example          # Приклад змінних середовища
├── next.config.js        # Конфігурація Next.js
├── package.json          # Залежності проекту
└── tailwind.config.js    # Конфігурація Tailwind CSS
```

## Компоненти маркетплейсу

### Сторінки

- **Головна сторінка** (`src/app/page.tsx`): Головна сторінка з представленням маркетплейсу
- **Маркетплейс** (`src/app/marketplace/page.tsx`): Сторінка з списком моделей та фільтрами
- **Деталі моделі** (`src/app/marketplace/[id]/page.tsx`): Сторінка з детальною інформацією про модель
- **Завантаження моделі** (`src/app/models/upload/page.tsx`): Сторінка для завантаження нових моделей
- **Профіль користувача** (`src/app/profile/page.tsx`): Сторінка профілю користувача
- **Підписки** (`src/app/memberships/page.tsx`): Сторінка з доступними підписками

### Компоненти

- **ModelCard** (`src/components/marketplace/ModelCard.tsx`): Компонент для відображення моделі в списку
- **ModelViewer** (`src/components/3d-viewer/ModelViewer.tsx`): 3D-переглядач моделей
- **FilterSidebar** (`src/components/marketplace/FilterSidebar.tsx`): Бічна панель з фільтрами
- **CheckoutForm** (`src/components/marketplace/CheckoutForm.tsx`): Форма оформлення замовлення
- **UploadForm** (`src/components/models/UploadForm.tsx`): Форма завантаження моделей

## API маркетплейсу

### API маршрути

- **Моделі**:
  - `GET /api/models`: Отримання списку моделей
  - `GET /api/models/:id`: Отримання деталей моделі
  - `POST /api/models`: Створення нової моделі
  - `PUT /api/models/:id`: Оновлення моделі
  - `DELETE /api/models/:id`: Видалення моделі

- **Користувачі**:
  - `GET /api/users/:id`: Отримання профілю користувача
  - `GET /api/users/:id/models`: Отримання моделей користувача
  - `PUT /api/users/:id`: Оновлення профілю

- **Замовлення**:
  - `POST /api/orders`: Створення нового замовлення
  - `GET /api/orders/:id`: Отримання деталей замовлення
  - `GET /api/users/:id/orders`: Отримання замовлень користувача

- **Підписки**:
  - `GET /api/memberships`: Отримання доступних планів підписок
  - `POST /api/memberships/subscribe`: Підписка на план

### Приклад API маршруту

```typescript
// src/app/api/models/route.ts
import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const category = searchParams.get('category');
  const priceFilter = searchParams.get('price');
  const sortBy = searchParams.get('sort') || 'popular';
  
  const supabase = createClient();
  
  let query = supabase
    .from('models')
    .select('*');
  
  // Фільтрація за категорією
  if (category && category !== 'All') {
    query = query.eq('category', category);
  }
  
  // Фільтрація за ціною
  if (priceFilter === 'free') {
    query = query.eq('price', 0);
  } else if (priceFilter === 'paid') {
    query = query.gt('price', 0);
  }
  
  // Сортування
  if (sortBy === 'popular') {
    query = query.order('likes', { ascending: false });
  } else if (sortBy === 'downloads') {
    query = query.order('downloads', { ascending: false });
  } else if (sortBy === 'newest') {
    query = query.order('created_at', { ascending: false });
  } else if (sortBy === 'price-low') {
    query = query.order('price', { ascending: true });
  } else if (sortBy === 'price-high') {
    query = query.order('price', { ascending: false });
  }
  
  const { data, error } = await query;
  
  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
  
  return NextResponse.json({ data });
}

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  const body = await request.json();
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('models')
    .insert({
      ...body,
      user_id: session.user.id,
    })
    .select();
  
  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
  
  return NextResponse.json({ data: data[0] }, { status: 201 });
}
```

## Інтеграція з платіжними системами

### Stripe

Маркетплейс використовує Stripe для обробки платежів. Інтеграція з Stripe реалізована за допомогою наступних компонентів:

- **API маршрути** для створення платіжних намірів та обробки webhook'ів
- **Компоненти** для відображення форми оплати та обробки платежів на клієнті

#### Приклад створення платіжного наміру

```typescript
// src/app/api/payments/create-intent/route.ts
import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { stripe } from '@/lib/stripe';

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  const { amount, currency = 'usd', description } = await request.json();
  
  try {
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency,
      description,
      automatic_payment_methods: { enabled: true },
    });
    
    return NextResponse.json({ clientSecret: paymentIntent.client_secret });
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
```

## 3D-візуалізація

Маркетплейс використовує Three.js та React Three Fiber для 3D-візуалізації моделей. Компонент `ModelViewer` дозволяє користувачам інтерактивно переглядати 3D-моделі перед покупкою.

### Приклад компонента ModelViewer

```tsx
// src/components/3d-viewer/ModelViewer.tsx
import { useEffect, useRef, useState } from 'react';
import { Canvas, useThree, useLoader } from '@react-three/fiber';
import { OrbitControls, PerspectiveCamera, Environment } from '@react-three/drei';
import { STLLoader } from 'three/examples/jsm/loaders/STLLoader';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';

interface ModelViewerProps {
  modelUrl: string;
  modelFormat: 'stl' | 'obj' | 'gltf';
}

const Model = ({ modelUrl, modelFormat }: ModelViewerProps) => {
  const { camera } = useThree();
  
  useEffect(() => {
    camera.position.set(0, 0, 5);
    camera.lookAt(0, 0, 0);
  }, [camera]);
  
  let model;
  
  if (modelFormat === 'stl') {
    model = useLoader(STLLoader, modelUrl);
  } else if (modelFormat === 'obj') {
    model = useLoader(OBJLoader, modelUrl);
  } else if (modelFormat === 'gltf') {
    model = useLoader(GLTFLoader, modelUrl);
  }
  
  return (
    <>
      {model && (
        <primitive
          object={model}
          scale={[0.1, 0.1, 0.1]}
          position={[0, 0, 0]}
        />
      )}
    </>
  );
};

export default function ModelViewer({ modelUrl, modelFormat }: ModelViewerProps) {
  return (
    <div className="w-full h-[400px] bg-gray-100 rounded-lg">
      <Canvas>
        <ambientLight intensity={0.5} />
        <spotLight position={[10, 10, 10]} angle={0.15} penumbra={1} />
        <PerspectiveCamera makeDefault />
        <Model modelUrl={modelUrl} modelFormat={modelFormat} />
        <OrbitControls />
        <Environment preset="city" />
      </Canvas>
    </div>
  );
}
```

## Тестування

Маркетплейс використовує Jest та React Testing Library для тестування компонентів та API.

### Запуск тестів

```bash
npm test
# або
yarn test
```

### Приклад тесту компонента

```tsx
// src/components/marketplace/ModelCard.test.tsx
import { render, screen } from '@testing-library/react';
import ModelCard from './ModelCard';

describe('ModelCard', () => {
  const mockModel = {
    id: '1',
    title: 'Test Model',
    thumbnail: 'https://example.com/thumbnail.jpg',
    designer: 'Test Designer',
    price: 4.99,
    likes: 10,
    downloads: 100,
  };
  
  it('renders model information correctly', () => {
    render(<ModelCard model={mockModel} />);
    
    expect(screen.getByText('Test Model')).toBeInTheDocument();
    expect(screen.getByText('by Test Designer')).toBeInTheDocument();
    expect(screen.getByText('$4.99')).toBeInTheDocument();
    expect(screen.getByText('10')).toBeInTheDocument();
    expect(screen.getByText('100')).toBeInTheDocument();
  });
  
  it('shows "Free" when price is 0', () => {
    render(<ModelCard model={{ ...mockModel, price: 0 }} />);
    
    expect(screen.getByText('Free')).toBeInTheDocument();
  });
});
```

## Розгортання

Маркетплейс налаштований для розгортання на Cloudflare Pages.

### Налаштування Cloudflare Pages

1. Створіть новий проект на Cloudflare Pages
2. Підключіть репозиторій GitHub
3. Налаштуйте змінні середовища
4. Налаштуйте команду збірки: `npm run build`
5. Налаштуйте директорію збірки: `.next`

### Розгортання вручну

```bash
npm run build
# або
yarn build

# Розгортання за допомогою Wrangler CLI
npx wrangler pages publish .next
```

## Внесок у проект

Ми вітаємо внесок у проект! Якщо ви хочете внести свій внесок, будь ласка, дотримуйтесь наступних кроків:

1. Створіть форк репозиторію
2. Створіть гілку для вашої функції (`git checkout -b feature/amazing-feature`)
3. Внесіть зміни
4. Зробіть коміт змін (`git commit -m 'Add some amazing feature'`)
5. Відправте зміни у ваш форк (`git push origin feature/amazing-feature`)
6. Створіть Pull Request

### Стиль коду

Ми використовуємо ESLint та Prettier для забезпечення якості коду. Перед відправкою Pull Request, будь ласка, переконайтеся, що ваш код відповідає нашим стандартам:

```bash
npm run lint
# або
yarn lint
```
