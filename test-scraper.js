// Тестовий скрипт для перевірки скрапера
const testScraper = async () => {
  try {
    console.log('🧪 Тестування скрапера...');
    
    // Тест 1: Генерація фейкових даних
    console.log('\n1️⃣ Тестування генерації фейкових даних...');
    const fakeDataResponse = await fetch('http://localhost:3000/api/scrape', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'fake-data',
        count: 10
      })
    });
    
    const fakeDataResult = await fakeDataResponse.json();
    console.log('Результат:', fakeDataResult);
    
    // Тест 2: Перевірка отримання моделей
    console.log('\n2️⃣ Тестування отримання моделей...');
    const modelsResponse = await fetch('http://localhost:3000/api/scraped-models?limit=5');
    const modelsResult = await modelsResponse.json();
    console.log('Кількість моделей:', modelsResult.total);
    console.log('Перші 3 моделі:', modelsResult.models?.slice(0, 3)?.map(m => m.name));
    
    console.log('\n✅ Тестування завершено!');
    
  } catch (error) {
    console.error('❌ Помилка тестування:', error);
  }
};

// Запуск тесту
if (typeof window === 'undefined') {
  // Node.js environment
  const fetch = require('node-fetch');
  testScraper();
} else {
  // Browser environment
  testScraper();
}
