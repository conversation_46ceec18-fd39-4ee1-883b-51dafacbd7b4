import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';

// POST handler for processing download through Durable Object
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json() as { sessionId: string; modelId: string };
    const { sessionId, modelId } = body;

    if (!sessionId || !modelId) {
      return NextResponse.json(
        { success: false, error: 'Session ID and Model ID are required' },
        { status: 400 }
      );
    }

    // Отримуємо Durable Object для управління завантаженнями
    const durableObjectId = (globalThis as any).DOWNLOAD_MANAGER?.idFromName(`download-${modelId}`);
    
    if (!durableObjectId) {
      return NextResponse.json(
        { success: false, error: 'Download service unavailable' },
        { status: 503 }
      );
    }

    const durableObject = (globalThis as any).DOWNLOAD_MANAGER?.get(durableObjectId);
    
    // Обробляємо завантаження через Durable Object
    const response = await durableObject.fetch(`https://download-manager/download?sessionId=${sessionId}`);
    const result = await response.json();

    return NextResponse.json(result, { status: response.status });

  } catch (error) {
    console.error('Error processing download:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to process download' },
      { status: 500 }
    );
  }
}
