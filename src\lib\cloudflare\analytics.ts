/**
 * Утиліти для роботи з Cloudflare Analytics Engine
 * Інтеграція з cloudflare-observability MCP
 */

import { AnalyticsEngineDataset, AnalyticsEngineDataPoint, CloudflareEnv } from './types';

// Глобальна змінна для зберігання екземпляра Analytics Engine
let analytics: AnalyticsEngineDataset | null = null;

/**
 * Ініціалізує з'єднання з Analytics Engine
 * @param env Cloudflare Environment з bindings
 */
export function initializeAnalytics(env: CloudflareEnv): void {
  if (env.ANALYTICS) {
    analytics = env.ANALYTICS;
    console.log('✅ Analytics Engine initialized successfully');
  } else {
    console.warn('⚠️ Analytics Engine binding not found in environment');
  }
}

/**
 * Отримує екземпляр Analytics Engine
 * @returns Екземпляр Analytics Engine
 */
export function getAnalytics(): AnalyticsEngineDataset {
  if (!analytics) {
    // В середовищі Cloudflare Pages, Analytics доступна через env.ANALYTICS
    if (process.env.ANALYTICS) {
      analytics = process.env.ANALYTICS as unknown as AnalyticsEngineDataset;
    } else if (globalThis.ANALYTICS) {
      analytics = globalThis.ANALYTICS as unknown as AnalyticsEngineDataset;
    } else {
      throw new Error('Analytics Engine недоступна. Переконайтеся, що ви налаштували Analytics Engine в wrangler.toml');
    }
  }
  return analytics;
}

/**
 * Записує точку даних в Analytics Engine
 * @param dataPoint Точка даних
 */
export async function writeDataPoint(dataPoint: AnalyticsEngineDataPoint): Promise<void> {
  try {
    const analytics = getAnalytics();
    await analytics.writeDataPoint(dataPoint);
  } catch (error) {
    console.error('Failed to write analytics data point:', error);
  }
}

/**
 * Записує подію користувача
 * @param event Назва події
 * @param userId ID користувача (опціонально)
 * @param metadata Додаткові метадані
 */
export async function trackUserEvent(
  event: string,
  userId?: string,
  metadata?: Record<string, any>
): Promise<void> {
  const dataPoint: AnalyticsEngineDataPoint = {
    blobs: [event, userId || 'anonymous'],
    doubles: [Date.now()],
    indexes: [event, userId || 'anonymous']
  };

  if (metadata) {
    dataPoint.blobs?.push(JSON.stringify(metadata));
  }

  await writeDataPoint(dataPoint);
}

/**
 * Записує подію завантаження моделі
 * @param modelId ID моделі
 * @param userId ID користувача
 * @param downloadType Тип завантаження (free/paid)
 */
export async function trackModelDownload(
  modelId: string,
  userId: string,
  downloadType: 'free' | 'paid'
): Promise<void> {
  await trackUserEvent('model_download', userId, {
    modelId,
    downloadType,
    timestamp: new Date().toISOString()
  });
}

/**
 * Записує подію перегляду моделі
 * @param modelId ID моделі
 * @param userId ID користувача (опціонально)
 */
export async function trackModelView(
  modelId: string,
  userId?: string
): Promise<void> {
  await trackUserEvent('model_view', userId, {
    modelId,
    timestamp: new Date().toISOString()
  });
}

/**
 * Записує подію пошуку
 * @param query Пошуковий запит
 * @param userId ID користувача (опціонально)
 * @param resultsCount Кількість результатів
 */
export async function trackSearch(
  query: string,
  userId?: string,
  resultsCount?: number
): Promise<void> {
  await trackUserEvent('search', userId, {
    query,
    resultsCount,
    timestamp: new Date().toISOString()
  });
}

/**
 * Записує подію помилки
 * @param error Помилка
 * @param context Контекст помилки
 * @param userId ID користувача (опціонально)
 */
export async function trackError(
  error: Error,
  context: string,
  userId?: string
): Promise<void> {
  await trackUserEvent('error', userId, {
    message: error.message,
    stack: error.stack,
    context,
    timestamp: new Date().toISOString()
  });
}

/**
 * Записує метрики продуктивності
 * @param metric Назва метрики
 * @param value Значення метрики
 * @param unit Одиниця вимірювання
 */
export async function trackPerformanceMetric(
  metric: string,
  value: number,
  unit: string = 'ms'
): Promise<void> {
  const dataPoint: AnalyticsEngineDataPoint = {
    blobs: [metric, unit],
    doubles: [value, Date.now()],
    indexes: [metric]
  };

  await writeDataPoint(dataPoint);
}

/**
 * Записує бізнес-метрики
 * @param metric Назва метрики
 * @param value Значення
 * @param metadata Додаткові дані
 */
export async function trackBusinessMetric(
  metric: string,
  value: number,
  metadata?: Record<string, any>
): Promise<void> {
  const dataPoint: AnalyticsEngineDataPoint = {
    blobs: [metric, JSON.stringify(metadata || {})],
    doubles: [value, Date.now()],
    indexes: [metric]
  };

  await writeDataPoint(dataPoint);
}
