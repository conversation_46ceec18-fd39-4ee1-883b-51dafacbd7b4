import type { Adapter } from "next-auth/adapters";
import { D1Database, generateId } from "../db";

/**
 * Адаптер для NextAuth.js, який використовує Cloudflare D1
 * @param db Екземпляр бази даних D1
 * @returns Адаптер для NextAuth.js
 */
export function D1Adapter(db: D1Database): Adapter {
  return {
    async createUser(user) {
      const id = generateId();
      const { name, email, image } = user;
      
      await db.prepare(
        `INSERT INTO users (id, name, email, avatar_url, created_at, updated_at)
         VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`
      )
        .bind(id, name, email, image)
        .run();
      
      const result = await db.prepare(
        `SELECT id, name, email, avatar_url as image, created_at, updated_at
         FROM users WHERE id = ?`
      )
        .bind(id)
        .first();
      
      return result as any;
    },
    
    async getUser(id) {
      const user = await db.prepare(
        `SELECT id, name, email, avatar_url as image, created_at, updated_at
         FROM users WHERE id = ?`
      )
        .bind(id)
        .first();
      
      return user as any;
    },
    
    async getUserByEmail(email) {
      const user = await db.prepare(
        `SELECT id, name, email, avatar_url as image, created_at, updated_at
         FROM users WHERE email = ?`
      )
        .bind(email)
        .first();
      
      return user as any;
    },
    
    async getUserByAccount({ providerAccountId, provider }) {
      const account = await db.prepare(
        `SELECT user_id FROM accounts
         WHERE provider_id = ? AND provider_account_id = ?`
      )
        .bind(provider, providerAccountId)
        .first<{ user_id: string }>();
      
      if (!account) return null;
      
      const user = await db.prepare(
        `SELECT id, name, email, avatar_url as image, created_at, updated_at
         FROM users WHERE id = ?`
      )
        .bind(account.user_id)
        .first();
      
      return user as any;
    },
    
    async updateUser(user) {
      const { id, name, email, image } = user;
      
      await db.prepare(
        `UPDATE users
         SET name = ?, email = ?, avatar_url = ?, updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`
      )
        .bind(name, email, image, id)
        .run();
      
      const result = await db.prepare(
        `SELECT id, name, email, avatar_url as image, created_at, updated_at
         FROM users WHERE id = ?`
      )
        .bind(id)
        .first();
      
      return result as any;
    },
    
    async deleteUser(userId) {
      // Видалення пов'язаних записів
      await db.prepare(`DELETE FROM accounts WHERE user_id = ?`).bind(userId).run();
      await db.prepare(`DELETE FROM sessions WHERE user_id = ?`).bind(userId).run();
      
      // Видалення користувача
      await db.prepare(`DELETE FROM users WHERE id = ?`).bind(userId).run();
    },
    
    async linkAccount(account) {
      const id = generateId();
      const {
        userId,
        provider,
        type,
        providerAccountId,
        access_token,
        expires_at,
        refresh_token,
        id_token,
        scope,
        session_state,
        token_type,
      } = account;
      
      await db.prepare(
        `INSERT INTO accounts (
          id, user_id, provider_id, provider_type, provider_account_id,
          access_token, expires_at, refresh_token, id_token,
          scope, session_state, token_type
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
      )
        .bind(
          id,
          userId,
          provider,
          type,
          providerAccountId,
          access_token,
          expires_at,
          refresh_token,
          id_token,
          scope,
          session_state,
          token_type
        )
        .run();
      
      return account;
    },
    
    async unlinkAccount({ providerAccountId, provider }) {
      await db.prepare(
        `DELETE FROM accounts
         WHERE provider_id = ? AND provider_account_id = ?`
      )
        .bind(provider, providerAccountId)
        .run();
    },
    
    async createSession({ sessionToken, userId, expires }) {
      const id = generateId();
      
      await db.prepare(
        `INSERT INTO sessions (id, user_id, session_token, expires_at)
         VALUES (?, ?, ?, ?)`
      )
        .bind(id, userId, sessionToken, expires.toISOString())
        .run();
      
      return {
        id,
        sessionToken,
        userId,
        expires,
      };
    },
    
    async getSessionAndUser(sessionToken) {
      const session = await db.prepare(
        `SELECT id, user_id as userId, session_token as sessionToken, expires_at as expires
         FROM sessions WHERE session_token = ? AND expires_at > CURRENT_TIMESTAMP`
      )
        .bind(sessionToken)
        .first<any>();
      
      if (!session) return null;
      
      // Перетворення рядка дати в об'єкт Date
      session.expires = new Date(session.expires);
      
      const user = await db.prepare(
        `SELECT id, name, email, avatar_url as image
         FROM users WHERE id = ?`
      )
        .bind(session.userId)
        .first<any>();
      
      if (!user) return null;
      
      return {
        session,
        user,
      };
    },
    
    async updateSession({ sessionToken, expires, userId }) {
      await db.prepare(
        `UPDATE sessions
         SET expires_at = ?, user_id = ?
         WHERE session_token = ?`
      )
        .bind(expires?.toISOString(), userId, sessionToken)
        .run();
      
      const session = await db.prepare(
        `SELECT id, user_id as userId, session_token as sessionToken, expires_at as expires
         FROM sessions WHERE session_token = ?`
      )
        .bind(sessionToken)
        .first<any>();
      
      if (!session) return null;
      
      // Перетворення рядка дати в об'єкт Date
      session.expires = new Date(session.expires);
      
      return session;
    },
    
    async deleteSession(sessionToken) {
      await db.prepare(`DELETE FROM sessions WHERE session_token = ?`)
        .bind(sessionToken)
        .run();
    },
    
    async createVerificationToken({ identifier, expires, token }) {
      await db.prepare(
        `INSERT INTO verification_tokens (identifier, token, expires_at)
         VALUES (?, ?, ?)`
      )
        .bind(identifier, token, expires.toISOString())
        .run();
      
      return {
        identifier,
        token,
        expires,
      };
    },
    
    async useVerificationToken({ identifier, token }) {
      const verificationToken = await db.prepare(
        `SELECT identifier, token, expires_at as expires
         FROM verification_tokens
         WHERE identifier = ? AND token = ? AND expires_at > CURRENT_TIMESTAMP`
      )
        .bind(identifier, token)
        .first<any>();
      
      if (!verificationToken) return null;
      
      // Видалення використаного токена
      await db.prepare(
        `DELETE FROM verification_tokens WHERE identifier = ? AND token = ?`
      )
        .bind(identifier, token)
        .run();
      
      // Перетворення рядка дати в об'єкт Date
      verificationToken.expires = new Date(verificationToken.expires);
      
      return verificationToken;
    },
  };
}
