'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Heart, Download, ChevronRight } from 'lucide-react';
import { FEATURED_MODELS } from '@/app/constants/images';

const FeaturedModels = () => {
  return (
    <section className="w-full py-12 px-4 bg-muted/30">
      <div className="container mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-3xl font-bold">Популярні моделі</h2>
          <Link
            href="/marketplace"
            className="text-primary hover:text-primary/80 font-medium flex items-center"
          >
            Переглянути маркетплейс
            <ChevronRight className="h-5 w-5 ml-1" />
          </Link>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {FEATURED_MODELS.map((model) => (
            <div key={model.id} className="card group">
              <Link href={`/models/${model.id}`}>
                <div className="card-image h-56 md:h-64">
                  <Image
                    src={model.image}
                    alt={model.title}
                    fill
                    sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                    className="group-hover:scale-105 transition-transform duration-300"
                    style={{ objectFit: 'cover' }}
                  />
                  <div className="absolute top-3 right-3 bg-background/80 backdrop-blur-sm rounded-full p-1.5 opacity-0 group-hover:opacity-100 transition-opacity">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-foreground" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z" />
                    </svg>
                  </div>
                </div>
              </Link>
              <div className="card-content">
                <div className="flex items-center mb-3">
                  <div className="w-8 h-8 bg-muted rounded-full mr-2 overflow-hidden">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-full w-full text-muted-foreground p-1.5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <Link href={`/profile/${model.creator}`} className="text-sm text-muted-foreground hover:text-primary font-medium">{model.creator}</Link>
                </div>
                <Link href={`/models/${model.id}`}>
                  <h3 className="font-semibold text-lg mb-2 hover:text-primary transition-colors line-clamp-1">{model.title}</h3>
                </Link>
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span className="flex items-center">
                    <Heart className="h-4 w-4 mr-1 text-red-500" />
                    {model.likes}
                  </span>
                  <span className="flex items-center">
                    <Download className="h-4 w-4 mr-1 text-primary" />
                    {model.downloads}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturedModels;
