{"mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/home/"]}, "Context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"], "alwaysAllow": ["browser_navigate", "browser_wait_for", "browser_snapshot", "browser_click", "browser_take_screenshot"]}, "postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql://localhost/mydb"]}, "everything": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-everything"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "puppeteer": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@smithery-ai/puppeteer", "--key", "f8927db5-af9b-4c34-b3b1-046c5001f5fa"], "alwaysAllow": ["puppeteer_navigate"]}, "github.com/modelcontextprotocol/servers/tree/main/src/github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "*********************************************************************************************"}}, "Bright Data": {"command": "npx", "args": ["@brightdata/mcp"], "env": {"API_TOKEN": "fd358d1920658af81dcd8b442154950efd141500fe564d360a1d25b6c818234c", "WEB_UNLOCKER_ZONE": "web_unlocker1", "BROWSER_AUTH": "wss://brd-customer-hl_e6b08106-zone-scraping_browser2:<EMAIL>:9222"}, "alwaysAllow": ["scrape_as_markdown"], "disabled": true}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSAJuPah0gzC7mCh62saJiUxINoWn90"}, "alwaysAllow": ["brave_web_search"]}, "@21st-dev/magic": {"command": "npx", "args": ["-y", "@21st-dev/magic@latest", "API_KEY=\"d76e77e62c1961a9c050c93fe6a1a36c5fc08a1c484d82128ac7853aaec88f57\""], "disabled": true, "alwaysAllow": []}, "cloudflare-observability": {"command": "npx", "args": ["mcp-remote", "https://observability.mcp.cloudflare.com/sse"], "disabled": true, "alwaysAllow": []}, "cloudflare-bindings": {"command": "npx", "args": ["mcp-remote", "https://bindings.mcp.cloudflare.com/sse"], "disabled": false, "alwaysAllow": ["accounts_list"]}, "cloudflare-documentation": {"command": "npx", "args": ["mcp-remote", "https://docs.mcp.cloudflare.com/sse"]}, "cloudflare-workers-builds": {"command": "npx", "args": ["mcp-remote", "https://builds.mcp.cloudflare.com/sse"]}, "cloudflare-radar": {"command": "npx", "args": ["mcp-remote", "https://radar.mcp.cloudflare.com/sse"]}, "cloudflare-container": {"command": "npx", "args": ["mcp-remote", "https://containers.mcp.cloudflare.com/sse"]}, "cloudflare-browser-rendering": {"command": "npx", "args": ["mcp-remote", "https://browser.mcp.cloudflare.com/sse"]}, "cloudflare-logpush": {"command": "npx", "args": ["mcp-remote", "https://logs.mcp.cloudflare.com/sse"]}, "cloudflare-ai-gateway": {"command": "npx", "args": ["mcp-remote", "https://ai-gateway.mcp.cloudflare.com/sse"]}, "cloudflare-autorag": {"command": "npx", "args": ["mcp-remote", "https://autorag.mcp.cloudflare.com/sse"]}, "cloudflare-audit-logs": {"command": "npx", "args": ["mcp-remote", "https://auditlogs.mcp.cloudflare.com/sse"]}, "cloudflare-dns-analytics": {"command": "npx", "args": ["mcp-remote", "https://dns-analytics.mcp.cloudflare.com/sse"]}, "cloudflare-dex-analysis": {"command": "npx", "args": ["mcp-remote", "https://dex.mcp.cloudflare.com/sse"]}, "cloudflare-one-casb": {"command": "npx", "args": ["mcp-remote", "https://casb.mcp.cloudflare.com/sse"]}, "cloudflare-graphql": {"command": "npx", "args": ["mcp-remote", "https://graphql.mcp.cloudflare.com/sse"]}, "Comfy MCP Server": {"command": "/path/to/uvx", "args": ["comfy-mcp-server"], "env": {"COMFY_URL": "http://your-comfy-server-url:port", "COMFY_WORKFLOW_JSON_FILE": "/path/to/the/comfyui_workflow_export.json", "PROMPT_NODE_ID": "6", "OUTPUT_NODE_ID": "9", "OUTPUT_MODE": "file"}, "disabled": true, "alwaysAllow": []}, "docker-mcp": {"command": "uvx", "args": ["docker-mcp"]}, "Figma MCP": {"command": "cmd", "args": ["/c", "npx", "-y", "figma-developer-mcp", "--figma-api-key=*********************************************", "--st<PERSON>"]}, "shadcn-ui-mcp-server": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@ymadd/shadcn-ui-mcp-server", "--key", "f8927db5-af9b-4c34-b3b1-046c5001f5fa"], "disabled": true, "alwaysAllow": []}, "web-eval-agent": {"command": "uvx", "args": ["--refresh-package", "webEvalAgent", "--from", "git+https://github.com/Operative-Sh/web-eval-agent.git", "webEvalAgent"], "env": {"OPERATIVE_API_KEY": "<YOUR_KEY>"}}}}