'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import { useCart } from '@/context/cart-context';
import { ArrowLeft, ShieldCheck, Star, Truck } from 'lucide-react';
import Link from 'next/link';
import React, { useState } from 'react';

import { FILAMENT_DATA, RECOMMENDED_FILAMENTS } from '@/app/constants/filament-data';

// Використовуємо дані з константного файлу
const MOCK_FILAMENTS = FILAMENT_DATA;

// Використовуємо рекомендовані товари з константного файлу

export default function FilamentDetailPage(props: {
  params: Promise<{ id: string }>
}) {
  // Access params using React.use() as recommended by Next.js
  const resolvedParams = React.use(props.params);
  const id = resolvedParams.id;
  const [activeImage, setActiveImage] = useState(0);
  const [activeTab, setActiveTab] = useState('overview');
  const [quantity, setQuantity] = useState(1);

  // Знаходимо філамент за ID
  const filament = MOCK_FILAMENTS.find(item => item.id === id) || MOCK_FILAMENTS[0];

  const { addToCart } = useCart();
  const { toast } = useToast();

  // Додавання товару до кошика
  const handleAddToCart = () => {
    addToCart({
      id: filament.id,
      title: filament.title,
      price: filament.price,
      thumbnail: filament.thumbnail,
    });

    toast({
      title: "Додано до кошика",
      description: `${filament.title} (${quantity} шт.) додано до вашого кошика.`,
    });
  };

  return (
    <main className="container mx-auto px-4 py-8">
      {/* Навігація */}
      <div className="mb-6">
        <Link href="/marketplace/filament" className="flex items-center text-muted-foreground hover:text-foreground transition-colors">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Назад до списку філаменту
        </Link>
      </div>

      {/* Основна інформація про філамент */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Галерея зображень */}
        <div className="lg:col-span-2 space-y-4">
          <div className="relative aspect-video bg-muted rounded-lg overflow-hidden">
            <img
              src={filament.images[activeImage] || filament.thumbnail}
              alt={filament.title}
              className="w-full h-full object-contain"
            />
          </div>
          <div className="flex space-x-2 overflow-x-auto pb-2">
            {filament.images.map((image, index) => (
              <button
                key={index}
                className={`relative w-20 h-20 rounded-md overflow-hidden flex-shrink-0 ${
                  activeImage === index ? 'ring-2 ring-primary' : ''
                }`}
                onClick={() => setActiveImage(index)}
              >
                <img src={image} alt={`${filament.title} view ${index + 1}`} className="w-full h-full object-cover" />
              </button>
            ))}
          </div>
        </div>

        {/* Інформація про товар */}
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold">{filament.title}</h1>
            <div className="flex items-center mt-2">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-5 w-5 ${
                      i < Math.floor(filament.rating)
                        ? 'text-yellow-400 fill-yellow-400'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
              <span className="ml-2 text-muted-foreground">
                {filament.rating} ({filament.reviewCount} відгуків)
              </span>
            </div>
            <div className="flex items-center mt-2">
              <Avatar className="h-6 w-6 mr-2">
                <AvatarImage src={filament.manufacturerLogo} alt={filament.manufacturer} />
                <AvatarFallback>{filament.manufacturer.substring(0, 2)}</AvatarFallback>
              </Avatar>
              <span className="text-muted-foreground">{filament.manufacturer}</span>
            </div>
          </div>

          <div className="text-3xl font-bold">${filament.price}</div>

          <div className="space-y-4">
            <div className="flex items-center text-sm">
              <ShieldCheck className="h-5 w-5 mr-2 text-green-500" />
              <span>Гарантія {filament.warranty}</span>
            </div>
            <div className="flex items-center text-sm">
              <Truck className="h-5 w-5 mr-2 text-blue-500" />
              <span>{filament.shipping}</span>
            </div>
            <Badge className={filament.inStock ? 'bg-green-500' : 'bg-red-500'}>
              {filament.inStock ? 'В наявності' : 'Немає в наявності'}
            </Badge>
          </div>

          <div className="flex items-center space-x-2">
            <div className="flex items-center border rounded-md">
              <button
                className="px-3 py-1 text-lg"
                onClick={() => setQuantity(Math.max(1, quantity - 1))}
                disabled={quantity <= 1}
              >
                -
              </button>
              <span className="px-3 py-1 border-x">{quantity}</span>
              <button
                className="px-3 py-1 text-lg"
                onClick={() => setQuantity(quantity + 1)}
              >
                +
              </button>
            </div>
            <Button className="flex-1" size="lg" onClick={handleAddToCart} disabled={!filament.inStock}>
              Додати до кошика
            </Button>
          </div>

          <div className="flex flex-wrap gap-2 mt-4">
            {filament.tags.map((tag) => (
              <Badge key={tag} variant="outline">
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      </div>

      {/* Вкладки з детальною інформацією */}
      <div className="mt-12">
        <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="w-full border-b justify-start">
            <TabsTrigger value="overview">Огляд</TabsTrigger>
            <TabsTrigger value="specifications">Характеристики</TabsTrigger>
            <TabsTrigger value="reviews">Відгуки ({filament.reviewCount})</TabsTrigger>
          </TabsList>
          <TabsContent value="overview" className="py-6">
            <div className="prose max-w-none dark:prose-invert">
              <h2 className="text-2xl font-bold mb-4">Опис</h2>
              <p className="mb-6">{filament.description}</p>

              <h3 className="text-xl font-semibold mb-3">Особливості</h3>
              <ul className="space-y-2 mb-6">
                {filament.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          </TabsContent>
          <TabsContent value="specifications" className="py-6">
            <div className="prose max-w-none dark:prose-invert">
              <h2 className="text-2xl font-bold mb-4">Технічні характеристики</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(filament.specifications).map(([key, value]) => (
                  <div key={key} className="border-b pb-2">
                    <span className="font-medium">{key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}: </span>
                    <span className="text-muted-foreground">{value}</span>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>
          <TabsContent value="reviews" className="py-6">
            <div className="space-y-6">
              <h2 className="text-2xl font-bold mb-4">Відгуки покупців</h2>

              <div className="flex items-center mb-6">
                <div className="flex items-center mr-4">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-6 w-6 ${
                        i < Math.floor(filament.rating)
                          ? 'text-yellow-400 fill-yellow-400'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <span className="text-xl font-semibold">{filament.rating} з 5</span>
                <span className="ml-2 text-muted-foreground">
                  на основі {filament.reviewCount} відгуків
                </span>
              </div>

              <div className="space-y-4">
                <p className="text-muted-foreground">Відгуки від покупців будуть доступні після перших продажів.</p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Рекомендовані товари */}
      <div className="mt-12">
        <h2 className="text-2xl font-bold mb-6">Вам також може сподобатися</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {RECOMMENDED_FILAMENTS.map((recFilament) => (
            <Link href={`/marketplace/filament/${recFilament.id}`} key={recFilament.id}>
              <Card className="overflow-hidden hover:shadow-md transition-shadow h-full">
                <div className="aspect-square relative">
                  <img
                    src={recFilament.thumbnail}
                    alt={recFilament.title}
                    className="w-full h-full object-cover"
                  />
                  <Badge className="absolute top-2 left-2 bg-blue-500">${recFilament.price}</Badge>
                </div>
                <div className="p-4">
                  <h3 className="font-semibold text-lg mb-1">{recFilament.title}</h3>
                  <p className="text-muted-foreground text-sm">
                    {recFilament.manufacturer}
                  </p>
                </div>
              </Card>
            </Link>
          ))}
        </div>
      </div>
    </main>
  );
}
