'use client';

import React, { useState } from 'react';

interface SplineSceneProps {
  sceneUrl: string;
  height?: string;
  className?: string;
}

export default function SplineScene({ 
  sceneUrl, 
  height = '400px',
  className = ''
}: SplineSceneProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleLoad = () => {
    setIsLoading(false);
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  return (
    <div className={`relative w-full overflow-hidden rounded-lg shadow-lg ${className}`} style={{ height }}>
      <iframe
        src={sceneUrl}
        width="100%"
        height="100%"
        style={{ 
          border: 'none',
          display: hasError ? 'none' : 'block'
        }}
        allowFullScreen
        title="3D Model Showcase"
        onLoad={handleLoad}
        onError={handleError}
      />
      
      {/* Показуємо індикатор завантаження */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-primary/5">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      )}
      
      {/* Показуємо повідомлення про помилку */}
      {hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-primary/10">
          <p className="text-lg font-medium">3D сцена недоступна. Спробуйте пізніше.</p>
        </div>
      )}
    </div>
  );
}
