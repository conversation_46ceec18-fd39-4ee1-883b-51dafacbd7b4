# Документація 3D-Маркетплейсу

## Вступ

Ласкаво просимо до документації 3D-маркетплейсу! Цей проект є платформою для купівлі, продажу та обміну 3D-моделями для 3D-друку та інших цілей. Документація містить інформацію про архітектуру проекту, компоненти, процес розробки та розгортання.

## Зміст

### Загальна документація

- [UI/UX Документація](./UI_UX_DOCUMENTATION.md) - Опис дизайн-системи, компонентів та взаємодії з користувачем
- [Документація компонентів](./COMPONENTS_DOCUMENTATION.md) - Детальний опис компонентів, які використовуються в проекті
- [3D-інтеграція](./3D_INTEGRATION_DOCUMENTATION.md) - Інформація про інтеграцію 3D-технологій у проект

### Розробка

- [Посібник з розробки](./DEVELOPMENT_GUIDE.md) - Інформація про налаштування середовища розробки, архітектуру проекту та процес розробки
- [Посібник з використання shadcn/ui](./SHADCN_GUIDE.md) - Інформація про використання компонентів shadcn/ui

### Розгортання

- [Розгортання на Cloudflare](./CLOUDFLARE_DEPLOYMENT.md) - Інформація про розгортання проекту на Cloudflare Pages

## Архітектура проекту

3D-маркетплейс побудований на основі наступних технологій:

- **Frontend**: Next.js 14, React 18, TypeScript
- **Стилізація**: Tailwind CSS, shadcn/ui
- **3D-технології**: Spline, Three.js, React Three Fiber
- **Розгортання**: Cloudflare Pages

## Структура проекту

```
3d-marketplace/
├── docs/                 # Документація
├── public/               # Статичні файли
├── src/
│   ├── app/              # Сторінки Next.js App Router
│   │   ├── auth/         # Сторінки аутентифікації
│   │   ├── models/       # Сторінки моделей
│   │   └── ...
│   ├── components/       # Компоненти React
│   │   ├── ui/           # Базові UI компоненти (shadcn/ui)
│   │   ├── model-viewer/ # Компоненти для перегляду 3D-моделей
│   │   └── ...
│   └── lib/              # Утиліти та допоміжні функції
├── .env.example          # Приклад змінних середовища
├── next.config.js        # Конфігурація Next.js
├── package.json          # Залежності проекту
└── tailwind.config.js    # Конфігурація Tailwind CSS
```

## Ключові функції

### Перегляд 3D-моделей

Проект дозволяє переглядати 3D-моделі в інтерактивному режимі:

- Обертання моделі
- Масштабування
- Панорамування
- Зміна освітлення
- Зміна фону

### Пошук та фільтрація

Користувачі можуть шукати та фільтрувати 3D-моделі за різними параметрами:

- Категорія
- Теги
- Ціна
- Популярність
- Дата додавання

### Завантаження моделей

Користувачі можуть завантажувати свої 3D-моделі на платформу:

- Підтримка різних форматів (STL, OBJ, GLTF, тощо)
- Додавання зображень
- Вказання деталей моделі
- Налаштування друку

## Початок роботи

### Вимоги

Для розробки проекту вам знадобиться:

- Node.js (версія 18.x або вище)
- npm (версія 9.x або вище) або yarn (версія 1.22.x або вище)
- Git
- Редактор коду (рекомендується VS Code)

### Встановлення

1. Клонуйте репозиторій:
```bash
git clone https://github.com/your-username/3d-marketplace.git
cd 3d-marketplace
```

2. Встановіть залежності:
```bash
npm install
# або
yarn install
```

3. Створіть файл `.env.local` на основі `.env.example`:
```bash
cp .env.example .env.local
```

4. Запустіть проект у режимі розробки:
```bash
npm run dev
# або
yarn dev
```

Проект буде доступний за адресою [http://localhost:3000](http://localhost:3000).

## Розгортання

Проект налаштований для розгортання на Cloudflare Pages. Детальну інформацію про розгортання можна знайти в [документації з розгортання на Cloudflare](./CLOUDFLARE_DEPLOYMENT.md).

## Внесок у проект

Ми вітаємо внесок у проект! Якщо ви хочете внести свій внесок, будь ласка, дотримуйтесь наступних кроків:

1. Створіть форк репозиторію
2. Створіть гілку для вашої функції (`git checkout -b feature/amazing-feature`)
3. Внесіть зміни
4. Зробіть коміт змін (`git commit -m 'Add some amazing feature'`)
5. Відправте зміни у ваш форк (`git push origin feature/amazing-feature`)
6. Створіть Pull Request

## Ліцензія

Цей проект ліцензований під [MIT License](../LICENSE).

## Контакти

Якщо у вас є питання або пропозиції, будь ласка, створіть Issue в репозиторії або зв'яжіться з нами за адресою [<EMAIL>](mailto:<EMAIL>).
