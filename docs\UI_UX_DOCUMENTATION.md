# UI/UX Документація для 3D-Маркетплейсу

## Зміст

1. [Вступ](#вступ)
2. [Дизайн-система](#дизайн-система)
   - [Кольорова схема](#кольорова-схема)
   - [Типографіка](#типографіка)
   - [Компоненти](#компоненти)
   - [Іконографія](#іконографія)
3. [Структура сторінок](#структура-сторінок)
   - [Головна сторінка](#головна-сторінка)
   - [Сторінка моделей](#сторінка-моделей)
   - [Сторінка деталей моделі](#сторінка-деталей-моделі)
   - [Сторінка завантаження моделі](#сторінка-завантаження-моделі)
4. [Навігація](#навігація)
   - [Головне меню](#головне-меню)
   - [Підменю](#підменю)
   - [Хлібні крихти](#хлібні-крихти)
5. [Взаємодія з користувачем](#взаємодія-з-користувачем)
   - [Форми](#форми)
   - [Сповіщення](#сповіщення)
   - [Модальні вікна](#модальні-вікна)
6. [3D-взаємодія](#3d-взаємодія)
   - [Перегляд моделей](#перегляд-моделей)
   - [Налаштування перегляду](#налаштування-перегляду)
7. [Адаптивний дизайн](#адаптивний-дизайн)
8. [Теми](#теми)
   - [Світла тема](#світла-тема)
   - [Темна тема](#темна-тема)
9. [Доступність](#доступність)
10. [Рекомендації для розробників](#рекомендації-для-розробників)

## Вступ

Ця документація описує UI/UX принципи та компоненти, які використовуються в 3D-маркетплейсі. Вона призначена для дизайнерів та розробників, які працюють над проектом, і забезпечує єдиний підхід до дизайну та розробки інтерфейсу.

## Дизайн-система

### Кольорова схема

Наш 3D-маркетплейс використовує систему кольорів, яка базується на CSS-змінних, що дозволяє легко змінювати тему та адаптувати дизайн.

#### Основні кольори

```css
--primary: 220.9 39.3% 11%;      /* Основний колір бренду */
--primary-foreground: 210 20% 98%; /* Текст на основному кольорі */
--secondary: 220 14.3% 95.9%;     /* Вторинний колір */
--secondary-foreground: 220.9 39.3% 11%; /* Текст на вторинному кольорі */
--background: 0 0% 100%;          /* Фон сторінки */
--foreground: 224 71.4% 4.1%;     /* Основний текст */
--muted: 220 14.3% 95.9%;         /* Приглушений фон */
--muted-foreground: 220 8.9% 46.1%; /* Приглушений текст */
--accent: 220 14.3% 95.9%;        /* Акцентний колір */
--accent-foreground: 220.9 39.3% 11%; /* Текст на акцентному кольорі */
--destructive: 0 84.2% 60.2%;     /* Колір для деструктивних дій */
--destructive-foreground: 210 20% 98%; /* Текст на деструктивному кольорі */
--border: 220 13% 91%;            /* Колір рамок */
--input: 220 13% 91%;             /* Колір рамок вводу */
--ring: 224 71.4% 4.1%;           /* Колір фокусу */
```

#### Кольори для графіків та візуалізацій

```css
--chart-1: 12 76% 61%;
--chart-2: 173 58% 39%;
--chart-3: 197 37% 24%;
--chart-4: 43 74% 66%;
--chart-5: 27 87% 67%;
```

### Типографіка

Проект використовує шрифт Inter для всього тексту, з різними вагами для різних елементів:

- **Заголовки**: Inter, font-weight: 600-700
- **Основний текст**: Inter, font-weight: 400
- **Акцентний текст**: Inter, font-weight: 500-600

Розміри шрифтів:
- Великі заголовки: 2rem - 3rem (32px - 48px)
- Середні заголовки: 1.5rem - 2rem (24px - 32px)
- Малі заголовки: 1.25rem - 1.5rem (20px - 24px)
- Основний текст: 1rem (16px)
- Малий текст: 0.875rem (14px)
- Дуже малий текст: 0.75rem (12px)

### Компоненти

Проект використовує компоненти shadcn/ui, які базуються на Radix UI та Tailwind CSS. Основні компоненти:

#### Кнопки

```tsx
<Button>Стандартна кнопка</Button>
<Button variant="secondary">Вторинна кнопка</Button>
<Button variant="destructive">Деструктивна кнопка</Button>
<Button variant="outline">Контурна кнопка</Button>
<Button variant="ghost">Прозора кнопка</Button>
<Button variant="link">Кнопка-посилання</Button>
```

Розміри кнопок:
```tsx
<Button size="sm">Маленька кнопка</Button>
<Button>Стандартна кнопка</Button>
<Button size="lg">Велика кнопка</Button>
<Button size="icon">Іконка</Button>
```

#### Картки

```tsx
<Card>
  <CardHeader>
    <CardTitle>Заголовок картки</CardTitle>
    <CardDescription>Опис картки</CardDescription>
  </CardHeader>
  <CardContent>
    Вміст картки
  </CardContent>
  <CardFooter>
    Футер картки
  </CardFooter>
</Card>
```

#### Форми

```tsx
<div className="space-y-2">
  <Label htmlFor="email">Email</Label>
  <Input id="email" type="email" placeholder="Введіть email" />
</div>
```

#### Вкладки

```tsx
<Tabs defaultValue="tab1">
  <TabsList>
    <TabsTrigger value="tab1">Вкладка 1</TabsTrigger>
    <TabsTrigger value="tab2">Вкладка 2</TabsTrigger>
  </TabsList>
  <TabsContent value="tab1">Вміст вкладки 1</TabsContent>
  <TabsContent value="tab2">Вміст вкладки 2</TabsContent>
</Tabs>
```

### Іконографія

Проект використовує бібліотеку іконок Lucide для всіх іконок. Приклади використання:

```tsx
<Heart className="h-4 w-4" />
<Download className="h-4 w-4" />
<Search className="h-4 w-4" />
```

## Структура сторінок

### Головна сторінка

Головна сторінка складається з наступних секцій:
1. **Герой-секція** - великий банер з заголовком, підзаголовком та кнопками дії
2. **Популярні теги** - горизонтальний список популярних тегів
3. **Популярні моделі** - сітка з популярними 3D-моделями
4. **Категорії** - сітка з категоріями 3D-моделей
5. **Колекції** - сітка з колекціями 3D-моделей
6. **Як це працює** - секція з поясненням процесу використання маркетплейсу
7. **Приєднуйтесь до спільноти** - секція з закликом до дії

### Сторінка моделей

Сторінка моделей містить:
1. **Фільтри** - бічна панель з фільтрами для пошуку моделей
2. **Сортування** - випадаюче меню для сортування моделей
3. **Сітка моделей** - сітка з картками моделей
4. **Пагінація** - навігація по сторінках результатів

### Сторінка деталей моделі

Сторінка деталей моделі містить:
1. **3D-перегляд** - інтерактивний 3D-перегляд моделі
2. **Інформація про модель** - назва, автор, опис, ціна, тощо
3. **Вкладки з додатковою інформацією** - огляд, налаштування друку, відгуки
4. **Рекомендовані моделі** - сітка з рекомендованими моделями

### Сторінка завантаження моделі

Сторінка завантаження моделі містить форму з кроками:
1. **Завантаження файлів** - завантаження 3D-файлів моделі
2. **Завантаження зображень** - завантаження зображень моделі
3. **Деталі моделі** - введення інформації про модель
4. **Налаштування друку** - введення рекомендованих налаштувань друку

## Навігація

### Головне меню

Головне меню розташоване у верхній частині сторінки і містить:
- Логотип (посилання на головну сторінку)
- Посилання на основні розділи сайту
- Пошук
- Перемикач теми
- Кнопки входу та реєстрації

### Підменю

Підменю з'являється при наведенні на пункти головного меню і містить додаткові посилання.

### Хлібні крихти

Хлібні крихти відображаються на внутрішніх сторінках і показують шлях від головної сторінки до поточної.

## Взаємодія з користувачем

### Форми

Форми використовують компоненти shadcn/ui і включають:
- Валідацію вводу
- Повідомлення про помилки
- Автоматичне форматування
- Підказки для користувача

### Сповіщення

Сповіщення відображаються у верхній частині сторінки і можуть бути:
- Інформаційними
- Успішними
- Попереджувальними
- Помилковими

### Модальні вікна

Модальні вікна використовуються для:
- Підтвердження дій
- Відображення додаткової інформації
- Форм, які не потребують окремої сторінки

## 3D-взаємодія

### Перегляд моделей

Для перегляду 3D-моделей використовується компонент ModelViewer, який підтримує:
- Обертання моделі
- Масштабування
- Панорамування
- Зміну освітлення
- Зміну фону

### Налаштування перегляду

Користувач може налаштувати перегляд моделі:
- Увімкнути/вимкнути автоматичне обертання
- Змінити швидкість обертання
- Змінити фон
- Показати/приховати сітку

## Адаптивний дизайн

Сайт адаптований для різних розмірів екранів:
- **Мобільні пристрої** (до 640px) - одна колонка, спрощений інтерфейс
- **Планшети** (641px - 1024px) - дві колонки, компактний інтерфейс
- **Десктопи** (від 1025px) - повний інтерфейс з усіма функціями

## Теми

### Світла тема

Світла тема використовує білий фон і темний текст, з акцентами основного кольору.

### Темна тема

Темна тема використовує темний фон і світлий текст, з акцентами основного кольору.

## Доступність

Сайт відповідає стандартам доступності WCAG 2.1:
- Всі інтерактивні елементи доступні з клавіатури
- Всі зображення мають альтернативний текст
- Контраст тексту відповідає вимогам
- Структура сторінки логічна і зрозуміла для скрін-рідерів

## Рекомендації для розробників

1. Використовуйте компоненти shadcn/ui замість створення власних
2. Додавайте `'use client'` на початку файлів, які використовують клієнтські компоненти
3. Використовуйте CSS-змінні для кольорів замість жорстко закодованих значень
4. Дотримуйтесь принципів адаптивного дизайну
5. Забезпечуйте доступність всіх компонентів
6. Використовуйте іконки з бібліотеки Lucide
7. Тестуйте інтерфейс на різних пристроях та браузерах
