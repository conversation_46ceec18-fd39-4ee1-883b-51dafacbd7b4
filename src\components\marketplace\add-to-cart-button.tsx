'use client';

import React, { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { ShoppingCart, Check, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useCart } from '@/context/cart-context';
import { useToast } from '@/hooks/use-toast';

interface AddToCartButtonProps {
  model: {
    id: string;
    name: string;
    price: number;
    thumbnail_url: string;
    is_free: boolean;
    user_id: string;
  };
  variant?: 'default' | 'outline' | 'secondary';
  size?: 'sm' | 'default' | 'lg';
  className?: string;
}

export default function AddToCartButton({ 
  model, 
  variant = 'default', 
  size = 'default',
  className = '' 
}: AddToCartButtonProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const { addToCart, isInCart } = useCart();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const isOwnModel = session?.user?.id === model.user_id;
  const alreadyInCart = isInCart(model.id);

  const handleAddToCart = async () => {
    // Перевірка аутентифікації
    if (!session) {
      toast({
        title: 'Потрібна авторизація',
        description: 'Увійдіть в систему, щоб додати модель до кошика',
        variant: 'destructive',
      });
      router.push('/auth/signin');
      return;
    }

    // Перевірка, чи це не власна модель
    if (isOwnModel) {
      toast({
        title: 'Неможливо додати',
        description: 'Ви не можете купити власну модель',
        variant: 'destructive',
      });
      return;
    }

    // Перевірка, чи модель вже в кошику
    if (alreadyInCart) {
      toast({
        title: 'Вже в кошику',
        description: 'Ця модель вже додана до вашого кошика',
      });
      return;
    }

    setIsLoading(true);

    try {
      // Для безкоштовних моделей - одразу "купуємо"
      if (model.is_free) {
        const response = await fetch('/api/models/download', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            modelId: model.id,
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to download free model');
        }

        const result = await response.json();
        
        toast({
          title: 'Модель завантажена!',
          description: 'Безкоштовна модель додана до ваших завантажень',
        });

        // Перенаправлення на сторінку завантажень
        router.push('/profile?tab=downloads');
        return;
      }

      // Для платних моделей - додаємо до кошика
      addToCart({
        id: model.id,
        name: model.name,
        price: model.price,
        thumbnail: model.thumbnail_url,
        type: 'model',
      });

      toast({
        title: 'Додано до кошика!',
        description: `${model.name} додано до вашого кошика`,
      });

    } catch (error) {
      console.error('Error adding to cart:', error);
      toast({
        title: 'Помилка',
        description: 'Не вдалося додати модель до кошика',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Якщо це власна модель користувача
  if (isOwnModel) {
    return (
      <Button 
        variant="outline" 
        size={size}
        className={className}
        disabled
      >
        Ваша модель
      </Button>
    );
  }

  // Якщо модель вже в кошику
  if (alreadyInCart && !model.is_free) {
    return (
      <Button 
        variant="outline" 
        size={size}
        className={className}
        onClick={() => router.push('/cart')}
      >
        <Check className="mr-2 h-4 w-4" />
        В кошику
      </Button>
    );
  }

  return (
    <Button
      variant={variant}
      size={size}
      className={className}
      onClick={handleAddToCart}
      disabled={isLoading}
    >
      {isLoading ? (
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
      ) : (
        <ShoppingCart className="mr-2 h-4 w-4" />
      )}
      {model.is_free ? 'Завантажити безкоштовно' : `Додати до кошика - $${model.price}`}
    </Button>
  );
}
