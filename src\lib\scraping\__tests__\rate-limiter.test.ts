/**
 * Tests for Rate Limiter
 */

import { MemoryRateLimiter, RateLimitError, PlatformRateLimiters, withRateLimit, platformRateLimiters } from '../rate-limiter';

describe('MemoryRateLimiter', () => {
  let rateLimiter: MemoryRateLimiter;

  beforeEach(() => {
    rateLimiter = new MemoryRateLimiter({
      windowMs: 1000, // 1 second window
      maxRequests: 3,  // 3 requests max
    });
  });

  describe('Basic rate limiting', () => {
    it('should allow requests within limit', async () => {
      const result1 = await rateLimiter.consume('test-key');
      expect(result1.remainingPoints).toBe(2);
      expect(result1.totalHitsPerWindow).toBe(1);

      const result2 = await rateLimiter.consume('test-key');
      expect(result2.remainingPoints).toBe(1);
      expect(result2.totalHitsPerWindow).toBe(2);

      const result3 = await rateLimiter.consume('test-key');
      expect(result3.remainingPoints).toBe(0);
      expect(result3.totalHitsPerWindow).toBe(3);
    });

    it('should throw RateLimitError when limit exceeded', async () => {
      // Consume all available requests
      await rateLimiter.consume('test-key');
      await rateLimiter.consume('test-key');
      await rateLimiter.consume('test-key');

      // Fourth request should fail
      await expect(rateLimiter.consume('test-key')).rejects.toThrow(RateLimitError);
    });

    it('should handle multiple points consumption', async () => {
      const result = await rateLimiter.consume('test-key', 2);
      expect(result.remainingPoints).toBe(1);
      expect(result.totalHitsPerWindow).toBe(2);

      // Should fail when trying to consume 2 more points
      await expect(rateLimiter.consume('test-key', 2)).rejects.toThrow(RateLimitError);
    });
  });

  describe('Window management', () => {
    it('should reset after window expires', async () => {
      // Consume all requests
      await rateLimiter.consume('test-key');
      await rateLimiter.consume('test-key');
      await rateLimiter.consume('test-key');

      // Should fail immediately
      await expect(rateLimiter.consume('test-key')).rejects.toThrow(RateLimitError);

      // Wait for window to expire
      await new Promise(resolve => setTimeout(resolve, 1100));

      // Should work again
      const result = await rateLimiter.consume('test-key');
      expect(result.remainingPoints).toBe(2);
    });

    it('should handle different keys independently', async () => {
      await rateLimiter.consume('key1');
      await rateLimiter.consume('key1');
      await rateLimiter.consume('key1');

      // key1 should be exhausted
      await expect(rateLimiter.consume('key1')).rejects.toThrow(RateLimitError);

      // key2 should still work
      const result = await rateLimiter.consume('key2');
      expect(result.remainingPoints).toBe(2);
    });
  });

  describe('Utility methods', () => {
    it('should reset key correctly', async () => {
      await rateLimiter.consume('test-key');
      await rateLimiter.consume('test-key');

      let remaining = await rateLimiter.getRemainingPoints('test-key');
      expect(remaining).toBe(1);

      await rateLimiter.reset('test-key');

      remaining = await rateLimiter.getRemainingPoints('test-key');
      expect(remaining).toBe(3);
    });

    it('should return correct remaining points', async () => {
      expect(await rateLimiter.getRemainingPoints('new-key')).toBe(3);

      await rateLimiter.consume('new-key');
      expect(await rateLimiter.getRemainingPoints('new-key')).toBe(2);

      await rateLimiter.consume('new-key', 2);
      expect(await rateLimiter.getRemainingPoints('new-key')).toBe(0);
    });
  });
});

describe('RateLimitError', () => {
  it('should create error with rate limit info', () => {
    const rateLimitInfo = {
      totalHits: 5,
      totalHitsPerWindow: 5,
      remainingPoints: 0,
      msBeforeNext: 1000,
      isFirstInDuration: false,
    };

    const error = new RateLimitError(rateLimitInfo);
    expect(error.message).toContain('Rate limit exceeded');
    expect(error.message).toContain('1 seconds');
    expect(error.rateLimitInfo).toEqual(rateLimitInfo);
    expect(error.name).toBe('RateLimitError');
  });
});

describe('PlatformRateLimiters', () => {
  let platformLimiters: PlatformRateLimiters;

  beforeEach(() => {
    platformLimiters = new PlatformRateLimiters();
  });

  describe('Platform-specific limits', () => {
    it('should enforce different limits for different platforms', async () => {
      // Printables: 10 requests/minute
      for (let i = 0; i < 10; i++) {
        await platformLimiters.consume('printables');
      }
      await expect(platformLimiters.consume('printables')).rejects.toThrow(RateLimitError);

      // MakerWorld should still work (15 requests/minute)
      const result = await platformLimiters.consume('makerworld');
      expect(result.remainingPoints).toBeGreaterThan(0);
    });

    it('should handle different identifiers for same platform', async () => {
      await platformLimiters.consume('printables', 'user1');
      await platformLimiters.consume('printables', 'user2');

      const remaining1 = await platformLimiters.getRemainingPoints('printables', 'user1');
      const remaining2 = await platformLimiters.getRemainingPoints('printables', 'user2');

      expect(remaining1).toBe(9); // 10 - 1
      expect(remaining2).toBe(9); // 10 - 1
    });
  });

  describe('Utility methods', () => {
    it('should get remaining points for all platforms', async () => {
      await platformLimiters.consume('printables');
      await platformLimiters.consume('makerworld');

      const allRemaining = await platformLimiters.getAllRemainingPoints();

      expect(allRemaining.printables).toBe(9);  // 10 - 1
      expect(allRemaining.makerworld).toBe(14); // 15 - 1
      expect(allRemaining.thangs).toBe(12);     // 12 - 0
    });

    it('should reset specific platform', async () => {
      await platformLimiters.consume('printables');
      await platformLimiters.consume('printables');

      let remaining = await platformLimiters.getRemainingPoints('printables');
      expect(remaining).toBe(8);

      await platformLimiters.reset('printables');

      remaining = await platformLimiters.getRemainingPoints('printables');
      expect(remaining).toBe(10);
    });
  });

  describe('Error handling', () => {
    it('should throw error for unsupported platform', async () => {
      await expect(platformLimiters.consume('unsupported' as any)).rejects.toThrow('No rate limiter configured');
    });

    it('should handle errors gracefully in getAllRemainingPoints', async () => {
      // This test ensures the method doesn't crash on errors
      const allRemaining = await platformLimiters.getAllRemainingPoints();
      expect(typeof allRemaining).toBe('object');
      expect(allRemaining.printables).toBeGreaterThanOrEqual(0);
    });
  });
});

describe('withRateLimit', () => {
  let platformLimiters: PlatformRateLimiters;

  beforeEach(() => {
    platformLimiters = new PlatformRateLimiters();
  });

  it('should execute operation when rate limit allows', async () => {
    const mockOperation = jest.fn().mockResolvedValue('success');

    const result = await withRateLimit('printables', 'test', mockOperation);

    expect(result).toBe('success');
    expect(mockOperation).toHaveBeenCalledTimes(1);
  });

  it('should throw RateLimitError when limit exceeded', async () => {
    const mockOperation = jest.fn().mockResolvedValue('success');

    // Create a fresh instance for this test
    const testPlatformLimiters = new PlatformRateLimiters();

    // Exhaust rate limit
    for (let i = 0; i < 10; i++) {
      await testPlatformLimiters.consume('printables', 'test');
    }

    // Mock the global instance to use our test instance
    const originalConsume = platformRateLimiters.consume;
    platformRateLimiters.consume = testPlatformLimiters.consume.bind(testPlatformLimiters);

    try {
      await expect(withRateLimit('printables', 'test', mockOperation)).rejects.toThrow(RateLimitError);
      expect(mockOperation).not.toHaveBeenCalled();
    } finally {
      // Restore original method
      platformRateLimiters.consume = originalConsume;
    }
  });

  it('should propagate operation errors', async () => {
    const mockOperation = jest.fn().mockRejectedValue(new Error('Operation failed'));

    await expect(withRateLimit('printables', 'test', mockOperation)).rejects.toThrow('Operation failed');
    expect(mockOperation).toHaveBeenCalledTimes(1);
  });
});

describe('Integration tests', () => {
  it('should handle concurrent requests correctly', async () => {
    const rateLimiter = new MemoryRateLimiter({
      windowMs: 1000,
      maxRequests: 5,
    });

    // Create 10 concurrent requests
    const promises = Array.from({ length: 10 }, (_, i) =>
      rateLimiter.consume('concurrent-test').catch(error => error)
    );

    const results = await Promise.all(promises);

    // Should have 5 successful results and 5 errors
    const successful = results.filter(r => !(r instanceof RateLimitError));
    const failed = results.filter(r => r instanceof RateLimitError);

    expect(successful).toHaveLength(5);
    expect(failed).toHaveLength(5);
  });

  it('should handle rapid sequential requests', async () => {
    const rateLimiter = new MemoryRateLimiter({
      windowMs: 100,
      maxRequests: 3,
    });

    // Make requests rapidly
    await rateLimiter.consume('rapid-test');
    await rateLimiter.consume('rapid-test');
    await rateLimiter.consume('rapid-test');

    // Fourth should fail
    await expect(rateLimiter.consume('rapid-test')).rejects.toThrow(RateLimitError);

    // Wait for window to reset
    await new Promise(resolve => setTimeout(resolve, 150));

    // Should work again
    const result = await rateLimiter.consume('rapid-test');
    expect(result.remainingPoints).toBe(2);
  });
});
