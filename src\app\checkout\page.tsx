'use client';

import React from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, ShoppingCart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useCart } from '@/context/cart-context';
import CheckoutForm from '@/components/marketplace/checkout-form';

export default function CheckoutPage() {
  const { data: session, status } = useSession();
  const { items, totalItems, totalPrice } = useCart();
  const router = useRouter();

  // Loading state
  if (status === 'loading') {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Redirect if not authenticated
  if (status === 'unauthenticated') {
    router.push('/auth/signin?callbackUrl=/checkout');
    return null;
  }

  // Redirect if cart is empty
  if (items.length === 0) {
    router.push('/cart');
    return null;
  }

  return (
    <main className="container mx-auto px-4 py-8 max-w-4xl">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Оформлення замовлення</h1>
          <Link href="/cart">
            <Button variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Назад до кошика
            </Button>
          </Link>
        </div>
        <p className="text-gray-600 mt-2">
          Завершіть покупку ваших 3D моделей
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Order Summary */}
        <div className="space-y-6">
          <div className="bg-card rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <ShoppingCart className="mr-2 h-5 w-5" />
              Ваше замовлення ({totalItems} {totalItems === 1 ? 'товар' : 'товарів'})
            </h2>
            
            <div className="space-y-4">
              {items.map((item) => (
                <div key={item.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                  <img
                    src={item.thumbnail || '/placeholder-model.jpg'}
                    alt={item.name}
                    className="w-16 h-16 object-cover rounded-md"
                  />
                  <div className="flex-1">
                    <h3 className="font-medium">{item.name}</h3>
                    <p className="text-sm text-gray-500">
                      Кількість: {item.quantity}
                    </p>
                    <p className="text-sm font-medium">
                      ${item.price.toFixed(2)} x {item.quantity} = ${(item.price * item.quantity).toFixed(2)}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            <div className="border-t pt-4 mt-4">
              <div className="flex justify-between items-center text-lg font-semibold">
                <span>Загальна сума:</span>
                <span>${totalPrice.toFixed(2)}</span>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                Включає всі податки та збори
              </p>
            </div>
          </div>

          {/* Purchase Terms */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-medium text-blue-900 mb-2">Умови покупки</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Після оплати ви отримаєте миттєвий доступ до завантаження</li>
              <li>• Файли будуть доступні в розділі "Мої завантаження"</li>
              <li>• Ліцензія дозволяє особисте використання</li>
              <li>• Повернення коштів протягом 30 днів</li>
            </ul>
          </div>
        </div>

        {/* Payment Form */}
        <div>
          <CheckoutForm 
            onSuccess={() => {
              router.push('/checkout/success');
            }}
          />
        </div>
      </div>
    </main>
  );
}
