import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';

// Маршрути, які потребують автентифікації
const protectedPaths = [
  '/models/upload',
  '/profile',
  '/dashboard',
  '/orders',
  '/purchases',
];

// Маршрути, які доступні тільки для неавтентифікованих користувачів
const authRoutes = [
  '/auth/signin',
  '/auth/signup',
  '/auth/forgot-password',
  '/auth/reset-password',
];

// Перевірка, чи маршрут потребує автентифікації
const isProtectedPath = (path: string) => {
  return protectedPaths.some(protectedPath =>
    path === protectedPath || path.startsWith(`${protectedPath}/`)
  );
};

// Перевірка, чи маршрут доступний тільки для неавтентифікованих користувачів
const isAuthRoute = (path: string) => {
  return authRoutes.some(authRoute =>
    path === authRoute || path.startsWith(`${authRoute}/`)
  );
};

// This middleware runs on all routes
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Add CORS headers for API routes
  if (pathname.startsWith('/api/')) {
    const response = NextResponse.next();

    response.headers.set('Access-Control-Allow-Origin', '*');
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    return response;
  }

  // Отримання токена автентифікації
  const token = await getToken({
    req: request,
    secret: process.env.NEXTAUTH_SECRET,
  });

  // Перенаправлення автентифікованих користувачів з маршрутів автентифікації
  if (token && isAuthRoute(pathname)) {
    return NextResponse.redirect(new URL('/', request.url));
  }

  // Перенаправлення неавтентифікованих користувачів з захищених маршрутів
  if (!token && isProtectedPath(pathname)) {
    const callbackUrl = encodeURIComponent(request.url);
    return NextResponse.redirect(new URL(`/auth/signin?callbackUrl=${callbackUrl}`, request.url));
  }

  return NextResponse.next();
}

// Configure middleware to run on specific paths
export const config = {
  matcher: [
    // Match all API routes
    '/api/:path*',
    // Match protected paths
    '/models/upload', '/models/upload/:path*',
    '/profile', '/profile/:path*',
    '/dashboard', '/dashboard/:path*',
    '/orders', '/orders/:path*',
    '/purchases', '/purchases/:path*',
    // Match auth routes
    '/auth/signin', '/auth/signin/:path*',
    '/auth/signup', '/auth/signup/:path*',
    '/auth/forgot-password', '/auth/forgot-password/:path*',
    '/auth/reset-password', '/auth/reset-password/:path*'
  ],
};
