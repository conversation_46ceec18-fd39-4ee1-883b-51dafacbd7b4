/**
 * Cloudflare Observability Integration for 3D Marketplace
 * Provides comprehensive monitoring, logging, and analytics
 */

import { ModelSource } from '@/types/models';

export interface CloudflareMetric {
  name: string;
  value: number;
  unit?: string;
  tags?: Record<string, string>;
  timestamp?: string;
}

export interface CloudflareLogEvent {
  level: 'debug' | 'info' | 'warn' | 'error' | 'fatal';
  message: string;
  service: string;
  platform?: ModelSource;
  userId?: string;
  sessionId?: string;
  requestId?: string;
  duration?: number;
  metadata?: Record<string, any>;
}

export interface ScrapingMetrics {
  platform: ModelSource;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  rateLimitHits: number;
  cacheHits: number;
  cacheMisses: number;
}

export interface JobQueueMetrics {
  totalJobs: number;
  pendingJobs: number;
  processingJobs: number;
  completedJobs: number;
  failedJobs: number;
  averageProcessingTime: number;
  queueThroughput: number;
}

export class CloudflareMonitoring {
  private static instance: CloudflareMonitoring;
  private d1Database: any = null;
  private kvNamespace: any = null;
  private analyticsEngine: any = null;

  constructor() {
    this.initializeBindings();
  }

  static getInstance(): CloudflareMonitoring {
    if (!CloudflareMonitoring.instance) {
      CloudflareMonitoring.instance = new CloudflareMonitoring();
    }
    return CloudflareMonitoring.instance;
  }

  /**
   * Ініціалізація Cloudflare bindings
   */
  private initializeBindings() {
    try {
      if (typeof globalThis !== 'undefined' && (globalThis as any).env) {
        this.d1Database = (globalThis as any).env.DB;
        this.kvNamespace = (globalThis as any).env.CACHE_KV || (globalThis as any).env.JOB_QUEUE_KV;
        this.analyticsEngine = (globalThis as any).env.ANALYTICS;
      }

      // Для локальної розробки логуємо статус bindings
      if (!this.d1Database) {
        console.info('⚠️ D1 Database binding not found - using fallback logging');
      }

      if (!this.kvNamespace) {
        console.info('⚠️ KV Storage binding not found - metrics caching disabled');
      }

      if (!this.analyticsEngine) {
        console.info('⚠️ Analytics Engine binding not found - real-time analytics disabled');
      }
    } catch (error) {
      console.warn('Cloudflare bindings initialization failed:', error);
    }
  }

  /**
   * Логування подій
   */
  async logEvent(event: CloudflareLogEvent): Promise<void> {
    const logEntry = {
      ...event,
      timestamp: new Date().toISOString(),
      requestId: event.requestId || this.generateRequestId(),
    };

    // Логування в консоль для розробки
    console.log(`[${event.level.toUpperCase()}] ${event.service}: ${event.message}`, {
      platform: event.platform,
      userId: event.userId,
      duration: event.duration,
      metadata: event.metadata
    });

    // Збереження в D1 для довгострокового зберігання
    if (this.d1Database && event.level !== 'debug') {
      try {
        await this.d1Database.prepare(`
          INSERT INTO error_logs (
            id, code, message, user_message, category, severity,
            platform, url, user_id, session_id, user_agent,
            context, retryable, reportable, created_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).bind(
          logEntry.requestId,
          'LOG_EVENT',
          event.message,
          event.message,
          'system',
          event.level,
          event.platform,
          null,
          event.userId,
          event.sessionId,
          null,
          JSON.stringify(event.metadata || {}),
          false,
          event.level === 'error' || event.level === 'fatal',
          logEntry.timestamp
        ).run();
      } catch (error) {
        console.error('Failed to save log to D1:', error);
      }
    }

    // Відправка в Analytics Engine для real-time аналітики
    if (this.analyticsEngine) {
      try {
        this.analyticsEngine.writeDataPoint({
          blobs: [event.service, event.platform || 'unknown'],
          doubles: [event.duration || 0],
          indexes: [event.level, event.userId || 'anonymous']
        });
      } catch (error) {
        console.error('Failed to send to Analytics Engine:', error);
      }
    }
  }

  /**
   * Запис метрик
   */
  async recordMetric(metric: CloudflareMetric): Promise<void> {
    const metricEntry = {
      ...metric,
      timestamp: metric.timestamp || new Date().toISOString(),
      id: this.generateRequestId()
    };

    // Збереження в D1
    if (this.d1Database) {
      try {
        await this.d1Database.prepare(`
          INSERT INTO system_metrics (
            id, metric_name, metric_value, metric_unit, tags, timestamp, source
          ) VALUES (?, ?, ?, ?, ?, ?, ?)
        `).bind(
          metricEntry.id,
          metric.name,
          metric.value,
          metric.unit || 'count',
          JSON.stringify(metric.tags || {}),
          metricEntry.timestamp,
          'marketplace'
        ).run();
      } catch (error) {
        console.error('Failed to save metric to D1:', error);
      }
    }

    // Кешування останніх метрик в KV
    if (this.kvNamespace) {
      try {
        const cacheKey = `metric:${metric.name}:latest`;
        await this.kvNamespace.put(cacheKey, JSON.stringify(metricEntry), {
          expirationTtl: 3600 // 1 година
        });
      } catch (error) {
        console.error('Failed to cache metric in KV:', error);
      }
    }
  }

  /**
   * Моніторинг скрапінгу
   */
  async trackScrapingOperation(
    platform: ModelSource,
    operation: 'start' | 'success' | 'error' | 'rate_limit',
    metadata?: Record<string, any>
  ): Promise<void> {
    const baseEvent: CloudflareLogEvent = {
      level: operation === 'error' ? 'error' : 'info',
      message: `Scraping ${operation} for ${platform}`,
      service: 'scraper',
      platform,
      metadata
    };

    await this.logEvent(baseEvent);

    // Запис метрик
    await this.recordMetric({
      name: `scraping.${operation}`,
      value: 1,
      tags: { platform, operation }
    });

    if (metadata?.duration) {
      await this.recordMetric({
        name: 'scraping.duration',
        value: metadata.duration,
        unit: 'ms',
        tags: { platform }
      });
    }
  }

  /**
   * Моніторинг черги завдань
   */
  async trackJobQueueOperation(
    operation: 'job_created' | 'job_started' | 'job_completed' | 'job_failed',
    jobId: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logEvent({
      level: operation === 'job_failed' ? 'error' : 'info',
      message: `Job queue ${operation}: ${jobId}`,
      service: 'job-queue',
      requestId: jobId,
      metadata
    });

    await this.recordMetric({
      name: `job_queue.${operation}`,
      value: 1,
      tags: { operation }
    });
  }

  /**
   * Моніторинг API запитів
   */
  async trackAPIRequest(
    endpoint: string,
    method: string,
    statusCode: number,
    duration: number,
    userId?: string
  ): Promise<void> {
    const level = statusCode >= 500 ? 'error' : statusCode >= 400 ? 'warn' : 'info';
    
    await this.logEvent({
      level,
      message: `API ${method} ${endpoint} - ${statusCode}`,
      service: 'api',
      userId,
      duration,
      metadata: { endpoint, method, statusCode }
    });

    await this.recordMetric({
      name: 'api.requests',
      value: 1,
      tags: { endpoint, method, status: statusCode.toString() }
    });

    await this.recordMetric({
      name: 'api.response_time',
      value: duration,
      unit: 'ms',
      tags: { endpoint, method }
    });
  }

  /**
   * Отримання метрик скрапінгу
   */
  async getScrapingMetrics(platform?: ModelSource, timeRange?: string): Promise<ScrapingMetrics[]> {
    if (!this.d1Database) return [];

    try {
      let query = `
        SELECT 
          JSON_EXTRACT(tags, '$.platform') as platform,
          COUNT(*) as total_requests,
          SUM(CASE WHEN metric_name = 'scraping.success' THEN metric_value ELSE 0 END) as successful_requests,
          SUM(CASE WHEN metric_name = 'scraping.error' THEN metric_value ELSE 0 END) as failed_requests,
          AVG(CASE WHEN metric_name = 'scraping.duration' THEN metric_value ELSE NULL END) as avg_response_time,
          SUM(CASE WHEN metric_name = 'scraping.rate_limit' THEN metric_value ELSE 0 END) as rate_limit_hits
        FROM system_metrics 
        WHERE metric_name LIKE 'scraping.%'
      `;

      const params: any[] = [];

      if (platform) {
        query += ` AND JSON_EXTRACT(tags, '$.platform') = ?`;
        params.push(platform);
      }

      if (timeRange) {
        query += ` AND timestamp > datetime('now', ?)`;
        params.push(timeRange);
      }

      query += ` GROUP BY JSON_EXTRACT(tags, '$.platform')`;

      const result = await this.d1Database.prepare(query).bind(...params).all();

      return result.results.map((row: any) => ({
        platform: row.platform,
        totalRequests: row.total_requests || 0,
        successfulRequests: row.successful_requests || 0,
        failedRequests: row.failed_requests || 0,
        averageResponseTime: row.avg_response_time || 0,
        rateLimitHits: row.rate_limit_hits || 0,
        cacheHits: 0, // TODO: Implement cache metrics
        cacheMisses: 0
      }));
    } catch (error) {
      console.error('Failed to get scraping metrics:', error);
      return [];
    }
  }

  /**
   * Отримання метрик черги завдань
   */
  async getJobQueueMetrics(): Promise<JobQueueMetrics> {
    if (!this.d1Database) {
      return {
        totalJobs: 0,
        pendingJobs: 0,
        processingJobs: 0,
        completedJobs: 0,
        failedJobs: 0,
        averageProcessingTime: 0,
        queueThroughput: 0
      };
    }

    try {
      const result = await this.d1Database.prepare(`
        SELECT 
          COUNT(*) as total_jobs,
          SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_jobs,
          SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing_jobs,
          SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_jobs,
          SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_jobs,
          AVG(
            CASE 
              WHEN completed_at IS NOT NULL AND started_at IS NOT NULL 
              THEN (julianday(completed_at) - julianday(started_at)) * 24 * 60 * 60 * 1000
              ELSE NULL 
            END
          ) as avg_processing_time
        FROM job_queue
        WHERE created_at > datetime('now', '-24 hours')
      `).first();

      return {
        totalJobs: result?.total_jobs || 0,
        pendingJobs: result?.pending_jobs || 0,
        processingJobs: result?.processing_jobs || 0,
        completedJobs: result?.completed_jobs || 0,
        failedJobs: result?.failed_jobs || 0,
        averageProcessingTime: result?.avg_processing_time || 0,
        queueThroughput: (result?.completed_jobs || 0) / 24 // jobs per hour
      };
    } catch (error) {
      console.error('Failed to get job queue metrics:', error);
      return {
        totalJobs: 0,
        pendingJobs: 0,
        processingJobs: 0,
        completedJobs: 0,
        failedJobs: 0,
        averageProcessingTime: 0,
        queueThroughput: 0
      };
    }
  }

  /**
   * Генерація унікального ID запиту
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  /**
   * Очищення старих логів та метрик
   */
  async cleanup(): Promise<void> {
    if (!this.d1Database) return;

    try {
      // Очищення старих логів (старше 30 днів)
      await this.d1Database.prepare(`
        DELETE FROM error_logs 
        WHERE created_at < datetime('now', '-30 days')
      `).run();

      // Очищення старих метрик (старше 90 днів)
      await this.d1Database.prepare(`
        DELETE FROM system_metrics 
        WHERE timestamp < datetime('now', '-90 days')
      `).run();

      console.log('Cleanup completed successfully');
    } catch (error) {
      console.error('Failed to cleanup old data:', error);
    }
  }
}

// Export singleton instance
export const cloudflareMonitoring = CloudflareMonitoring.getInstance();
