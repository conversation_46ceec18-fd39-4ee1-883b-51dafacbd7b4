import { DownloadManager } from './durable-objects/download-manager';

// Export Durable Object class
export { DownloadManager };

// Environment interface
export interface Env {
  DOWNLOAD_MANAGER: DurableObjectNamespace;
  DB: D1Database;
  R2_BUCKET: R2Bucket;
  CACHE_KV: KVNamespace;
  ANALYTICS: AnalyticsEngineDataset;
  BACKGROUND_QUEUE: Queue;
  
  // Secrets
  STRIPE_SECRET_KEY: string;
  STRIPE_WEBHOOK_SECRET: string;
  NEXTAUTH_SECRET: string;
  GITHUB_CLIENT_ID: string;
  GITHUB_CLIENT_SECRET: string;
  GOOGLE_CLIENT_ID: string;
  GOOGLE_CLIENT_SECRET: string;
  EMAIL_SERVER_HOST: string;
  EMAIL_SERVER_PORT: string;
  EMAIL_SERVER_USER: string;
  EMAIL_SERVER_PASSWORD: string;
  EMAIL_FROM: string;
  
  // Environment
  ENVIRONMENT: string;
}

// Main Worker handler
export default {
  async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
    const url = new URL(request.url);
    
    // Handle CORS preflight requests
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          'Access-Control-Max-Age': '86400',
        },
      });
    }

    // Route to Durable Objects for download management
    if (url.pathname.startsWith('/api/downloads/')) {
      const modelId = url.pathname.split('/')[3];
      if (!modelId) {
        return new Response('Model ID required', { status: 400 });
      }

      // Get Durable Object instance for this model
      const durableObjectId = env.DOWNLOAD_MANAGER.idFromName(`download-${modelId}`);
      const durableObject = env.DOWNLOAD_MANAGER.get(durableObjectId);
      
      // Forward request to Durable Object
      return durableObject.fetch(request);
    }

    // Health check endpoint
    if (url.pathname === '/health') {
      return new Response(JSON.stringify({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        environment: env.ENVIRONMENT,
      }), {
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Analytics endpoint
    if (url.pathname === '/api/analytics/track' && request.method === 'POST') {
      try {
        const data = await request.json() as any;
        
        // Write to Analytics Engine
        env.ANALYTICS.writeDataPoint({
          blobs: [
            data.event || 'unknown',
            data.userId || 'anonymous',
            data.modelId || '',
            data.category || '',
          ],
          doubles: [
            data.value || 0,
            data.timestamp || Date.now(),
          ],
          indexes: [
            data.sessionId || '',
          ],
        });

        return new Response(JSON.stringify({ success: true }), {
          headers: { 'Content-Type': 'application/json' },
        });
      } catch (error) {
        console.error('Analytics error:', error);
        return new Response(JSON.stringify({ 
          success: false, 
          error: 'Failed to track event' 
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        });
      }
    }

    // Cache management endpoint
    if (url.pathname.startsWith('/api/cache/')) {
      const action = url.pathname.split('/')[3];
      const key = url.searchParams.get('key');

      if (!key) {
        return new Response('Cache key required', { status: 400 });
      }

      switch (action) {
        case 'get':
          const value = await env.CACHE_KV.get(key);
          return new Response(JSON.stringify({ 
            success: true, 
            value: value ? JSON.parse(value) : null 
          }), {
            headers: { 'Content-Type': 'application/json' },
          });

        case 'set':
          if (request.method !== 'POST') {
            return new Response('POST method required', { status: 405 });
          }
          const data = await request.json() as any;
          const ttl = data.ttl || 3600; // Default 1 hour
          await env.CACHE_KV.put(key, JSON.stringify(data.value), {
            expirationTtl: ttl,
          });
          return new Response(JSON.stringify({ success: true }), {
            headers: { 'Content-Type': 'application/json' },
          });

        case 'delete':
          await env.CACHE_KV.delete(key);
          return new Response(JSON.stringify({ success: true }), {
            headers: { 'Content-Type': 'application/json' },
          });

        default:
          return new Response('Invalid cache action', { status: 400 });
      }
    }

    // Background task queue endpoint
    if (url.pathname === '/api/queue/send' && request.method === 'POST') {
      try {
        const task = await request.json() as any;
        
        await env.BACKGROUND_QUEUE.send({
          type: task.type || 'generic',
          data: task.data || {},
          timestamp: Date.now(),
        });

        return new Response(JSON.stringify({ success: true }), {
          headers: { 'Content-Type': 'application/json' },
        });
      } catch (error) {
        console.error('Queue error:', error);
        return new Response(JSON.stringify({ 
          success: false, 
          error: 'Failed to queue task' 
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        });
      }
    }

    // File upload to R2 endpoint
    if (url.pathname === '/api/upload' && request.method === 'POST') {
      try {
        const formData = await request.formData();
        const file = formData.get('file') as File;
        
        if (!file) {
          return new Response('No file provided', { status: 400 });
        }

        const fileName = `${Date.now()}-${file.name}`;
        const arrayBuffer = await file.arrayBuffer();
        
        await env.R2_BUCKET.put(fileName, arrayBuffer, {
          httpMetadata: {
            contentType: file.type,
          },
        });

        return new Response(JSON.stringify({
          success: true,
          fileName,
          url: `https://your-r2-domain.com/${fileName}`,
        }), {
          headers: { 'Content-Type': 'application/json' },
        });
      } catch (error) {
        console.error('Upload error:', error);
        return new Response(JSON.stringify({ 
          success: false, 
          error: 'Failed to upload file' 
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        });
      }
    }

    // Default response for unhandled routes
    return new Response('Not Found', { status: 404 });
  },

  // Queue consumer for background tasks
  async queue(batch: MessageBatch<any>, env: Env): Promise<void> {
    for (const message of batch.messages) {
      try {
        const { type, data, timestamp } = message.body;
        
        switch (type) {
          case 'cleanup_downloads':
            // Cleanup expired download sessions
            await cleanupExpiredDownloads(env);
            break;
            
          case 'update_analytics':
            // Update analytics data
            await updateAnalytics(env, data);
            break;
            
          case 'send_email':
            // Send email notification
            await sendEmailNotification(env, data);
            break;
            
          default:
            console.log(`Unknown task type: ${type}`);
        }
        
        message.ack();
      } catch (error) {
        console.error('Queue processing error:', error);
        message.retry();
      }
    }
  },
};

// Helper functions
async function cleanupExpiredDownloads(env: Env): Promise<void> {
  // Implementation for cleaning up expired download sessions
  console.log('Cleaning up expired downloads...');
}

async function updateAnalytics(env: Env, data: any): Promise<void> {
  // Implementation for updating analytics
  console.log('Updating analytics...', data);
}

async function sendEmailNotification(env: Env, data: any): Promise<void> {
  // Implementation for sending email notifications
  console.log('Sending email notification...', data);
}
