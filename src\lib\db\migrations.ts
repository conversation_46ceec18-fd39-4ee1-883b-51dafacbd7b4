import { D1Database } from './types';
import { execute, getDb, transaction } from './client';

/**
 * Інтерфейс для міграції
 */
export interface Migration {
  id: string;
  name: string;
  up: (db: D1Database) => Promise<void>;
  down?: (db: D1Database) => Promise<void>;
}

/**
 * Створює таблицю міграцій, якщо вона не існує
 * @param db Екземпляр бази даних
 */
async function createMigrationsTable(db: D1Database): Promise<void> {
  await db.exec(`
    CREATE TABLE IF NOT EXISTS migrations (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `);
}

/**
 * Отримує список застосованих міграцій
 * @param db Екземпляр бази даних
 * @returns Список застосованих міграцій
 */
async function getAppliedMigrations(db: D1Database): Promise<string[]> {
  const result = await db.prepare('SELECT id FROM migrations ORDER BY applied_at').all<{ id: string }>();
  return result.results.map(row => row.id);
}

/**
 * Застосовує міграцію
 * @param db Екземпляр бази даних
 * @param migration Міграція для застосування
 */
async function applyMigration(db: D1Database, migration: Migration): Promise<void> {
  await transaction(async (tx) => {
    // Застосування міграції
    await migration.up(tx);
    
    // Запис інформації про міграцію
    await tx.prepare('INSERT INTO migrations (id, name) VALUES (?, ?)')
      .bind(migration.id, migration.name)
      .run();
  });
  
  console.log(`Applied migration: ${migration.name} (${migration.id})`);
}

/**
 * Відкочує міграцію
 * @param db Екземпляр бази даних
 * @param migration Міграція для відкату
 */
async function rollbackMigration(db: D1Database, migration: Migration): Promise<void> {
  if (!migration.down) {
    throw new Error(`Migration ${migration.name} (${migration.id}) does not support rollback`);
  }
  
  await transaction(async (tx) => {
    // Відкат міграції
    await migration.down!(tx);
    
    // Видалення інформації про міграцію
    await tx.prepare('DELETE FROM migrations WHERE id = ?')
      .bind(migration.id)
      .run();
  });
  
  console.log(`Rolled back migration: ${migration.name} (${migration.id})`);
}

/**
 * Застосовує всі нові міграції
 * @param migrations Список міграцій
 */
export async function migrate(migrations: Migration[]): Promise<void> {
  const db = getDb();
  
  // Створення таблиці міграцій
  await createMigrationsTable(db);
  
  // Отримання списку застосованих міграцій
  const appliedMigrations = await getAppliedMigrations(db);
  
  // Сортування міграцій за ID
  const sortedMigrations = [...migrations].sort((a, b) => a.id.localeCompare(b.id));
  
  // Застосування нових міграцій
  for (const migration of sortedMigrations) {
    if (!appliedMigrations.includes(migration.id)) {
      await applyMigration(db, migration);
    }
  }
}

/**
 * Відкочує останню міграцію
 * @param migrations Список міграцій
 */
export async function rollback(migrations: Migration[]): Promise<void> {
  const db = getDb();
  
  // Створення таблиці міграцій
  await createMigrationsTable(db);
  
  // Отримання списку застосованих міграцій
  const appliedMigrations = await getAppliedMigrations(db);
  
  if (appliedMigrations.length === 0) {
    console.log('No migrations to roll back');
    return;
  }
  
  // Отримання останньої застосованої міграції
  const lastMigrationId = appliedMigrations[appliedMigrations.length - 1];
  const lastMigration = migrations.find(m => m.id === lastMigrationId);
  
  if (!lastMigration) {
    throw new Error(`Migration with ID ${lastMigrationId} not found`);
  }
  
  // Відкат останньої міграції
  await rollbackMigration(db, lastMigration);
}

/**
 * Відкочує всі міграції
 * @param migrations Список міграцій
 */
export async function reset(migrations: Migration[]): Promise<void> {
  const db = getDb();
  
  // Створення таблиці міграцій
  await createMigrationsTable(db);
  
  // Отримання списку застосованих міграцій
  const appliedMigrations = await getAppliedMigrations(db);
  
  // Відкат міграцій у зворотному порядку
  for (const migrationId of [...appliedMigrations].reverse()) {
    const migration = migrations.find(m => m.id === migrationId);
    
    if (!migration) {
      throw new Error(`Migration with ID ${migrationId} not found`);
    }
    
    await rollbackMigration(db, migration);
  }
}

/**
 * Застосовує SQL-скрипт до бази даних
 * @param sql SQL-скрипт
 */
export async function applySqlScript(sql: string): Promise<void> {
  const db = getDb();
  
  // Розділення скрипту на окремі запити
  const queries = sql
    .split(';')
    .map(query => query.trim())
    .filter(query => query.length > 0);
  
  // Виконання запитів
  for (const query of queries) {
    await db.exec(query);
  }
}
