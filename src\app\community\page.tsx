'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { 
  Users, 
  MessageSquare, 
  Calendar, 
  Award, 
  Heart, 
  ThumbsUp, 
  MessageCircle, 
  Eye, 
  Clock, 
  ChevronRight,
  Image,
  FileText,
  Bookmark,
  Share2,
  Search
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';

// Тимчасові дані для демонстрації
const FORUM_TOPICS = [
  {
    id: '1',
    title: 'Поради для початківців у 3D-друку',
    author: 'PrintMaster',
    authorAvatar: 'https://placehold.co/40x40/png',
    date: '2 дні тому',
    replies: 24,
    views: 342,
    category: 'Поради',
    isSticky: true
  },
  {
    id: '2',
    title: 'Проблеми з налаштуванням Ender 3',
    author: 'NewPrinter',
    authorAvatar: 'https://placehold.co/40x40/png',
    date: '5 днів тому',
    replies: 18,
    views: 215,
    category: 'Технічні питання',
    isSticky: false
  },
  {
    id: '3',
    title: 'Огляд нових матеріалів для друку',
    author: 'MaterialExpert',
    authorAvatar: 'https://placehold.co/40x40/png',
    date: '1 тиждень тому',
    replies: 32,
    views: 487,
    category: 'Матеріали',
    isSticky: false
  },
  {
    id: '4',
    title: 'Конкурс на найкращу функціональну модель',
    author: 'Admin',
    authorAvatar: 'https://placehold.co/40x40/png',
    date: '2 тижні тому',
    replies: 56,
    views: 890,
    category: 'Конкурси',
    isSticky: true
  },
  {
    id: '5',
    title: 'Як оптимізувати моделі для друку?',
    author: 'Optimizer',
    authorAvatar: 'https://placehold.co/40x40/png',
    date: '3 тижні тому',
    replies: 41,
    views: 623,
    category: 'Моделювання',
    isSticky: false
  }
];

const COMMUNITY_PROJECTS = [
  {
    id: '1',
    title: 'Модульна система зберігання інструментів',
    author: 'ToolMaster',
    authorAvatar: 'https://placehold.co/40x40/png',
    image: 'https://placehold.co/600x400/png?text=Tool+Storage',
    likes: 156,
    comments: 32,
    category: 'Функціональні',
    description: 'Повністю модульна система для зберігання інструментів, яку можна налаштувати під свої потреби.'
  },
  {
    id: '2',
    title: 'Колекція мініатюр для настільних ігор',
    author: 'GameMaster',
    authorAvatar: 'https://placehold.co/40x40/png',
    image: 'https://placehold.co/600x400/png?text=Miniatures',
    likes: 243,
    comments: 47,
    category: 'Ігри',
    description: 'Набір високодеталізованих мініатюр для фентезійних настільних ігор.'
  },
  {
    id: '3',
    title: 'Розумний горщик для рослин',
    author: 'GreenThumb',
    authorAvatar: 'https://placehold.co/40x40/png',
    image: 'https://placehold.co/600x400/png?text=Smart+Planter',
    likes: 189,
    comments: 28,
    category: 'Розумний дім',
    description: 'Горщик з вбудованою системою автоматичного поливу та моніторингу стану рослини.'
  }
];

const UPCOMING_EVENTS = [
  {
    id: '1',
    title: 'Майстер-клас з 3D-моделювання',
    date: '15 червня 2023',
    time: '14:00 - 16:00',
    location: 'Онлайн (Zoom)',
    organizer: 'Cube 3D Academy',
    attendees: 78,
    image: 'https://placehold.co/600x400/png?text=3D+Modeling+Workshop'
  },
  {
    id: '2',
    title: 'Виставка 3D-друкованих проектів',
    date: '22-24 червня 2023',
    time: '10:00 - 18:00',
    location: 'Київ, Експоцентр',
    organizer: 'Українська асоціація 3D-друку',
    attendees: 215,
    image: 'https://placehold.co/600x400/png?text=3D+Printing+Expo'
  },
  {
    id: '3',
    title: 'Хакатон: Розумні пристрої на 3D-принтері',
    date: '8-9 липня 2023',
    time: '09:00 - 21:00',
    location: 'Львів, IT Hub',
    organizer: 'Tech Innovators',
    attendees: 42,
    image: 'https://placehold.co/600x400/png?text=Smart+Devices+Hackathon'
  }
];

const COMMUNITY_MEMBERS = [
  {
    id: '1',
    name: 'PrintMaster',
    avatar: 'https://placehold.co/40x40/png',
    role: 'Експерт',
    contributions: 156,
    joined: 'Січень 2021',
    badges: ['Топ-дописувач', 'Модератор', 'Переможець конкурсу']
  },
  {
    id: '2',
    name: 'DesignGuru',
    avatar: 'https://placehold.co/40x40/png',
    role: 'Дизайнер',
    contributions: 98,
    joined: 'Березень 2021',
    badges: ['Креативний дизайнер', 'Автор місяця']
  },
  {
    id: '3',
    name: 'TechWizard',
    avatar: 'https://placehold.co/40x40/png',
    role: 'Інженер',
    contributions: 132,
    joined: 'Травень 2021',
    badges: ['Технічний експерт', 'Помічник спільноти']
  },
  {
    id: '4',
    name: 'ArtisticSoul',
    avatar: 'https://placehold.co/40x40/png',
    role: 'Художник',
    contributions: 87,
    joined: 'Липень 2021',
    badges: ['Художній майстер', 'Інноватор']
  }
];

export default function CommunityPage() {
  const [activeTab, setActiveTab] = useState('forum');

  return (
    <main className="bg-background min-h-screen">
      <div className="container mx-auto px-4 py-8">
        {/* Заголовок сторінки */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">Спільнота 3D-друку</h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Приєднуйтесь до нашої спільноти ентузіастів 3D-друку, діліться досвідом та знаходьте натхнення
          </p>
        </div>

        {/* Статистика спільноти */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col items-center text-center">
                <Users className="h-10 w-10 text-primary mb-2" />
                <h3 className="text-2xl font-bold">5,240+</h3>
                <p className="text-muted-foreground">Учасників</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col items-center text-center">
                <MessageSquare className="h-10 w-10 text-primary mb-2" />
                <h3 className="text-2xl font-bold">12,800+</h3>
                <p className="text-muted-foreground">Повідомлень</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col items-center text-center">
                <Image className="h-10 w-10 text-primary mb-2" />
                <h3 className="text-2xl font-bold">3,450+</h3>
                <p className="text-muted-foreground">Проектів</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col items-center text-center">
                <Calendar className="h-10 w-10 text-primary mb-2" />
                <h3 className="text-2xl font-bold">120+</h3>
                <p className="text-muted-foreground">Заходів</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Вкладки */}
        <Tabs defaultValue="forum" value={activeTab} onValueChange={setActiveTab} className="mb-12">
          <TabsList className="grid grid-cols-4 max-w-2xl mx-auto">
            <TabsTrigger value="forum">Форум</TabsTrigger>
            <TabsTrigger value="projects">Проекти</TabsTrigger>
            <TabsTrigger value="events">Події</TabsTrigger>
            <TabsTrigger value="members">Учасники</TabsTrigger>
          </TabsList>

          {/* Вкладка "Форум" */}
          <TabsContent value="forum" className="mt-8">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Останні обговорення</h2>
              <div className="flex gap-2">
                <div className="relative w-64">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="Пошук тем..." className="pl-8" />
                </div>
                <Button>Нова тема</Button>
              </div>
            </div>

            <Card>
              <CardContent className="p-0">
                <div className="divide-y divide-border">
                  {FORUM_TOPICS.map((topic) => (
                    <div key={topic.id} className={`p-4 hover:bg-muted/50 ${topic.isSticky ? 'bg-muted/30' : ''}`}>
                      <div className="flex items-start gap-4">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={topic.authorAvatar} alt={topic.author} />
                          <AvatarFallback>{topic.author.substring(0, 2)}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            {topic.isSticky && (
                              <Badge variant="outline" className="text-xs">Закріплено</Badge>
                            )}
                            <Badge variant="secondary" className="text-xs">{topic.category}</Badge>
                          </div>
                          <Link href={`/community/forum/${topic.id}`} className="text-lg font-semibold hover:text-primary truncate block">
                            {topic.title}
                          </Link>
                          <div className="flex items-center text-sm text-muted-foreground mt-1">
                            <span>{topic.author}</span>
                            <span className="mx-2">•</span>
                            <span>{topic.date}</span>
                          </div>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center">
                            <MessageCircle className="h-4 w-4 mr-1" />
                            <span>{topic.replies}</span>
                          </div>
                          <div className="flex items-center">
                            <Eye className="h-4 w-4 mr-1" />
                            <span>{topic.views}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
              <CardFooter className="flex justify-between py-4 border-t">
                <Button variant="outline" size="sm">
                  Попередня
                </Button>
                <div className="flex items-center gap-1">
                  <Button variant="outline" size="sm" className="w-8 h-8 p-0">1</Button>
                  <Button variant="outline" size="sm" className="w-8 h-8 p-0">2</Button>
                  <Button variant="outline" size="sm" className="w-8 h-8 p-0">3</Button>
                  <span className="mx-1">...</span>
                  <Button variant="outline" size="sm" className="w-8 h-8 p-0">10</Button>
                </div>
                <Button variant="outline" size="sm">
                  Наступна
                </Button>
              </CardFooter>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
              <Card>
                <CardHeader>
                  <CardTitle>Популярні категорії</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <Badge variant="outline" className="mr-2">Технічні питання</Badge>
                      <span className="text-sm text-muted-foreground">245 тем</span>
                    </div>
                    <ChevronRight className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <Badge variant="outline" className="mr-2">Моделювання</Badge>
                      <span className="text-sm text-muted-foreground">187 тем</span>
                    </div>
                    <ChevronRight className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <Badge variant="outline" className="mr-2">Матеріали</Badge>
                      <span className="text-sm text-muted-foreground">156 тем</span>
                    </div>
                    <ChevronRight className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <Badge variant="outline" className="mr-2">Поради</Badge>
                      <span className="text-sm text-muted-foreground">132 тем</span>
                    </div>
                    <ChevronRight className="h-4 w-4 text-muted-foreground" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Правила форуму</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <p className="text-sm">1. Будьте ввічливими та поважайте інших учасників.</p>
                  <p className="text-sm">2. Не публікуйте спам або рекламу без дозволу.</p>
                  <p className="text-sm">3. Використовуйте відповідні категорії для своїх тем.</p>
                  <p className="text-sm">4. Не порушуйте авторські права.</p>
                  <p className="text-sm">5. Дотримуйтесь загальних правил спільноти.</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Активні учасники</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {COMMUNITY_MEMBERS.slice(0, 3).map((member) => (
                    <div key={member.id} className="flex items-center gap-2">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={member.avatar} alt={member.name} />
                        <AvatarFallback>{member.name.substring(0, 2)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{member.name}</div>
                        <div className="text-xs text-muted-foreground">{member.contributions} дописів</div>
                      </div>
                      <Badge variant="secondary" className="ml-auto text-xs">{member.role}</Badge>
                    </div>
                  ))}
                </CardContent>
                <CardFooter>
                  <Button variant="ghost" size="sm" className="w-full" onClick={() => setActiveTab('members')}>
                    Переглянути всіх учасників
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </TabsContent>

          {/* Вкладка "Проекти" */}
          <TabsContent value="projects" className="mt-8">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Проекти спільноти</h2>
              <div className="flex gap-2">
                <Button>Поділитися проектом</Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {COMMUNITY_PROJECTS.map((project) => (
                <Card key={project.id} className="overflow-hidden">
                  <div className="aspect-video w-full overflow-hidden">
                    <img 
                      src={project.image} 
                      alt={project.title} 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <CardHeader>
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="secondary">{project.category}</Badge>
                    </div>
                    <CardTitle className="text-xl">{project.title}</CardTitle>
                    <CardDescription>{project.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-2">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={project.authorAvatar} alt={project.author} />
                        <AvatarFallback>{project.author.substring(0, 2)}</AvatarFallback>
                      </Avatar>
                      <span className="text-sm">{project.author}</span>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between border-t pt-4">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center">
                        <Heart className="h-4 w-4 mr-1 text-muted-foreground" />
                        <span className="text-sm text-muted-foreground">{project.likes}</span>
                      </div>
                      <div className="flex items-center">
                        <MessageCircle className="h-4 w-4 mr-1 text-muted-foreground" />
                        <span className="text-sm text-muted-foreground">{project.comments}</span>
                      </div>
                    </div>
                    <div className="flex gap-1">
                      <Button variant="ghost" size="icon">
                        <Bookmark className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon">
                        <Share2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardFooter>
                </Card>
              ))}
            </div>

            <div className="mt-8 text-center">
              <Button variant="outline">Завантажити більше проектів</Button>
            </div>
          </TabsContent>

          {/* Вкладка "Події" */}
          <TabsContent value="events" className="mt-8">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Майбутні події</h2>
              <div className="flex gap-2">
                <Button>Запропонувати подію</Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {UPCOMING_EVENTS.map((event) => (
                <Card key={event.id} className="overflow-hidden">
                  <div className="aspect-video w-full overflow-hidden">
                    <img 
                      src={event.image} 
                      alt={event.title} 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <CardHeader>
                    <CardTitle className="text-xl">{event.title}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-start gap-2">
                      <Calendar className="h-5 w-5 text-muted-foreground flex-shrink-0 mt-0.5" />
                      <div>
                        <div>{event.date}</div>
                        <div className="text-sm text-muted-foreground">{event.time}</div>
                      </div>
                    </div>
                    <div className="flex items-start gap-2">
                      <Users className="h-5 w-5 text-muted-foreground flex-shrink-0 mt-0.5" />
                      <div>
                        <div>{event.attendees} учасників</div>
                        <div className="text-sm text-muted-foreground">Організатор: {event.organizer}</div>
                      </div>
                    </div>
                    <div className="flex items-start gap-2">
                      <MessageSquare className="h-5 w-5 text-muted-foreground flex-shrink-0 mt-0.5" />
                      <div>
                        <div>{event.location}</div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button className="w-full">Зареєструватися</Button>
                  </CardFooter>
                </Card>
              ))}
            </div>

            <Card className="mt-8">
              <CardHeader>
                <CardTitle>Календар подій</CardTitle>
                <CardDescription>Перегляньте всі заплановані події спільноти</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center p-8 border border-dashed rounded-lg">
                  <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">Календар подій</h3>
                  <p className="text-muted-foreground mb-4">Тут буде відображатися повний календар подій спільноти</p>
                  <Button variant="outline">Переглянути календар</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Вкладка "Учасники" */}
          <TabsContent value="members" className="mt-8">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Учасники спільноти</h2>
              <div className="flex gap-2">
                <div className="relative w-64">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="Пошук учасників..." className="pl-8" />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {COMMUNITY_MEMBERS.map((member) => (
                <Card key={member.id}>
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <Avatar className="h-16 w-16">
                        <AvatarImage src={member.avatar} alt={member.name} />
                        <AvatarFallback>{member.name.substring(0, 2)}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="text-lg font-semibold">{member.name}</h3>
                          <Badge variant="secondary">{member.role}</Badge>
                        </div>
                        <div className="text-sm text-muted-foreground mb-2">
                          Приєднався: {member.joined} • {member.contributions} дописів
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {member.badges.map((badge, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              <Award className="h-3 w-3 mr-1" />
                              {badge}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <Button variant="ghost" size="sm">
                        Профіль
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="mt-8 text-center">
              <Button variant="outline">Завантажити більше учасників</Button>
            </div>
          </TabsContent>
        </Tabs>

        {/* Заклик до дії */}
        <div className="mt-16 bg-primary/10 rounded-lg p-8 text-center">
          <h2 className="text-3xl font-bold mb-4">Приєднуйтесь до нашої спільноти!</h2>
          <p className="text-xl text-muted-foreground mb-6 max-w-2xl mx-auto">
            Станьте частиною найбільшої спільноти 3D-друку в Україні. Діліться досвідом, знаходьте однодумців та розвивайтеся разом з нами.
          </p>
          <div className="flex gap-4 justify-center">
            <Button size="lg">
              Зареєструватися
            </Button>
            <Button size="lg" variant="outline">
              Дізнатися більше
            </Button>
          </div>
        </div>
      </div>
    </main>
  );
}
