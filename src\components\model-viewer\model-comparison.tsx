'use client'

import { useState } from 'react'
import { SplineScene } from "@/components/ui/splite"
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { 
  ArrowLeftRight,
  Maximize2,
  Minimize2,
  RotateCw,
  Pause,
  Play,
  Check,
  X
} from "lucide-react"
import { cn } from '@/lib/utils'

interface Model {
  id: string
  name: string
  splineSceneUrl: string
  fileSize: string
  printTime: string
  materialUsage: string
  resolution: string
  supportRequired: boolean
  complexity: 'Low' | 'Medium' | 'High'
  price: number | 'Free'
}

interface ModelComparisonProps {
  modelA: Model
  modelB: Model
  className?: string
}

export function ModelComparison({ 
  modelA, 
  modelB,
  className 
}: ModelComparisonProps) {
  const [syncedRotation, setSyncedRotation] = useState(true);
  const [isRotating, setIsRotating] = useState(true);
  const [syncedZoom, setSyncedZoom] = useState(true);
  const [splineAppA, setSplineAppA] = useState<any>(null);
  const [splineAppB, setSplineAppB] = useState<any>(null);
  
  const handleLoadA = (app: any) => {
    setSplineAppA(app);
    console.log("Model A scene loaded");
  };
  
  const handleLoadB = (app: any) => {
    setSplineAppB(app);
    console.log("Model B scene loaded");
  };
  
  const toggleRotation = () => {
    setIsRotating(!isRotating);
  };
  
  const toggleSyncedRotation = () => {
    setSyncedRotation(!syncedRotation);
  };
  
  const toggleSyncedZoom = () => {
    setSyncedZoom(!syncedZoom);
  };
  
  // Handle synchronized camera movements
  const handleMouseDownA = (e: any) => {
    if (syncedRotation && splineAppB) {
      // Synchronize rotation/movement to model B
      // This is a simplified example - actual implementation would depend on Spline's API
      splineAppB.emitEvent('syncCamera', e.camera);
    }
  };
  
  const handleMouseDownB = (e: any) => {
    if (syncedRotation && splineAppA) {
      // Synchronize rotation/movement to model A
      splineAppA.emitEvent('syncCamera', e.camera);
    }
  };
  
  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>Model Comparison</CardTitle>
          <div className="flex items-center space-x-2">
            <Button 
              variant={syncedRotation ? "default" : "outline"} 
              size="sm"
              onClick={toggleSyncedRotation}
            >
              <ArrowLeftRight className="h-4 w-4 mr-2" />
              Sync Views
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={toggleRotation}
            >
              {isRotating ? <Pause className="h-4 w-4 mr-2" /> : <Play className="h-4 w-4 mr-2" />}
              {isRotating ? "Stop Rotation" : "Start Rotation"}
            </Button>
          </div>
        </div>
        <CardDescription>
          Compare these models side by side to see which one better fits your needs
        </CardDescription>
      </CardHeader>
      
      <CardContent className="p-0">
        <div className="grid grid-cols-2 gap-0 border-t">
          {/* Model A */}
          <div className="border-r">
            <div className="p-4 border-b">
              <h3 className="font-medium text-lg">{modelA.name}</h3>
              <p className="text-sm text-muted-foreground">
                {modelA.price === 'Free' ? 'Free' : `$${modelA.price}`}
              </p>
            </div>
            
            <div className="h-[400px] relative">
              <SplineScene 
                scene={modelA.splineSceneUrl}
                preset="PRODUCT_VIEWER"
                config={{ 
                  backgroundColor: "#f5f5f5",
                  autoRotate: isRotating,
                  enableZoom: true,
                  enablePan: true
                }}
                onLoad={handleLoadA}
                onMouseDown={handleMouseDownA}
              />
              
              {/* Controls overlay */}
              <div className="absolute bottom-4 right-4 flex items-center bg-background/80 backdrop-blur-sm rounded-full px-2 py-1 shadow-md">
                <Button variant="ghost" size="icon">
                  <Maximize2 className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon">
                  <RotateCw className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
          
          {/* Model B */}
          <div>
            <div className="p-4 border-b">
              <h3 className="font-medium text-lg">{modelB.name}</h3>
              <p className="text-sm text-muted-foreground">
                {modelB.price === 'Free' ? 'Free' : `$${modelB.price}`}
              </p>
            </div>
            
            <div className="h-[400px] relative">
              <SplineScene 
                scene={modelB.splineSceneUrl}
                preset="PRODUCT_VIEWER"
                config={{ 
                  backgroundColor: "#f5f5f5",
                  autoRotate: isRotating,
                  enableZoom: true,
                  enablePan: true
                }}
                onLoad={handleLoadB}
                onMouseDown={handleMouseDownB}
              />
              
              {/* Controls overlay */}
              <div className="absolute bottom-4 right-4 flex items-center bg-background/80 backdrop-blur-sm rounded-full px-2 py-1 shadow-md">
                <Button variant="ghost" size="icon">
                  <Maximize2 className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon">
                  <RotateCw className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
        
        <Tabs defaultValue="specs" className="p-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="specs">Specifications</TabsTrigger>
            <TabsTrigger value="printing">Printing Details</TabsTrigger>
            <TabsTrigger value="features">Features</TabsTrigger>
          </TabsList>
          
          <TabsContent value="specs" className="mt-4">
            <div className="grid grid-cols-3 gap-4">
              <div className="font-medium">Specification</div>
              <div className="font-medium">{modelA.name}</div>
              <div className="font-medium">{modelB.name}</div>
              
              <div className="text-muted-foreground">File Size</div>
              <div>{modelA.fileSize}</div>
              <div>{modelB.fileSize}</div>
              
              <div className="text-muted-foreground">Resolution</div>
              <div>{modelA.resolution}</div>
              <div>{modelB.resolution}</div>
              
              <div className="text-muted-foreground">Complexity</div>
              <div>{modelA.complexity}</div>
              <div>{modelB.complexity}</div>
            </div>
          </TabsContent>
          
          <TabsContent value="printing" className="mt-4">
            <div className="grid grid-cols-3 gap-4">
              <div className="font-medium">Print Details</div>
              <div className="font-medium">{modelA.name}</div>
              <div className="font-medium">{modelB.name}</div>
              
              <div className="text-muted-foreground">Print Time</div>
              <div>{modelA.printTime}</div>
              <div>{modelB.printTime}</div>
              
              <div className="text-muted-foreground">Material Usage</div>
              <div>{modelA.materialUsage}</div>
              <div>{modelB.materialUsage}</div>
              
              <div className="text-muted-foreground">Supports Required</div>
              <div>
                {modelA.supportRequired ? 
                  <Check className="h-4 w-4 text-green-500" /> : 
                  <X className="h-4 w-4 text-red-500" />}
              </div>
              <div>
                {modelB.supportRequired ? 
                  <Check className="h-4 w-4 text-green-500" /> : 
                  <X className="h-4 w-4 text-red-500" />}
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="features" className="mt-4">
            <p className="text-muted-foreground mb-4">
              Key differences between these models:
            </p>
            <ul className="space-y-2">
              <li className="flex items-start">
                <Check className="h-4 w-4 text-green-500 mt-1 mr-2" />
                <span>
                  <strong>{modelA.name}</strong> has better detail in small features, while 
                  <strong> {modelB.name}</strong> is optimized for faster printing.
                </span>
              </li>
              <li className="flex items-start">
                <Check className="h-4 w-4 text-green-500 mt-1 mr-2" />
                <span>
                  <strong>{modelA.name}</strong> requires more supports but has more complex geometry.
                </span>
              </li>
              <li className="flex items-start">
                <Check className="h-4 w-4 text-green-500 mt-1 mr-2" />
                <span>
                  <strong>{modelB.name}</strong> uses less material but may have slightly lower resolution in some areas.
                </span>
              </li>
            </ul>
          </TabsContent>
        </Tabs>
        
        <div className="flex justify-between p-6 border-t">
          <Button variant="outline">
            View More Details
          </Button>
          <div className="space-x-2">
            <Button variant="secondary">
              Add Both to Cart
            </Button>
            <Button>
              Choose a Model
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
