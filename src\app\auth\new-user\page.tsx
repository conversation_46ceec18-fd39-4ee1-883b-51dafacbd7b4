'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState, Suspense } from 'react';
import Link from 'next/link';

function NewUserContent() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [countdown, setCountdown] = useState(5);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    }

    if (status === 'authenticated') {
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            router.push('/');
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [status, router]);

  if (status === 'loading') {
    return (
      <main className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </main>
    );
  }

  return (
    <main className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Ласкаво просимо!
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Ваш обліковий запис успішно створено
          </p>
        </div>

        <div className="mt-8 bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            <div className="text-green-500 mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Вітаємо, {session?.user?.name || 'новий користувач'}!</h3>
            <p className="text-sm text-gray-600 mb-6">
              Ваш обліковий запис успішно створено. Тепер ви можете користуватися всіма можливостями нашого сервісу.
            </p>

            <p className="text-xs text-gray-500 mb-6">
              Ви будете автоматично перенаправлені на головну сторінку через {countdown} секунд.
            </p>

            <div className="mt-6 flex justify-center space-x-4">
              <Link href="/" className="text-sm font-medium text-blue-600 hover:text-blue-500">
                Перейти на головну
              </Link>
              <span className="text-gray-300">|</span>
              <Link href="/profile" className="text-sm font-medium text-blue-600 hover:text-blue-500">
                Перейти до профілю
              </Link>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}

export default function NewUser() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    }>
      <NewUserContent />
    </Suspense>
  );
}
