'use client';

import Link from 'next/link';
import { Suspense, useState } from 'react';

function ForgotPasswordContent() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json() as any;

      if (!response.ok) {
        throw new Error(data?.message || 'Помилка при відправці запиту на відновлення пароля');
      }

      setSuccess(true);
    } catch (error) {
      if (error instanceof Error) {
        setError(error.message);
      } else {
        setError('Сталася помилка при відправці запиту на відновлення пароля. Спробуйте ще раз.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <main className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Відновлення пароля
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Введіть вашу електронну пошту, і ми надішлемо вам посилання для відновлення пароля
          </p>
        </div>

        <div className="mt-8 bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {success ? (
            <div className="text-center">
              <div className="text-green-500 mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900">Перевірте вашу електронну пошту</h3>
              <p className="mt-2 text-sm text-gray-600">
                Ми надіслали посилання для відновлення пароля на адресу {email}. Перевірте вашу електронну пошту та перейдіть за посиланням для створення нового пароля.
              </p>
              <div className="mt-6">
                <Link href="/auth/signin" className="text-sm font-medium text-blue-600 hover:text-blue-500">
                  Повернутися до сторінки входу
                </Link>
              </div>
            </div>
          ) : (
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                  Email
                </label>
                <div className="mt-1">
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    value={email}
                    onChange={(e) => {
                      // @ts-ignore - React 19 type issues
                      setEmail(e.currentTarget.value);
                    }}
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    aria-invalid={error ? 'true' : 'false'}
                    aria-describedby={error ? 'email-error' : undefined}
                    disabled={isLoading}
                  />
                </div>
              </div>

              {error && (
                <div className="text-red-500 text-sm mt-2">
                  {error}
                </div>
              )}

              <div>
                <button
                  type="submit"
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={isLoading}
                  aria-label="Відправити посилання для відновлення пароля"
                  tabIndex={0}
                >
                  {isLoading ? 'Відправка...' : 'Відправити посилання для відновлення пароля'}
                </button>
              </div>

              <div className="text-center">
                <Link href="/auth/signin" className="text-sm font-medium text-blue-600 hover:text-blue-500">
                  Повернутися до сторінки входу
                </Link>
              </div>
            </form>
          )}
        </div>
      </div>
    </main>
  );
}

export default function ForgotPassword() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    }>
      <ForgotPasswordContent />
    </Suspense>
  );
}
