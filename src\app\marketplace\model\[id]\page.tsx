'use client';

import SimpleModelDetail from '@/components/marketplace/simple-model-detail';
import { useParams } from 'next/navigation';
import { Model } from '@/types/models';

// Приклад даних моделі
const dummyModel: Model = {
  id: '1',
  title: 'Terminator T800 Armor',
  description: 'Dummy 1:3 Terminator T800 Armor - A highly detailed 3D printable model of the iconic T-800 Terminator endoskeleton armor. Perfect for collectors and fans of the Terminator franchise. This model features intricate details and is designed for easy printing and assembly.',
  price: 0, // Безкоштовна модель
  thumbnail: 'https://images.unsplash.com/photo-1589254065878-42c9da997008?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
  images: [
    'https://images.unsplash.com/photo-1589254065878-42c9da997008?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
    'https://images.unsplash.com/photo-1589254066213-a0c9dc853511?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
  ],
  designer: {
    id: 'cyberdyne-1',
    name: 'Cyberdyne Systems',
    avatar: 'https://images.unsplash.com/photo-1581822261290-991df61a7da2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
    models: 15,
    followers: 1250
  },
  category: 'Figurines & Models',
  likes: 342,
  downloads: 1250,
  tags: ['terminator', 't800', 'armor', 'sci-fi', 'movie', 'figurine'],
  fileFormats: ['STL', 'OBJ'],
  fileSize: '45.2MB',
  printSettings: {
    material: 'PLA',
    layerHeight: '0.2mm',
    infill: '15%',
    supports: 'Yes',
    rafts: 'No',
    printTime: '8h 30m'
  },
  source: 'printables',
  isFree: true,
  downloadUrl: 'https://www.printables.com/model/1286204-dummy-13-terminator-t800-armor/files',
  externalSource: {
    platform: 'printables',
    originalId: '1286204',
    originalUrl: 'https://www.printables.com/model/1286204-dummy-13-terminator-t800-armor',
    importedAt: new Date().toISOString()
  },
  license: {
    type: 'CC-BY',
    name: 'Creative Commons - Attribution',
    allowCommercialUse: true,
    requireAttribution: true,
    allowDerivatives: true
  }
};

export default function ModelDetailPage() {
  // Отримуємо параметри з URL за допомогою хука useParams
  const params = useParams();
  const id = params.id as string;

  // В реальному додатку тут буде запит до API для отримання даних моделі за ID
  // Для прикладу використовуємо фіктивні дані
  const model = dummyModel;

  return (
    <div>
      <SimpleModelDetail model={model} />
    </div>
  );
}
