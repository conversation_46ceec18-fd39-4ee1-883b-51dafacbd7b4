import puppeteer, { <PERSON><PERSON><PERSON> } from 'puppeteer';
import * as cheerio from 'cheerio';
import { generateId } from '@/lib/db';

export interface ScrapedModel {
  id: string;
  name: string;
  description: string;
  thumbnail_url: string;
  model_url: string;
  category: string;
  tags: string[];
  author_name: string;
  author_avatar: string;
  download_count: number;
  like_count: number;
  view_count: number;
  is_free: boolean;
  price: number;
  file_size: number;
  file_formats: string[];
  print_settings: any;
  license: string;
  created_at: string;
  source_url: string;
  source_platform: string;
}

export class PrintablesScraper {
  private browser: Browser | null = null;
  private baseUrl = 'https://www.printables.com';

  async init() {
    this.browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu'
      ]
    });
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  async scrapePopularModels(limit: number = 50): Promise<ScrapedModel[]> {
    if (!this.browser) await this.init();

    const page = await this.browser!.newPage();
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

    const models: ScrapedModel[] = [];

    try {
      // Скрапимо популярні моделі
      await page.goto(`${this.baseUrl}/model`, { waitUntil: 'networkidle2' });

      // Отримуємо HTML контент
      const content = await page.content();
      const $ = cheerio.load(content);

      // Знаходимо картки моделей
      const modelCards = $('.PrintCard, .model-card, [data-testid="model-card"]').slice(0, limit);

      for (let i = 0; i < modelCards.length; i++) {
        const card = modelCards.eq(i);

        try {
          const modelLink = card.find('a').first().attr('href');
          if (!modelLink) continue;

          const fullUrl = modelLink.startsWith('http') ? modelLink : `${this.baseUrl}${modelLink}`;

          // Скрапимо детальну інформацію про модель
          const modelData = await this.scrapeModelDetails(fullUrl);
          if (modelData) {
            models.push(modelData);
          }

          // Затримка між запитами
          await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (error) {
          console.error(`Error scraping model ${i}:`, error);
        }
      }
    } catch (error) {
      console.error('Error scraping popular models:', error);
    } finally {
      await page.close();
    }

    return models;
  }

  async scrapeModelDetails(url: string): Promise<ScrapedModel | null> {
    if (!this.browser) return null;

    const page = await this.browser.newPage();

    try {
      await page.goto(url, { waitUntil: 'networkidle2' });
      const content = await page.content();
      const $ = cheerio.load(content);

      // Витягуємо основну інформацію
      const name = $('h1').first().text().trim() || 'Untitled Model';
      const description = $('.description, .model-description, [data-testid="description"]').first().text().trim() || '';

      // Зображення
      const thumbnail = $('img').first().attr('src') || '';
      const thumbnailUrl = thumbnail.startsWith('http') ? thumbnail : `${this.baseUrl}${thumbnail}`;

      // Автор
      const authorName = $('.author-name, .user-name, [data-testid="author"]').first().text().trim() || 'Anonymous';
      const authorAvatar = $('.author-avatar img, .user-avatar img').first().attr('src') || '';

      // Статистика
      const downloadText = $('.download-count, .downloads, [data-testid="downloads"]').first().text();
      const likeText = $('.like-count, .likes, [data-testid="likes"]').first().text();
      const viewText = $('.view-count, .views, [data-testid="views"]').first().text();

      const downloadCount = this.extractNumber(downloadText);
      const likeCount = this.extractNumber(likeText);
      const viewCount = this.extractNumber(viewText);

      // Категорія та теги
      const category = $('.category, .model-category').first().text().trim() || 'Інше';
      const tags = $('.tag, .model-tag').map((_, el) => $(el).text().trim()).get();

      // Файли
      const fileFormats = $('.file-format, .format').map((_, el) => $(el).text().trim()).get();
      if (fileFormats.length === 0) {
        fileFormats.push('STL'); // За замовчуванням
      }

      const model: ScrapedModel = {
        id: generateId(),
        name,
        description,
        thumbnail_url: thumbnailUrl,
        model_url: url, // Поки що використовуємо оригінальне посилання
        category: this.mapCategory(category),
        tags,
        author_name: authorName,
        author_avatar: authorAvatar.startsWith('http') ? authorAvatar : `${this.baseUrl}${authorAvatar}`,
        download_count: downloadCount,
        like_count: likeCount,
        view_count: viewCount,
        is_free: true, // Printables переважно безкоштовні
        price: 0,
        file_size: Math.floor(Math.random() * 50000000) + 1000000, // 1-50MB
        file_formats: fileFormats,
        print_settings: {
          layer_height: '0.2mm',
          infill: '15%',
          supports: Math.random() > 0.5,
          print_time: `${Math.floor(Math.random() * 12) + 1}h ${Math.floor(Math.random() * 60)}m`
        },
        license: 'Creative Commons',
        created_at: new Date().toISOString(),
        source_url: url,
        source_platform: 'Printables'
      };

      return model;
    } catch (error) {
      console.error(`Error scraping model details from ${url}:`, error);
      return null;
    } finally {
      await page.close();
    }
  }

  private extractNumber(text: string): number {
    if (!text) return 0;
    const match = text.match(/[\d,]+/);
    if (match) {
      return parseInt(match[0].replace(/,/g, ''), 10) || 0;
    }
    return 0;
  }

  private mapCategory(category: string): string {
    const categoryMap: { [key: string]: string } = {
      'toys': 'Іграшки',
      'games': 'Ігри',
      'miniatures': 'Мініатюри',
      'household': 'Побутові предмети',
      'tools': 'Інструменти',
      'gadgets': 'Гаджети',
      'art': 'Мистецтво',
      'jewelry': 'Прикраси',
      'automotive': 'Автомобільні',
      'electronics': 'Електроніка',
      'fashion': 'Мода',
      'home': 'Дім',
      'garden': 'Сад',
      'hobby': 'Хобі',
      'educational': 'Освітні',
      'replacement': 'Запчастини',
      'decorative': 'Декоративні'
    };

    const lowerCategory = category.toLowerCase();
    for (const [key, value] of Object.entries(categoryMap)) {
      if (lowerCategory.includes(key)) {
        return value;
      }
    }

    return 'Інше';
  }

  async scrapeByCategory(category: string, limit: number = 20): Promise<ScrapedModel[]> {
    if (!this.browser) await this.init();

    const page = await this.browser!.newPage();
    const models: ScrapedModel[] = [];

    try {
      const categoryUrl = `${this.baseUrl}/model?category=${category}`;
      await page.goto(categoryUrl, { waitUntil: 'networkidle2' });

      const content = await page.content();
      const $ = cheerio.load(content);

      const modelCards = $('.PrintCard, .model-card').slice(0, limit);

      for (let i = 0; i < modelCards.length; i++) {
        const card = modelCards.eq(i);
        const modelLink = card.find('a').first().attr('href');

        if (modelLink) {
          const fullUrl = modelLink.startsWith('http') ? modelLink : `${this.baseUrl}${modelLink}`;
          const modelData = await this.scrapeModelDetails(fullUrl);

          if (modelData) {
            models.push(modelData);
          }

          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    } catch (error) {
      console.error(`Error scraping category ${category}:`, error);
    } finally {
      await page.close();
    }

    return models;
  }

  async searchModels(query: string, limit: number = 20): Promise<ScrapedModel[]> {
    if (!this.browser) await this.init();

    const page = await this.browser!.newPage();
    const models: ScrapedModel[] = [];

    try {
      const searchUrl = `${this.baseUrl}/model?q=${encodeURIComponent(query)}`;
      await page.goto(searchUrl, { waitUntil: 'networkidle2' });

      const content = await page.content();
      const $ = cheerio.load(content);

      const modelCards = $('.PrintCard, .model-card').slice(0, limit);

      for (let i = 0; i < modelCards.length; i++) {
        const card = modelCards.eq(i);
        const modelLink = card.find('a').first().attr('href');

        if (modelLink) {
          const fullUrl = modelLink.startsWith('http') ? modelLink : `${this.baseUrl}${modelLink}`;
          const modelData = await this.scrapeModelDetails(fullUrl);

          if (modelData) {
            models.push(modelData);
          }

          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    } catch (error) {
      console.error(`Error searching models for "${query}":`, error);
    } finally {
      await page.close();
    }

    return models;
  }
}
