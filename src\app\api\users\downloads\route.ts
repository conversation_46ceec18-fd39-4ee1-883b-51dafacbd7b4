import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { query } from '@/lib/db';

// GET handler for user downloads
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = Number(searchParams.get('page')) || 1;
    const limit = Number(searchParams.get('limit')) || 20;
    const category = searchParams.get('category');
    const sortBy = searchParams.get('sortBy') || 'newest';

    const offset = (page - 1) * limit;

    // Build SQL query to get user's downloaded models
    let sql = `
      SELECT 
        um.id,
        um.model_id,
        um.created_at as download_date,
        m.name as model_name,
        m.description as model_description,
        m.thumbnail_url as model_thumbnail,
        m.model_url,
        m.price as model_price,
        m.category as model_category,
        m.additional_files,
        u.name as author_name,
        u.avatar_url as author_avatar
      FROM user_models um
      JOIN models m ON um.model_id = m.id
      JOIN users u ON m.user_id = u.id
      WHERE um.user_id = ?
    `;

    const queryParams: any[] = [session.user.id];

    // Filter by category
    if (category) {
      sql += ` AND m.category = ?`;
      queryParams.push(category);
    }

    // Sorting
    switch (sortBy) {
      case 'newest':
        sql += ` ORDER BY um.created_at DESC`;
        break;
      case 'oldest':
        sql += ` ORDER BY um.created_at ASC`;
        break;
      case 'name':
        sql += ` ORDER BY m.name ASC`;
        break;
      case 'author':
        sql += ` ORDER BY u.name ASC`;
        break;
      case 'price_desc':
        sql += ` ORDER BY m.price DESC`;
        break;
      case 'price_asc':
        sql += ` ORDER BY m.price ASC`;
        break;
      default:
        sql += ` ORDER BY um.created_at DESC`;
    }

    // Pagination
    sql += ` LIMIT ? OFFSET ?`;
    queryParams.push(limit, offset);

    // Execute query
    const downloads = await query(sql, queryParams);

    // Get total count for pagination
    const countSql = `
      SELECT COUNT(*) as total
      FROM user_models um
      JOIN models m ON um.model_id = m.id
      WHERE um.user_id = ?
      ${category ? 'AND m.category = ?' : ''}
    `;

    const countParams = category ? [session.user.id, category] : [session.user.id];
    const totalResult = await query(countSql, countParams);
    const total = totalResult[0]?.total || 0;

    // Process downloads data
    const processedDownloads = downloads.map((download: any) => ({
      ...download,
      additional_files: download.additional_files ? JSON.parse(download.additional_files) : [],
    }));

    // Get download statistics
    const statsResult = await query(`
      SELECT 
        COUNT(*) as total_downloads,
        COUNT(CASE WHEN m.price > 0 THEN 1 END) as paid_downloads,
        COUNT(CASE WHEN m.price = 0 THEN 1 END) as free_downloads,
        SUM(CASE WHEN m.price > 0 THEN m.price ELSE 0 END) as total_spent
      FROM user_models um
      JOIN models m ON um.model_id = m.id
      WHERE um.user_id = ?
    `, [session.user.id]);

    const stats = statsResult[0] || {
      total_downloads: 0,
      paid_downloads: 0,
      free_downloads: 0,
      total_spent: 0
    };

    return NextResponse.json({
      success: true,
      data: processedDownloads,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      },
      stats
    });

  } catch (error) {
    console.error('Error fetching user downloads:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch downloads' },
      { status: 500 }
    );
  }
}
