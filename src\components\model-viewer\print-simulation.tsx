'use client'

import { useState, useEffect } from 'react'
import { SplineScene } from "@/components/ui/splite"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>lider } from "@/components/ui/slider"
import { 
  Play, 
  Pause, 
  SkipForward, 
  SkipB<PERSON>,
  Settings,
  Layers
} from "lucide-react"
import { cn } from '@/lib/utils'

interface PrintSimulationProps {
  modelName: string
  splineSceneUrl: string
  estimatedPrintTime: string
  className?: string
}

export function PrintSimulation({ 
  modelName, 
  splineSceneUrl, 
  estimatedPrintTime,
  className 
}: PrintSimulationProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentLayer, setCurrentLayer] = useState(0);
  const [totalLayers, setTotalLayers] = useState(100); // This would come from the model data
  const [splineApp, setSplineApp] = useState<any>(null);
  const [printSpeed, setPrintSpeed] = useState(1);
  
  const handleSceneLoad = (app: any) => {
    setSplineApp(app);
    console.log("Print simulation scene loaded");
  };
  
  // Control the print simulation
  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
  };
  
  const skipToStart = () => {
    setCurrentLayer(0);
    updateSplineScene(0);
  };
  
  const skipToEnd = () => {
    setCurrentLayer(totalLayers);
    updateSplineScene(totalLayers);
  };
  
  const handleLayerChange = (value: number[]) => {
    const newLayer = value[0];
    setCurrentLayer(newLayer);
    updateSplineScene(newLayer);
  };
  
  const updateSplineScene = (layerNumber: number) => {
    if (!splineApp) return;
    
    try {
      // This assumes your Spline scene has a timeline or events to control layer visibility
      // The actual implementation will depend on how your Spline scene is structured
      splineApp.setLayerVisibility(layerNumber);
      
      // Or you could trigger a specific event in your Spline scene
      // splineApp.emitEvent('setLayer', { layer: layerNumber });
    } catch (error) {
      console.error("Error updating print simulation in Spline scene:", error);
    }
  };
  
  // Advance the simulation when playing
  useEffect(() => {
    if (!isPlaying || currentLayer >= totalLayers) return;
    
    const interval = setInterval(() => {
      setCurrentLayer(prev => {
        const next = prev + 1;
        if (next > totalLayers) {
          setIsPlaying(false);
          return totalLayers;
        }
        updateSplineScene(next);
        return next;
      });
    }, 1000 / printSpeed); // Adjust speed based on printSpeed
    
    return () => clearInterval(interval);
  }, [isPlaying, currentLayer, totalLayers, printSpeed, splineApp]);
  
  // Calculate progress percentage
  const progressPercentage = (currentLayer / totalLayers) * 100;
  
  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle>Print Simulation: {modelName}</CardTitle>
        <CardDescription>
          See how your model will print layer by layer. Estimated print time: {estimatedPrintTime}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        <div className="h-[400px] rounded-lg overflow-hidden">
          <SplineScene 
            scene={splineSceneUrl}
            preset="PRINT_SIMULATION"
            onLoad={handleSceneLoad}
          />
        </div>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Layers className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">
                Layer: {currentLayer} / {totalLayers}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">Speed:</span>
              <select 
                className="text-sm bg-secondary rounded px-2 py-1"
                value={printSpeed}
                onChange={(e) => setPrintSpeed(Number(e.target.value))}
              >
                <option value="0.5">0.5x</option>
                <option value="1">1x</option>
                <option value="2">2x</option>
                <option value="4">4x</option>
              </select>
            </div>
          </div>
          
          <Slider
            value={[currentLayer]}
            min={0}
            max={totalLayers}
            step={1}
            onValueChange={handleLayerChange}
          />
          
          <div className="flex items-center justify-center space-x-2">
            <Button variant="outline" size="icon" onClick={skipToStart}>
              <SkipBack className="h-4 w-4" />
            </Button>
            <Button 
              variant={isPlaying ? "secondary" : "default"} 
              size="icon"
              onClick={togglePlayPause}
            >
              {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </Button>
            <Button variant="outline" size="icon" onClick={skipToEnd}>
              <SkipForward className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>0:00</span>
            <span>{estimatedPrintTime}</span>
          </div>
        </div>
        
        <div className="bg-secondary/30 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <Settings className="h-5 w-5 text-primary mt-0.5" />
            <div>
              <h3 className="font-medium">Print Settings</h3>
              <div className="mt-2 grid grid-cols-2 gap-x-8 gap-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Layer Height:</span>
                  <span>0.2mm</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Infill:</span>
                  <span>20%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Print Speed:</span>
                  <span>50mm/s</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Temperature:</span>
                  <span>210°C</span>
                </div>
              </div>
              <Button variant="outline" size="sm" className="mt-3">
                Adjust Settings
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
