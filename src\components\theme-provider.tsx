"use client"

import * as React from "react"
import { ThemeProvider as NextThemesProvider } from "next-themes"
import type { ThemeProviderProps as NextThemeProviderProps } from "next-themes"

export interface ThemeProviderProps extends React.PropsWithChildren<Omit<NextThemeProviderProps, "children">> {}

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return <NextThemesProvider {...props}>{children}</NextThemesProvider>
}

// Додаємо дефолтний експорт для динамічного імпорту
export default { ThemeProvider }
