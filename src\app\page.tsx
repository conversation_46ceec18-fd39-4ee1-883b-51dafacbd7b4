'use client';

import { COLLECTIONS, POPULAR_TAGS } from './constants/images';
import HeroSection from '@/components/hero-section';
import FeaturedModels from '@/components/featured-models';
import CategoriesDemo from '@/components/categories-demo';
import HowItWorks from '@/components/how-it-works';
import JoinCommunity from '@/components/join-community';
import CollectionsSection from '@/components/collections-section';
import PopularTags from '@/components/popular-tags';
import RecommendedModels from '@/components/marketplace/recommended-models';

export default function Home() {
  return (
    <main className="flex min-h-screen flex-col bg-background">
      {/* Hero Section */}
      <HeroSection />

      {/* Popular Tags Section */}
      <PopularTags tags={POPULAR_TAGS} />

      {/* Featured Models Section */}
      <FeaturedModels />

      {/* Recommended Models Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <RecommendedModels
            title="Популярні моделі"
            subtitle="Найкращі моделі від нашої спільноти"
            limit={8}
          />
        </div>
      </section>

      {/* Categories Section */}
      <CategoriesDemo />

      {/* Collections Section */}
      <CollectionsSection collections={COLLECTIONS} />

      {/* How It Works Section */}
      <HowItWorks />

      {/* Join Community Section */}
      <JoinCommunity />
    </main>
  );
}
