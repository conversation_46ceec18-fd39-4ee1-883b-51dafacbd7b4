'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Suspense } from 'react';

function AuthErrorContent() {
  const searchParams = useSearchParams();
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const errorParam = searchParams.get('error');

    if (errorParam) {
      switch (errorParam) {
        case 'Configuration':
          setError('Виникла проблема з конфігурацією сервера. Будь ласка, зв\'яжіться з адміністратором.');
          break;
        case 'AccessDenied':
          setError('Доступ заборонено. У вас немає дозволу на вхід.');
          break;
        case 'Verification':
          setError('Не вдалося завершити процес входу. Спробуйте ще раз пізніше.');
          break;
        case 'OAuthSignin':
          setError('Помилка при спробі входу через OAuth провайдера.');
          break;
        case 'OAuthCallback':
          setError('Помилка при отриманні відповіді від OAuth провайдера.');
          break;
        case 'OAuthCreateAccount':
          setError('Не вдалося створити обліковий запис через OAuth провайдера.');
          break;
        case 'EmailCreateAccount':
          setError('Не вдалося створити обліковий запис з використанням електронної пошти.');
          break;
        case 'Callback':
          setError('Помилка при обробці відповіді від провайдера автентифікації.');
          break;
        case 'OAuthAccountNotLinked':
          setError('Електронна пошта вже використовується з іншим обліковим записом.');
          break;
        case 'EmailSignin':
          setError('Не вдалося надіслати електронний лист для входу.');
          break;
        case 'CredentialsSignin':
          setError('Невірний email або пароль.');
          break;
        case 'SessionRequired':
          setError('Для доступу до цієї сторінки потрібно увійти в систему.');
          break;
        default:
          setError('Сталася невідома помилка при автентифікації.');
          break;
      }
    } else {
      setError('Сталася невідома помилка при автентифікації.');
    }
  }, [searchParams]);

  return (
    <main className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Помилка автентифікації
          </h2>
        </div>

        <div className="mt-8 bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            <div className="text-red-500 mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900">Не вдалося виконати вхід</h3>
            <p className="mt-2 text-sm text-gray-600">
              {error}
            </p>
            <div className="mt-6 flex justify-center space-x-4">
              <Link href="/auth/signin" className="text-sm font-medium text-blue-600 hover:text-blue-500">
                Спробувати ще раз
              </Link>
              <span className="text-gray-300">|</span>
              <Link href="/" className="text-sm font-medium text-blue-600 hover:text-blue-500">
                На головну
              </Link>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}

export default function AuthError() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    }>
      <AuthErrorContent />
    </Suspense>
  );
}
