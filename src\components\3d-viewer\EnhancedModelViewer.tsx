'use client';

import { useEffect, useRef, useState, Suspense } from 'react';
import { Canvas, useThree, useFrame } from '@react-three/fiber';
import { 
  OrbitControls, 
  useGLTF, 
  Environment, 
  Stage, 
  useProgress,
  Html,
  ContactShadows,
  useAnimations,
  Stats
} from '@react-three/drei';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { 
  Play, 
  Pause, 
  RotateCcw, 
  Maximize, 
  Settings,
  Download,
  Share2,
  Eye,
  EyeOff
} from 'lucide-react';
import * as THREE from 'three';

interface EnhancedModelViewerProps {
  modelUrl: string;
  backgroundColor?: string;
  autoRotate?: boolean;
  showControls?: boolean;
  showStats?: boolean;
  enableAnimations?: boolean;
  enableWireframe?: boolean;
  enableFullscreen?: boolean;
  onModelLoad?: (model: any) => void;
  onError?: (error: Error) => void;
}

interface ViewerSettings {
  autoRotate: boolean;
  rotationSpeed: number;
  wireframe: boolean;
  showStats: boolean;
  environmentPreset: string;
  lightIntensity: number;
  cameraPosition: [number, number, number];
}

function LoadingProgress() {
  const { progress } = useProgress();
  return (
    <Html center>
      <div className="text-center bg-white/90 backdrop-blur-sm rounded-lg p-4 shadow-lg">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
        <p className="text-sm text-gray-600">Завантаження: {Math.round(progress)}%</p>
      </div>
    </Html>
  );
}

function Model({ 
  url, 
  wireframe, 
  onLoad, 
  onError 
}: { 
  url: string; 
  wireframe: boolean;
  onLoad?: (model: any) => void;
  onError?: (error: Error) => void;
}) {
  const { scene, animations } = useGLTF(url);
  const { actions } = useAnimations(animations, scene);
  const [modelLoaded, setModelLoaded] = useState(false);

  useEffect(() => {
    if (scene && !modelLoaded) {
      setModelLoaded(true);
      onLoad?.(scene);
      
      // Start animations if available
      Object.values(actions).forEach(action => {
        action?.play();
      });
    }
  }, [scene, actions, modelLoaded, onLoad]);

  useEffect(() => {
    // Apply wireframe mode
    scene.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach(mat => {
              mat.wireframe = wireframe;
            });
          } else {
            child.material.wireframe = wireframe;
          }
        }
      }
    });
  }, [scene, wireframe]);

  return <primitive object={scene} />;
}

function CameraController({ 
  position, 
  onPositionChange 
}: { 
  position: [number, number, number];
  onPositionChange?: (position: [number, number, number]) => void;
}) {
  const { camera } = useThree();
  const controlsRef = useRef<any>();

  useEffect(() => {
    camera.position.set(...position);
  }, [camera, position]);

  useFrame(() => {
    if (controlsRef.current && onPositionChange) {
      const pos = camera.position;
      onPositionChange([pos.x, pos.y, pos.z]);
    }
  });

  return <OrbitControls ref={controlsRef} makeDefault />;
}

export default function EnhancedModelViewer({
  modelUrl,
  backgroundColor = '#f3f4f6',
  autoRotate = true,
  showControls = true,
  showStats = false,
  enableAnimations = true,
  enableWireframe = false,
  enableFullscreen = true,
  onModelLoad,
  onError,
}: EnhancedModelViewerProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [settings, setSettings] = useState<ViewerSettings>({
    autoRotate,
    rotationSpeed: 1,
    wireframe: enableWireframe,
    showStats,
    environmentPreset: 'sunset',
    lightIntensity: 0.6,
    cameraPosition: [0, 0, 5]
  });

  const containerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const environmentPresets = [
    'sunset', 'dawn', 'night', 'warehouse', 'forest', 'apartment', 'studio', 'city', 'park', 'lobby'
  ];

  const handleModelLoad = (model: any) => {
    setLoading(false);
    setError(null);
    onModelLoad?.(model);
  };

  const handleModelError = (error: Error) => {
    setLoading(false);
    setError(error.message);
    onError?.(error);
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement && containerRef.current) {
      containerRef.current.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  const resetCamera = () => {
    setSettings(prev => ({
      ...prev,
      cameraPosition: [0, 0, 5]
    }));
  };

  const downloadModel = () => {
    const link = document.createElement('a');
    link.href = modelUrl;
    link.download = 'model.glb';
    link.click();
  };

  const shareModel = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: '3D Model',
          url: window.location.href
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      navigator.clipboard.writeText(window.location.href);
    }
  };

  return (
    <div 
      ref={containerRef}
      className={`relative w-full rounded-lg overflow-hidden ${
        isFullscreen ? 'h-screen' : 'h-96'
      }`}
      style={{ backgroundColor }}
    >
      {/* Loading State */}
      {loading && !error && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Завантаження 3D моделі...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-red-50 z-10">
          <div className="text-center p-6">
            <div className="text-red-500 text-4xl mb-4">⚠️</div>
            <h3 className="text-lg font-semibold text-red-800 mb-2">Помилка завантаження</h3>
            <p className="text-red-600 mb-4">{error}</p>
            <Button 
              onClick={() => window.location.reload()} 
              variant="outline"
              className="border-red-300 text-red-700 hover:bg-red-50"
            >
              Спробувати знову
            </Button>
          </div>
        </div>
      )}

      {/* 3D Canvas */}
      <Canvas
        ref={canvasRef}
        camera={{ position: settings.cameraPosition, fov: 45 }}
        onCreated={({ gl }) => {
          gl.setClearColor(backgroundColor);
        }}
      >
        <Suspense fallback={<LoadingProgress />}>
          {/* Lighting */}
          <ambientLight intensity={settings.lightIntensity * 0.4} />
          <directionalLight 
            position={[10, 10, 5]} 
            intensity={settings.lightIntensity} 
            castShadow
          />
          <pointLight position={[-10, -10, -10]} intensity={settings.lightIntensity * 0.3} />

          {/* Environment */}
          <Environment preset={settings.environmentPreset as any} />
          
          {/* Model */}
          <Stage environment={null} intensity={settings.lightIntensity}>
            <Model 
              url={modelUrl} 
              wireframe={settings.wireframe}
              onLoad={handleModelLoad}
              onError={handleModelError}
            />
          </Stage>

          {/* Shadows */}
          <ContactShadows 
            position={[0, -1, 0]} 
            opacity={0.4} 
            scale={10} 
            blur={2} 
            far={4} 
          />

          {/* Camera Controls */}
          <CameraController 
            position={settings.cameraPosition}
            onPositionChange={(pos) => setSettings(prev => ({ ...prev, cameraPosition: pos }))}
          />
          
          <OrbitControls 
            autoRotate={settings.autoRotate}
            autoRotateSpeed={settings.rotationSpeed}
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            makeDefault
          />

          {/* Stats */}
          {settings.showStats && <Stats />}
        </Suspense>
      </Canvas>

      {/* Control Panel */}
      {showControls && !loading && !error && (
        <>
          {/* Main Controls */}
          <div className="absolute top-4 left-4 flex flex-col gap-2">
            <Button
              size="sm"
              variant="secondary"
              onClick={() => setSettings(prev => ({ ...prev, autoRotate: !prev.autoRotate }))}
              className="bg-white/90 backdrop-blur-sm"
            >
              {settings.autoRotate ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            </Button>
            
            <Button
              size="sm"
              variant="secondary"
              onClick={resetCamera}
              className="bg-white/90 backdrop-blur-sm"
            >
              <RotateCcw className="w-4 h-4" />
            </Button>

            <Button
              size="sm"
              variant="secondary"
              onClick={() => setSettings(prev => ({ ...prev, wireframe: !prev.wireframe }))}
              className="bg-white/90 backdrop-blur-sm"
            >
              {settings.wireframe ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
            </Button>
          </div>

          {/* Top Right Controls */}
          <div className="absolute top-4 right-4 flex gap-2">
            <Button
              size="sm"
              variant="secondary"
              onClick={downloadModel}
              className="bg-white/90 backdrop-blur-sm"
            >
              <Download className="w-4 h-4" />
            </Button>

            <Button
              size="sm"
              variant="secondary"
              onClick={shareModel}
              className="bg-white/90 backdrop-blur-sm"
            >
              <Share2 className="w-4 h-4" />
            </Button>

            <Button
              size="sm"
              variant="secondary"
              onClick={() => setShowSettings(!showSettings)}
              className="bg-white/90 backdrop-blur-sm"
            >
              <Settings className="w-4 h-4" />
            </Button>

            {enableFullscreen && (
              <Button
                size="sm"
                variant="secondary"
                onClick={toggleFullscreen}
                className="bg-white/90 backdrop-blur-sm"
              >
                <Maximize className="w-4 h-4" />
              </Button>
            )}
          </div>

          {/* Settings Panel */}
          {showSettings && (
            <Card className="absolute top-16 right-4 p-4 bg-white/95 backdrop-blur-sm w-64 max-h-96 overflow-y-auto">
              <h3 className="font-semibold mb-3">Налаштування в'юера</h3>

              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Швидкість обертання</label>
                  <Slider
                    value={[settings.rotationSpeed]}
                    onValueChange={([value]) => setSettings(prev => ({ ...prev, rotationSpeed: value }))}
                    max={5}
                    min={0.1}
                    step={0.1}
                    className="mt-1"
                  />
                  <span className="text-xs text-gray-500">{settings.rotationSpeed.toFixed(1)}</span>
                </div>

                <div>
                  <label className="text-sm font-medium">Інтенсивність освітлення</label>
                  <Slider
                    value={[settings.lightIntensity]}
                    onValueChange={([value]) => setSettings(prev => ({ ...prev, lightIntensity: value }))}
                    max={2}
                    min={0.1}
                    step={0.1}
                    className="mt-1"
                  />
                  <span className="text-xs text-gray-500">{settings.lightIntensity.toFixed(1)}</span>
                </div>

                <div>
                  <label className="text-sm font-medium">Середовище</label>
                  <select
                    value={settings.environmentPreset}
                    onChange={(e) => setSettings(prev => ({ ...prev, environmentPreset: e.target.value }))}
                    className="w-full mt-1 p-2 border rounded text-sm bg-white"
                  >
                    {environmentPresets.map(preset => (
                      <option key={preset} value={preset}>
                        {preset.charAt(0).toUpperCase() + preset.slice(1)}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={settings.showStats}
                    onChange={(e) => setSettings(prev => ({ ...prev, showStats: e.target.checked }))}
                    id="showStats"
                    className="rounded"
                  />
                  <label htmlFor="showStats" className="text-sm">Показати статистику</label>
                </div>

                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={settings.wireframe}
                    onChange={(e) => setSettings(prev => ({ ...prev, wireframe: e.target.checked }))}
                    id="wireframeMode"
                    className="rounded"
                  />
                  <label htmlFor="wireframeMode" className="text-sm">Каркасний режим</label>
                </div>

                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={settings.autoRotate}
                    onChange={(e) => setSettings(prev => ({ ...prev, autoRotate: e.target.checked }))}
                    id="autoRotate"
                    className="rounded"
                  />
                  <label htmlFor="autoRotate" className="text-sm">Автообертання</label>
                </div>

                <div className="pt-2 border-t">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      setSettings({
                        autoRotate: true,
                        rotationSpeed: 1,
                        wireframe: false,
                        showStats: false,
                        environmentPreset: 'sunset',
                        lightIntensity: 0.6,
                        cameraPosition: [0, 0, 5]
                      });
                    }}
                    className="w-full"
                  >
                    Скинути налаштування
                  </Button>
                </div>
              </div>
            </Card>
          )}

          {/* Help Text */}
          <div className="absolute bottom-4 right-4 bg-white/75 backdrop-blur-sm rounded-lg p-2 text-xs text-gray-700">
            <p>ЛКМ + перетягування: Обертання</p>
            <p>ПКМ + перетягування: Панорамування</p>
            <p>Колесо миші: Масштабування</p>
          </div>
        </>
      )}
    </div>
  );
}
