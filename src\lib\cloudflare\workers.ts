/**
 * Утиліти для роботи з Cloudflare Workers
 */

// Інтерфейс для Cloudflare Worker
export interface Worker {
  fetch: (request: Request) => Promise<Response>;
}

// Інтерфейс для опцій запиту до Worker
export interface WorkerRequestOptions {
  method?: string;
  headers?: HeadersInit;
  body?: BodyInit;
}

// Глобальна змінна для зберігання екземпляра Worker
let worker: Worker | null = null;

/**
 * Отримує екземпляр Worker
 * @returns Екземпляр Worker
 */
export function getWorker(): Worker {
  if (!worker) {
    // В середовищі Cloudflare Pages, Worker доступний через env.WORKER
    if (process.env.WORKER) {
      worker = process.env.WORKER as unknown as Worker;
    } else if (globalThis.WORKER) {
      // Для серверних компонентів Next.js
      worker = globalThis.WORKER as unknown as Worker;
    } else {
      throw new Error('Worker недоступний. Переконайтеся, що ви налаштували Worker в wrangler.toml і запустили проект з прапорцем --service=WORKER');
    }
  }
  return worker;
}

/**
 * Виконує запит до Worker
 * @param path Шлях запиту
 * @param options Опції запиту
 * @returns Відповідь від Worker
 */
export async function fetchWorker(
  path: string,
  options?: WorkerRequestOptions
): Promise<Response> {
  const worker = getWorker();
  
  // Створення URL для запиту
  const url = new URL(path, 'http://worker');
  
  // Створення запиту
  const request = new Request(url.toString(), {
    method: options?.method || 'GET',
    headers: options?.headers,
    body: options?.body,
  });
  
  // Виконання запиту
  return await worker.fetch(request);
}

/**
 * Виконує GET-запит до Worker
 * @param path Шлях запиту
 * @param headers Заголовки запиту
 * @returns Відповідь від Worker
 */
export async function getWorkerData(
  path: string,
  headers?: HeadersInit
): Promise<Response> {
  return await fetchWorker(path, { method: 'GET', headers });
}

/**
 * Виконує POST-запит до Worker
 * @param path Шлях запиту
 * @param body Тіло запиту
 * @param headers Заголовки запиту
 * @returns Відповідь від Worker
 */
export async function postWorkerData(
  path: string,
  body?: BodyInit,
  headers?: HeadersInit
): Promise<Response> {
  return await fetchWorker(path, { method: 'POST', body, headers });
}

/**
 * Виконує PUT-запит до Worker
 * @param path Шлях запиту
 * @param body Тіло запиту
 * @param headers Заголовки запиту
 * @returns Відповідь від Worker
 */
export async function putWorkerData(
  path: string,
  body?: BodyInit,
  headers?: HeadersInit
): Promise<Response> {
  return await fetchWorker(path, { method: 'PUT', body, headers });
}

/**
 * Виконує DELETE-запит до Worker
 * @param path Шлях запиту
 * @param headers Заголовки запиту
 * @returns Відповідь від Worker
 */
export async function deleteWorkerData(
  path: string,
  headers?: HeadersInit
): Promise<Response> {
  return await fetchWorker(path, { method: 'DELETE', headers });
}

/**
 * Виконує запит до Worker і повертає результат як JSON
 * @param path Шлях запиту
 * @param options Опції запиту
 * @returns Результат запиту як JSON
 */
export async function fetchWorkerJSON<T = any>(
  path: string,
  options?: WorkerRequestOptions
): Promise<T> {
  const response = await fetchWorker(path, options);
  
  if (!response.ok) {
    throw new Error(`Worker request failed: ${response.status} ${response.statusText}`);
  }
  
  return await response.json() as T;
}
