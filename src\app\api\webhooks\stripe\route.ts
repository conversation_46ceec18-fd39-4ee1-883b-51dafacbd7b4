import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import Strip<PERSON> from 'stripe';
import { queryOne, execute, generateId } from '@/lib/db';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!;

export async function POST(request: NextRequest) {
  const body = await request.text();
  const headersList = headers();
  const sig = headersList.get('stripe-signature');

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(body, sig!, endpointSecret);
  } catch (err) {
    console.error('Webhook signature verification failed:', err);
    return NextResponse.json(
      { error: 'Webhook signature verification failed' },
      { status: 400 }
    );
  }

  try {
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.PaymentIntent);
        break;
      
      case 'payment_intent.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.PaymentIntent);
        break;
      
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Error processing webhook:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}

async function handlePaymentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  console.log('Payment succeeded:', paymentIntent.id);

  try {
    // Find the order
    const order = await queryOne(
      'SELECT * FROM orders WHERE payment_intent_id = ?',
      [paymentIntent.id]
    );

    if (!order) {
      console.error('Order not found for payment intent:', paymentIntent.id);
      return;
    }

    // Update order status
    await execute(
      'UPDATE orders SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      ['completed', (order as any).id]
    );

    // Get order items
    const orderItems = await queryOne(
      'SELECT * FROM order_items WHERE order_id = ?',
      [(order as any).id]
    );

    if (!orderItems) {
      console.error('Order items not found for order:', (order as any).id);
      return;
    }

    // Grant access to models
    const items = Array.isArray(orderItems) ? orderItems : [orderItems];
    
    for (const item of items) {
      const userModelId = generateId();
      
      // Check if user already has access (prevent duplicates)
      const existingAccess = await queryOne(
        'SELECT * FROM user_models WHERE user_id = ? AND model_id = ?',
        [(order as any).user_id, (item as any).model_id]
      );

      if (!existingAccess) {
        // Grant access to the model
        await execute(
          'INSERT INTO user_models (id, user_id, model_id, created_at) VALUES (?, ?, ?, CURRENT_TIMESTAMP)',
          [userModelId, (order as any).user_id, (item as any).model_id]
        );

        // Update model download count
        await execute(
          'UPDATE models SET download_count = download_count + ? WHERE id = ?',
          [(item as any).quantity, (item as any).model_id]
        );

        // Update seller earnings (if needed)
        const model = await queryOne(
          'SELECT user_id, price FROM models WHERE id = ?',
          [(item as any).model_id]
        );

        if (model) {
          const earnings = (model as any).price * (item as any).quantity * 0.85; // 85% to seller, 15% platform fee
          
          await execute(
            'UPDATE users SET total_earnings = COALESCE(total_earnings, 0) + ? WHERE id = ?',
            [earnings, (model as any).user_id]
          );
        }
      }
    }

    console.log('Payment processing completed for order:', (order as any).id);

  } catch (error) {
    console.error('Error handling payment success:', error);
    throw error;
  }
}

async function handlePaymentFailed(paymentIntent: Stripe.PaymentIntent) {
  console.log('Payment failed:', paymentIntent.id);

  try {
    // Find and update the order
    const order = await queryOne(
      'SELECT * FROM orders WHERE payment_intent_id = ?',
      [paymentIntent.id]
    );

    if (order) {
      await execute(
        'UPDATE orders SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        ['failed', (order as any).id]
      );
    }

    console.log('Payment failure processed for order:', order ? (order as any).id : 'not found');

  } catch (error) {
    console.error('Error handling payment failure:', error);
    throw error;
  }
}
