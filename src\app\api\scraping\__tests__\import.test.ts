/**
 * Tests for import API endpoint
 */

import { NextRequest } from 'next/server';
import { POST, GET } from '../import/route';

// Mock the scrapers
jest.mock('@/lib/api/printables', () => ({
  printablesScraper: {
    validateUrl: jest.fn(),
    scrapeModel: jest.fn(),
  },
}));

jest.mock('@/lib/api/makerworld', () => ({
  makerWorldScraper: {
    validateUrl: jest.fn(),
    scrapeModel: jest.fn(),
  },
}));

jest.mock('@/lib/api/thangs', () => ({
  thangsScraper: {
    validateUrl: jest.fn(),
    scrapeModel: jest.fn(),
  },
}));

jest.mock('@/lib/scraping/rate-limiter', () => ({
  platformRateLimiters: {
    consume: jest.fn(),
    getAllRemainingPoints: jest.fn(),
  },
  RateLimitError: class RateLimitError extends Error {
    rateLimitInfo: any;
    constructor(rateLimitInfo: any) {
      super('Rate limit exceeded');
      this.rateLimitInfo = rateLimitInfo;
    }
  },
}));

jest.mock('@/lib/scraping/data-normalizer', () => ({
  DataNormalizer: {
    validateScrapedModel: jest.fn(),
    sanitizeScrapedModel: jest.fn(),
    convertToInternalModel: jest.fn(),
  },
}));

import { printablesScraper } from '@/lib/api/printables';
import { makerWorldScraper } from '@/lib/api/makerworld';
import { thangsScraper } from '@/lib/api/thangs';
import { platformRateLimiters, RateLimitError } from '@/lib/scraping/rate-limiter';
import { DataNormalizer } from '@/lib/scraping/data-normalizer';

// Helper function to create requests
const createRequest = (body: any) => {
  return new NextRequest('http://localhost/api/scraping/import', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(body),
  });
};

describe('/api/scraping/import', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST', () => {

    it('should successfully import from Printables', async () => {
      const mockScrapedModel = {
        title: 'Test Model',
        description: 'Test description',
        platform: 'printables',
        originalId: '123',
        originalUrl: 'https://www.printables.com/model/123',
        designer: { id: 'test', name: 'Test Designer' },
        // ... other required fields
      };

      const mockInternalModel = {
        id: 'printables_123_1234567890',
        title: 'Test Model',
        // ... other fields
      };

      // Setup mocks
      (printablesScraper.validateUrl as jest.Mock).mockReturnValue(true);
      (makerWorldScraper.validateUrl as jest.Mock).mockReturnValue(false);
      (thangsScraper.validateUrl as jest.Mock).mockReturnValue(false);
      (platformRateLimiters.consume as jest.Mock).mockResolvedValue({});
      (printablesScraper.scrapeModel as jest.Mock).mockResolvedValue(mockScrapedModel);
      (DataNormalizer.validateScrapedModel as jest.Mock).mockReturnValue({ isValid: true, errors: [] });
      (DataNormalizer.sanitizeScrapedModel as jest.Mock).mockReturnValue(mockScrapedModel);
      (DataNormalizer.convertToInternalModel as jest.Mock).mockReturnValue(mockInternalModel);

      const request = createRequest({
        url: 'https://www.printables.com/model/123',
        options: { includeFiles: true },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.modelId).toBe('printables_123_1234567890');
      expect(data.data.status).toBe('imported');
    });

    it('should return error for invalid URL', async () => {
      // Setup mocks to return false for all platforms (invalid URL)
      (printablesScraper.validateUrl as jest.Mock).mockReturnValue(false);
      (makerWorldScraper.validateUrl as jest.Mock).mockReturnValue(false);
      (thangsScraper.validateUrl as jest.Mock).mockReturnValue(false);

      const request = createRequest({
        url: 'invalid-url',
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('UNSUPPORTED_PLATFORM');
    });

    it('should return error for unsupported platform', async () => {
      // Setup mocks to return false for all platforms
      (printablesScraper.validateUrl as jest.Mock).mockReturnValue(false);
      (makerWorldScraper.validateUrl as jest.Mock).mockReturnValue(false);
      (thangsScraper.validateUrl as jest.Mock).mockReturnValue(false);

      const request = createRequest({
        url: 'https://unsupported.com/model/123',
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('UNSUPPORTED_PLATFORM');
    });

    it('should handle rate limit exceeded', async () => {
      const rateLimitInfo = {
        totalHits: 11,
        totalHitsPerWindow: 11,
        remainingPoints: 0,
        msBeforeNext: 30000,
        isFirstInDuration: false,
      };

      // Setup mocks
      (printablesScraper.validateUrl as jest.Mock).mockReturnValue(true);
      (makerWorldScraper.validateUrl as jest.Mock).mockReturnValue(false);
      (thangsScraper.validateUrl as jest.Mock).mockReturnValue(false);
      (platformRateLimiters.consume as jest.Mock).mockRejectedValue(new RateLimitError(rateLimitInfo));

      const request = createRequest({
        url: 'https://www.printables.com/model/123',
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(429);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('RATE_LIMIT_EXCEEDED');
      expect(data.error.details.retryAfter).toBe(30);
    });

    it('should handle scraping errors', async () => {
      // Setup mocks
      (printablesScraper.validateUrl as jest.Mock).mockReturnValue(true);
      (makerWorldScraper.validateUrl as jest.Mock).mockReturnValue(false);
      (thangsScraper.validateUrl as jest.Mock).mockReturnValue(false);
      (platformRateLimiters.consume as jest.Mock).mockResolvedValue({});
      (printablesScraper.scrapeModel as jest.Mock).mockRejectedValue(new Error('Model not found'));

      const request = createRequest({
        url: 'https://www.printables.com/model/123',
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('SCRAPING_FAILED');
    });

    it('should handle validation errors', async () => {
      const mockScrapedModel = {
        title: '', // Invalid: empty title
        platform: 'printables',
        // ... other fields
      };

      // Setup mocks
      (printablesScraper.validateUrl as jest.Mock).mockReturnValue(true);
      (makerWorldScraper.validateUrl as jest.Mock).mockReturnValue(false);
      (thangsScraper.validateUrl as jest.Mock).mockReturnValue(false);
      (platformRateLimiters.consume as jest.Mock).mockResolvedValue({});
      (printablesScraper.scrapeModel as jest.Mock).mockResolvedValue(mockScrapedModel);
      (DataNormalizer.validateScrapedModel as jest.Mock).mockReturnValue({
        isValid: false,
        errors: ['Title is required'],
      });

      const request = createRequest({
        url: 'https://www.printables.com/model/123',
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('INVALID_SCRAPED_DATA');
      expect(data.error.details.errors).toContain('Title is required');
    });

    it('should handle missing URL', async () => {
      const request = createRequest({});

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('INVALID_INPUT');
    });

    it('should handle malformed JSON', async () => {
      const request = new NextRequest('http://localhost/api/scraping/import', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: 'invalid json',
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('INTERNAL_ERROR');
    });
  });

  describe('GET', () => {
    it('should return service status', async () => {
      (platformRateLimiters.getAllRemainingPoints as jest.Mock).mockResolvedValue({
        printables: 8,
        makerworld: 12,
        thangs: 10,
      });

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.status).toBe('operational');
      expect(data.data.supportedPlatforms).toEqual(['printables', 'makerworld', 'thangs']);
      expect(data.data.rateLimits.printables.remaining).toBe(8);
      expect(data.data.rateLimits.makerworld.remaining).toBe(12);
      expect(data.data.rateLimits.thangs.remaining).toBe(10);
    });

    it('should handle errors in status check', async () => {
      (platformRateLimiters.getAllRemainingPoints as jest.Mock).mockRejectedValue(new Error('Redis connection failed'));

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('HEALTH_CHECK_FAILED');
    });
  });

  describe('Platform detection', () => {
    it('should detect Printables URLs', async () => {
      (printablesScraper.validateUrl as jest.Mock).mockReturnValue(true);
      (makerWorldScraper.validateUrl as jest.Mock).mockReturnValue(false);
      (thangsScraper.validateUrl as jest.Mock).mockReturnValue(false);

      const request = createRequest({
        url: 'https://www.printables.com/model/123456-test-model',
      });

      // Mock successful flow
      (platformRateLimiters.consume as jest.Mock).mockResolvedValue({});
      (printablesScraper.scrapeModel as jest.Mock).mockResolvedValue({
        platform: 'printables',
        title: 'Test',
        designer: { id: 'test', name: 'Test' },
      });
      (DataNormalizer.validateScrapedModel as jest.Mock).mockReturnValue({ isValid: true, errors: [] });
      (DataNormalizer.sanitizeScrapedModel as jest.Mock).mockReturnValue({});
      (DataNormalizer.convertToInternalModel as jest.Mock).mockReturnValue({ id: 'test' });

      const response = await POST(request);
      expect(printablesScraper.validateUrl).toHaveBeenCalledWith('https://www.printables.com/model/123456-test-model');
    });

    it('should detect MakerWorld URLs', async () => {
      (printablesScraper.validateUrl as jest.Mock).mockReturnValue(false);
      (makerWorldScraper.validateUrl as jest.Mock).mockReturnValue(true);
      (thangsScraper.validateUrl as jest.Mock).mockReturnValue(false);

      const request = createRequest({
        url: 'https://makerworld.com/en/models/123456',
      });

      // Mock successful flow
      (platformRateLimiters.consume as jest.Mock).mockResolvedValue({});
      (makerWorldScraper.scrapeModel as jest.Mock).mockResolvedValue({
        platform: 'makerworld',
        title: 'Test',
        designer: { id: 'test', name: 'Test' },
      });
      (DataNormalizer.validateScrapedModel as jest.Mock).mockReturnValue({ isValid: true, errors: [] });
      (DataNormalizer.sanitizeScrapedModel as jest.Mock).mockReturnValue({});
      (DataNormalizer.convertToInternalModel as jest.Mock).mockReturnValue({ id: 'test' });

      const response = await POST(request);
      expect(makerWorldScraper.validateUrl).toHaveBeenCalledWith('https://makerworld.com/en/models/123456');
    });

    it('should detect Thangs URLs', async () => {
      (printablesScraper.validateUrl as jest.Mock).mockReturnValue(false);
      (makerWorldScraper.validateUrl as jest.Mock).mockReturnValue(false);
      (thangsScraper.validateUrl as jest.Mock).mockReturnValue(true);

      const request = createRequest({
        url: 'https://thangs.com/designer/user/model/123456',
      });

      // Mock successful flow
      (platformRateLimiters.consume as jest.Mock).mockResolvedValue({});
      (thangsScraper.scrapeModel as jest.Mock).mockResolvedValue({
        platform: 'thangs',
        title: 'Test',
        designer: { id: 'test', name: 'Test' },
      });
      (DataNormalizer.validateScrapedModel as jest.Mock).mockReturnValue({ isValid: true, errors: [] });
      (DataNormalizer.sanitizeScrapedModel as jest.Mock).mockReturnValue({});
      (DataNormalizer.convertToInternalModel as jest.Mock).mockReturnValue({ id: 'test' });

      const response = await POST(request);
      expect(thangsScraper.validateUrl).toHaveBeenCalledWith('https://thangs.com/designer/user/model/123456');
    });
  });
});
