# Cloudflare Workers для 3D Marketplace

Цей каталог містить Cloudflare Workers для обробки API запитів 3D Marketplace.

## Структура

```
src/workers/
├── marketplace-worker.ts  # Головний worker для marketplace API
└── README.md             # Цей файл
```

## Marketplace Worker

### Функціональність

Marketplace Worker обробляє наступні API endpoints:

- **`/api/models`** - Управління 3D моделями
  - `GET` - Отримання списку моделей
  - `POST` - Створення нової моделі

- **`/api/users`** - Управління користувачами
  - `GET` - Отримання списку користувачів
  - `POST` - Створення нового користувача

- **`/api/stats`** - Статистика marketplace
  - `GET` - Отримання загальної статистики

- **`/api/health`** - Перевірка стану worker
  - `GET` - Статус здоров'я worker

### Bindings

Worker використовує наступні Cloudflare bindings:

- **D1 Database** (`DB`) - Основна база даних
- **KV Storage** (`KV`) - Кеш та швидкий доступ до даних
- **R2 Storage** (`STORAGE`) - Файлове сховище для 3D моделей

### Environment Variables

- `ENVIRONMENT` - Середовище (development/production)
- `API_VERSION` - Версія API
- `ENABLE_CORS` - Увімкнення CORS
- `ALLOWED_ORIGINS` - Дозволені домени для CORS

## Розробка

### Локальний запуск

```bash
# Запуск worker в режимі розробки
npm run worker

# Worker буде доступний на http://localhost:8789
```

### Деплой

```bash
# Деплой worker на Cloudflare
npm run worker:deploy
```

### Тестування

Worker можна протестувати через API endpoint:

```bash
# Тестування health endpoint
curl http://localhost:8789/api/health

# Тестування через Next.js API
curl http://localhost:3000/api/marketplace-worker?endpoint=health
```

## Конфігурація

Worker налаштовується через файл `wrangler-worker.toml`:

```toml
name = "marketplace-worker"
main = "src/workers/marketplace-worker.ts"
compatibility_date = "2025-01-15"
compatibility_flags = ["nodejs_compat"]

# Bindings
[[d1_databases]]
binding = "DB"
database_name = "marketplace_db"
database_id = "8aad55c8-39fa-4432-b730-323680364383"

[[kv_namespaces]]
binding = "KV"
id = "9711cfa19bd04ce4afbd8b28bd051f7b"

[[r2_buckets]]
binding = "STORAGE"
bucket_name = "3d-marketplace-r2"
```

## API Документація

### Models API

#### GET /api/models
Отримання списку опублікованих моделей.

**Response:**
```json
{
  "success": true,
  "data": {
    "models": [...],
    "count": 50
  },
  "timestamp": "2025-01-15T10:00:00.000Z"
}
```

#### POST /api/models
Створення нової моделі.

**Request:**
```json
{
  "title": "Назва моделі",
  "description": "Опис моделі",
  "price": 10.99,
  "category": "toys",
  "designer_name": "Ім'я дизайнера",
  "file_url": "https://...",
  "thumbnail_url": "https://..."
}
```

### Users API

#### GET /api/users
Отримання списку користувачів.

#### POST /api/users
Створення нового користувача.

### Stats API

#### GET /api/stats
Отримання статистики marketplace.

**Response:**
```json
{
  "success": true,
  "data": {
    "models": 1250,
    "users": 340,
    "downloads": 5670,
    "kv_stats": {...}
  }
}
```

## Помилки та відладка

Worker логує всі помилки в консоль Cloudflare. Для відладки використовуйте:

```bash
# Перегляд логів worker
wrangler tail marketplace-worker
```

## Безпека

- Всі запити проходять CORS перевірку
- Помилки не розкривають внутрішню інформацію в production
- Використовуються підготовлені SQL запити для захисту від ін'єкцій
