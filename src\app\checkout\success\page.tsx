'use client';

import React, { useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { CheckCircle, Download, Home, Package } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function CheckoutSuccessPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to profile downloads after 10 seconds
    const timer = setTimeout(() => {
      router.push('/profile?tab=downloads');
    }, 10000);

    return () => clearTimeout(timer);
  }, [router]);

  return (
    <main className="container mx-auto px-4 py-8 max-w-2xl">
      <div className="text-center space-y-8">
        {/* Success Icon */}
        <div className="flex justify-center">
          <div className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center">
            <CheckCircle className="w-12 h-12 text-green-600" />
          </div>
        </div>

        {/* Success Message */}
        <div className="space-y-4">
          <h1 className="text-3xl font-bold text-gray-900">
            Оплата успішна!
          </h1>
          <p className="text-lg text-gray-600">
            Дякуємо за покупку! Ваші 3D моделі готові до завантаження.
          </p>
        </div>

        {/* Order Details Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-center">
              <Package className="mr-2 h-5 w-5" />
              Деталі замовлення
            </CardTitle>
            <CardDescription>
              Ваші моделі додано до розділу "Мої завантаження"
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                <span className="text-green-800 font-medium">
                  Платіж оброблено успішно
                </span>
              </div>
              <p className="text-green-700 text-sm mt-1">
                Ви отримаєте email-підтвердження протягом кількох хвилин
              </p>
            </div>

            <div className="text-left space-y-2">
              <h3 className="font-medium">Що далі?</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>✓ Моделі додано до вашого профілю</li>
                <li>✓ Файли доступні для миттєвого завантаження</li>
                <li>✓ Email-підтвердження надіслано</li>
                <li>✓ Ліцензія активована</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/profile?tab=downloads">
            <Button size="lg" className="w-full sm:w-auto">
              <Download className="mr-2 h-4 w-4" />
              Мої завантаження
            </Button>
          </Link>
          
          <Link href="/marketplace">
            <Button variant="outline" size="lg" className="w-full sm:w-auto">
              Продовжити покупки
            </Button>
          </Link>
          
          <Link href="/">
            <Button variant="outline" size="lg" className="w-full sm:w-auto">
              <Home className="mr-2 h-4 w-4" />
              На головну
            </Button>
          </Link>
        </div>

        {/* Auto Redirect Notice */}
        <div className="text-sm text-gray-500">
          Автоматичне перенаправлення до завантажень через 10 секунд...
        </div>

        {/* Support Info */}
        <Card className="bg-gray-50">
          <CardContent className="pt-6">
            <h3 className="font-medium mb-2">Потрібна допомога?</h3>
            <p className="text-sm text-gray-600 mb-4">
              Якщо у вас виникли проблеми з завантаженням або у вас є питання, 
              зв'яжіться з нашою службою підтримки.
            </p>
            <div className="flex flex-col sm:flex-row gap-2">
              <Button variant="outline" size="sm">
                Служба підтримки
              </Button>
              <Button variant="outline" size="sm">
                FAQ
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </main>
  );
}
