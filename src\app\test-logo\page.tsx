'use client';

import React from 'react';
import AnimatedLogo from '@/components/ui/animated-logo';

export default function TestLogoPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Контент для демонстрації хедера */}
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold mb-4">
            Покращений хедер з оптимізованим розміщенням
          </h1>
          <p className="text-xl text-muted-foreground mb-8">
            Реєстрація і кошик тепер в правому верхньому куті для кращого UX
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {/* Анімований логотип */}
          <div className="flex flex-col items-center gap-4 p-8 border rounded-lg bg-card">
            <h2 className="text-xl font-semibold">Анімований логотип</h2>
            <AnimatedLogo className="h-16 w-16" showText={true} />
            <p className="text-sm text-muted-foreground text-center">
              3D куб з градієнтами та анімацією обертання
            </p>
          </div>

          {/* Пошук з завантаженням */}
          <div className="flex flex-col items-center gap-4 p-8 border rounded-lg bg-card">
            <h2 className="text-xl font-semibold">Пошук з анімацією</h2>
            <div className="text-center">
              <p className="text-sm text-muted-foreground">
                Спробуйте пошук в хедері - з'явиться анімація завантаження
              </p>
            </div>
          </div>

          {/* Кнопка завантаження */}
          <div className="flex flex-col items-center gap-4 p-8 border rounded-lg bg-card">
            <h2 className="text-xl font-semibold">Завантаження</h2>
            <div className="text-center">
              <p className="text-sm text-muted-foreground">
                Натисніть на іконку завантаження в хедері для швидкого доступу
              </p>
            </div>
          </div>
        </div>

        {/* Демонстрація функцій */}
        <div className="bg-card rounded-lg p-8">
          <h2 className="text-2xl font-bold mb-6">Оптимізоване розміщення елементів:</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h3 className="text-lg font-semibold mb-3">🏠 Ліва частина</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li>• Анімований логотип</li>
                <li>• Головна навігація</li>
                <li>• Dropdown меню категорій</li>
                <li>• Responsive приховування</li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-3">🔍 Центр</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li>• Пошукова форма</li>
                <li>• Анімація завантаження</li>
                <li>• Центроване розміщення</li>
                <li>• Максимальна ширина</li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-3">👤 Правий кут</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li>• Реєстрація/Авторизація</li>
                <li>• Кошик покупок</li>
                <li>• Перемикач теми</li>
                <li>• Кнопка завантаження</li>
              </ul>
            </div>
          </div>

          <div className="mt-8 p-4 bg-accent/20 rounded-lg">
            <h3 className="text-lg font-semibold mb-2">🎯 UX покращення:</h3>
            <p className="text-muted-foreground">
              Реєстрація і кошик тепер розміщені в правому верхньому куті - стандартне місце,
              де користувачі очікують їх знайти. Це покращує зручність використання та
              відповідає сучасним стандартам веб-дизайну.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
