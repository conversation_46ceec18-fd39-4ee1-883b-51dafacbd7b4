name = "marketplace-worker"
main = "src/workers/marketplace-worker.ts"
compatibility_date = "2025-01-15"
compatibility_flags = ["nodejs_compat"]
workers_dev = true

# Development configuration
[dev]
ip = "localhost"
port = 8789
local_protocol = "http"

# Use local bindings for development
[vars]
ENVIRONMENT = "development"
API_VERSION = "2.0.0"
ENABLE_CORS = "true"
ALLOWED_ORIGINS = "*"

# D1 database binding
[[d1_databases]]
binding = "DB"
database_name = "marketplace_db"
database_id = "8aad55c8-39fa-4432-b730-323680364383"
preview_database_id = "DB"

# KV namespace binding
[[kv_namespaces]]
binding = "KV"
id = "9711cfa19bd04ce4afbd8b28bd051f7b"
preview_id = "KV"

# R2 bucket binding
[[r2_buckets]]
binding = "STORAGE"
bucket_name = "3d-marketplace-r2"
preview_bucket_name = "3d-marketplace-r2"
