import { withApiMiddleware } from '@/lib/cloudflare/middleware';
import { NextRequest, NextResponse } from 'next/server';

// Конфігурація для статичного експорту
export const dynamic = 'force-static';
export const revalidate = false;

/**
 * GET: Приклад використання всіх Cloudflare Bindings
 */
export const GET = withApiMiddleware(async (
  _request: NextRequest,
  { db, storage, kv, worker, hyperdrive }
) => {
  try {
    // Результати для кожного сервісу
    const results: Record<string, any> = {};

    // D1: Отримання списку таблиць
    try {
      const tablesResult = await db.prepare(`
        SELECT name FROM sqlite_master
        WHERE type='table'
        ORDER BY name
      `).all();

      results.d1 = {
        available: true,
        tables: tablesResult.results,
      };
    } catch (error) {
      results.d1 = {
        available: false,
        error: (error as Error).message,
      };
    }

    // R2: Отримання списку об'єктів
    try {
      const objects = await storage.list({ limit: 10 });
      results.r2 = {
        available: true,
        objects: objects.objects,
      };
    } catch (error) {
      results.r2 = {
        available: false,
        error: (error as Error).message,
      };
    }

    // KV: Тестовий запис і читання
    try {
      // Запис тестового значення
      const testKey = 'test-key';
      const testValue = { timestamp: Date.now(), message: 'Hello from KV!' };

      await kv.put(testKey, JSON.stringify(testValue));

      // Читання тестового значення
      const value = await kv.get(testKey, { type: 'json' });

      results.kv = {
        available: true,
        testKey,
        value,
      };
    } catch (error) {
      results.kv = {
        available: false,
        error: (error as Error).message,
      };
    }

    // Worker: Перевірка доступності
    try {
      if (worker && typeof worker.fetch === 'function') {
        results.worker = {
          available: true,
          message: 'Worker binding is available',
        };
      } else {
        results.worker = {
          available: false,
          message: 'Worker binding is not configured',
        };
      }
    } catch (error) {
      results.worker = {
        available: false,
        error: (error as Error).message,
      };
    }

    // Hyperdrive: Перевірка доступності
    try {
      if (hyperdrive && typeof hyperdrive.query === 'function') {
        results.hyperdrive = {
          available: true,
          message: 'Hyperdrive binding is available',
        };
      } else {
        results.hyperdrive = {
          available: false,
          message: 'Hyperdrive binding is not configured',
        };
      }
    } catch (error) {
      results.hyperdrive = {
        available: false,
        error: (error as Error).message,
      };
    }

    return NextResponse.json({
      success: true,
      message: 'Cloudflare Bindings status',
      data: results,
    });
  } catch (error) {
    console.error('Error in Cloudflare Bindings example:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to execute Cloudflare Bindings example' },
      { status: 500 }
    );
  }
});

/**
 * POST: Приклад запису даних через Cloudflare Bindings
 */
export const POST = withApiMiddleware(async (
  request: NextRequest,
  { db, storage, kv }
) => {
  try {
    const body = await request.json() as any;
    const results: Record<string, any> = {};

    // D1: Запис тестових даних
    if (body && body.d1) {
      try {
        const id = crypto.randomUUID();
        await db.prepare(`
          INSERT INTO users (id, email, name, created_at, updated_at)
          VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `).bind(id, `test-${id}@example.com`, `Test User ${id}`).run();

        results.d1 = {
          success: true,
          id,
        };
      } catch (error) {
        results.d1 = {
          success: false,
          error: (error as Error).message,
        };
      }
    }

    // KV: Запис даних
    if (body && body.kv) {
      try {
        const key = body.kv.key || `test-${Date.now()}`;
        const value = body.kv.value || { timestamp: Date.now() };

        await kv.put(key, JSON.stringify(value));

        results.kv = {
          success: true,
          key,
        };
      } catch (error) {
        results.kv = {
          success: false,
          error: (error as Error).message,
        };
      }
    }

    // R2: Запис тестового файлу
    if (body && body.r2) {
      try {
        const key = body.r2.key || `test-${Date.now()}.txt`;
        const value = body.r2.value || 'Test content from R2';

        await storage.put(key, value);

        results.r2 = {
          success: true,
          key,
        };
      } catch (error) {
        results.r2 = {
          success: false,
          error: (error as Error).message,
        };
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Cloudflare Bindings data written',
      data: results,
    });
  } catch (error) {
    console.error('Error in Cloudflare Bindings example:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to execute Cloudflare Bindings example' },
      { status: 500 }
    );
  }
});
