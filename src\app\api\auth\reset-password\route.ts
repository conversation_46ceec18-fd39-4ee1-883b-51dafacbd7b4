import { NextRequest, NextResponse } from 'next/server';
import { queryOne, execute, getDb } from '@/lib/db';
import { hashPassword } from '@/lib/auth';
import { z } from 'zod';

// Схема валідації для скидання пароля
const resetPasswordSchema = z.object({
  token: z.string().min(1, { message: 'Токен обов\'язковий' }),
  email: z.string().email({ message: 'Невірний формат email' }),
  password: z.string().min(8, { message: 'Пароль має містити щонайменше 8 символів' }),
});

/**
 * Обробник POST-запиту для скидання пароля
 * @param request Запит
 * @returns Відповідь
 */
export async function POST(request: NextRequest) {
  try {
    // Отримання даних з запиту
    const body = await request.json();

    // Валідація даних
    const result = resetPasswordSchema.safeParse(body);
    if (!result.success) {
      return NextResponse.json(
        { success: false, message: result.error.errors[0].message },
        { status: 400 }
      );
    }

    const { token, email, password } = result.data;

    // Перевірка, чи доступна база даних
    const db = getDb();
    if (!db) {
      console.warn('База даних недоступна. Використовується тестовий режим скидання пароля.');

      // В режимі розробки імітуємо успішне скидання пароля
      if (token && email && password) {
        return NextResponse.json(
          { success: true, message: 'Пароль успішно змінено (тестовий режим)' },
          { status: 200 }
        );
      } else {
        return NextResponse.json(
          { success: false, message: 'Недійсні дані для скидання пароля' },
          { status: 400 }
        );
      }
    }

    // Перевірка, чи існує токен і чи не прострочений він
    const verificationToken = await queryOne<{ identifier: string, token: string, expires_at: string }>(
      `SELECT identifier, token, expires_at
       FROM verification_tokens
       WHERE identifier = ? AND token = ?`,
      [email, token]
    );

    if (!verificationToken) {
      return NextResponse.json(
        { success: false, message: 'Недійсний або прострочений токен' },
        { status: 400 }
      );
    }

    // Перевірка, чи не прострочений токен
    const expiresAt = new Date(verificationToken.expires_at);
    if (expiresAt < new Date()) {
      return NextResponse.json(
        { success: false, message: 'Токен прострочений. Запитайте новий лист для відновлення пароля.' },
        { status: 400 }
      );
    }

    // Перевірка, чи існує користувач з таким email
    const user = await queryOne<{ id: string }>(
      'SELECT id FROM users WHERE email = ?',
      [email]
    );

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Користувач не знайдений' },
        { status: 404 }
      );
    }

    // Хешування нового пароля
    const hashedPassword = await hashPassword(password);

    // Оновлення пароля користувача
    await execute(
      `UPDATE users
       SET password = ?, updated_at = CURRENT_TIMESTAMP
       WHERE id = ?`,
      [hashedPassword, user.id]
    );

    // Видалення використаного токена
    await execute(
      `DELETE FROM verification_tokens
       WHERE identifier = ? AND token = ?`,
      [email, token]
    );

    // Повернення успішної відповіді
    return NextResponse.json(
      { success: true, message: 'Пароль успішно змінено' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Помилка при скиданні пароля:', error);

    return NextResponse.json(
      { success: false, message: 'Сталася помилка при скиданні пароля. Спробуйте ще раз.' },
      { status: 500 }
    );
  }
}
