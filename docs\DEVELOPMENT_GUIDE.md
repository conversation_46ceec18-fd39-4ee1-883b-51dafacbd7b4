# Посібник з розробки 3D-Маркетплейсу

## Зміст

1. [Вступ](#вступ)
2. [Налаштування середовища розробки](#налаштування-середовища-розробки)
   - [Вимоги](#вимоги)
   - [Встановлення](#встановлення)
   - [Конфігурація](#конфігурація)
3. [Архітектура проекту](#архітектура-проекту)
   - [Структура папок](#структура-папок)
   - [Ключові компоненти](#ключові-компоненти)
   - [Маршрутизація](#маршрутизація)
4. [Технологічний стек](#технологічний-стек)
   - [Frontend](#frontend)
   - [3D-технології](#3d-технології)
   - [Стилізація](#стилізація)
5. [Робочий процес розробки](#робочий-процес-розробки)
   - [Створення нових компонентів](#створення-нових-компонентів)
   - [Додавання нових сторінок](#додавання-нових-сторінок)
   - [Інтеграція 3D-моделей](#інтеграція-3d-моделей)
6. [Тестування](#тестування)
   - [Юніт-тести](#юніт-тести)
   - [Інтеграційні тести](#інтеграційні-тести)
   - [Тестування продуктивності](#тестування-продуктивності)
7. [Розгортання](#розгортання)
   - [Підготовка до розгортання](#підготовка-до-розгортання)
   - [Розгортання на Cloudflare](#розгортання-на-cloudflare)
   - [CI/CD](#cicd)
8. [Рекомендації та найкращі практики](#рекомендації-та-найкращі-практики)
   - [Продуктивність](#продуктивність)
   - [Доступність](#доступність)
   - [SEO](#seo)

## Вступ

Цей посібник призначений для розробників, які працюють над проектом 3D-маркетплейсу. Він містить інформацію про налаштування середовища розробки, архітектуру проекту, робочий процес розробки та розгортання.

## Налаштування середовища розробки

### Вимоги

Для розробки проекту вам знадобиться:

- Node.js (версія 18.x або вище)
- npm (версія 9.x або вище) або yarn (версія 1.22.x або вище)
- Git
- Редактор коду (рекомендується VS Code)

### Встановлення

1. Клонуйте репозиторій:
```bash
git clone https://github.com/your-username/3d-marketplace.git
cd 3d-marketplace
```

2. Встановіть залежності:
```bash
npm install
# або
yarn install
```

3. Запустіть проект у режимі розробки:
```bash
npm run dev
# або
yarn dev
```

Проект буде доступний за адресою [http://localhost:3000](http://localhost:3000).

### Конфігурація

Проект використовує наступні конфігураційні файли:

- `next.config.js` - конфігурація Next.js
- `tailwind.config.js` - конфігурація Tailwind CSS
- `components.json` - конфігурація shadcn/ui
- `.env.local` - локальні змінні середовища (не включені в репозиторій)

Для локальної розробки створіть файл `.env.local` на основі `.env.example`:

```bash
cp .env.example .env.local
```

## Архітектура проекту

### Структура папок

```
3d-marketplace/
├── docs/                 # Документація
├── public/               # Статичні файли
├── src/
│   ├── app/              # Сторінки Next.js App Router
│   │   ├── auth/         # Сторінки аутентифікації
│   │   ├── models/       # Сторінки моделей
│   │   └── ...
│   ├── components/       # Компоненти React
│   │   ├── ui/           # Базові UI компоненти (shadcn/ui)
│   │   ├── model-viewer/ # Компоненти для перегляду 3D-моделей
│   │   └── ...
│   └── lib/              # Утиліти та допоміжні функції
├── .env.example          # Приклад змінних середовища
├── next.config.js        # Конфігурація Next.js
├── package.json          # Залежності проекту
└── tailwind.config.js    # Конфігурація Tailwind CSS
```

### Ключові компоненти

- **ClientLayout** (`src/app/ClientLayout.tsx`) - Загальний макет для всіх сторінок
- **ThemeProvider** (`src/components/theme-provider.tsx`) - Провайдер теми (світла/темна)
- **ModelViewer** (`src/components/model-viewer/model-viewer.tsx`) - Компонент для перегляду 3D-моделей
- **SplineScene** (`src/components/ui/splite.tsx`) - Компонент для інтеграції Spline

### Маршрутизація

Проект використовує App Router від Next.js 14. Структура маршрутів відповідає структурі папок у `src/app/`:

- `/` - Головна сторінка (`src/app/page.tsx`)
- `/models` - Сторінка моделей (`src/app/models/page.tsx`)
- `/models/[id]` - Сторінка деталей моделі (`src/app/models/[id]/page.tsx`)
- `/models/upload` - Сторінка завантаження моделі (`src/app/models/upload/page.tsx`)
- `/auth/signin` - Сторінка входу (`src/app/auth/signin/page.tsx`)
- `/auth/signup` - Сторінка реєстрації (`src/app/auth/signup/page.tsx`)

## Технологічний стек

### Frontend

- **Next.js** (версія 14) - React-фреймворк з App Router
- **React** (версія 18) - Бібліотека для створення інтерфейсів
- **TypeScript** - Типізована надбудова над JavaScript

### 3D-технології

- **Spline** - Інструмент для створення та інтеграції 3D-дизайнів
- **Three.js** - Бібліотека для 3D-графіки у веб
- **React Three Fiber** - React-рендерер для Three.js

### Стилізація

- **Tailwind CSS** - Утилітарний CSS-фреймворк
- **shadcn/ui** - Колекція компонентів на основі Radix UI та Tailwind CSS
- **Lucide** - Бібліотека іконок

## Робочий процес розробки

### Створення нових компонентів

1. Для створення нового компонента UI використовуйте shadcn/ui:

```bash
npx shadcn-ui@latest add [component-name]
```

2. Для створення власного компонента створіть новий файл у відповідній папці:

```tsx
// src/components/example-component.tsx
'use client';

import React from 'react';
import { Button } from '@/components/ui/button';

interface ExampleComponentProps {
  title: string;
}

const ExampleComponent: React.FC<ExampleComponentProps> = ({ title }) => {
  return (
    <div className="p-4 bg-muted rounded-lg">
      <h2 className="text-xl font-bold mb-4">{title}</h2>
      <Button>Натисніть мене</Button>
    </div>
  );
};

export default ExampleComponent;
```

### Додавання нових сторінок

Для додавання нової сторінки створіть новий файл `page.tsx` у відповідній папці:

```tsx
// src/app/example/page.tsx
'use client';

import React from 'react';
import ExampleComponent from '@/components/example-component';

export default function ExamplePage() {
  return (
    <main className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-6">Приклад сторінки</h1>
      <ExampleComponent title="Мій компонент" />
    </main>
  );
}
```

### Інтеграція 3D-моделей

1. Для інтеграції Spline-сцени:

```tsx
import { SplineScene } from '@/components/ui/splite';

<SplineScene 
  scene="https://prod.spline.design/your-scene-id/scene.splinecode"
  preset="PRODUCT_VIEWER"
/>
```

2. Для інтеграції 3D-моделі через ModelViewer:

```tsx
import { ModelViewer } from '@/components/model-viewer/model-viewer';

<ModelViewer 
  model={{
    id: '1',
    name: 'Example Model',
    splineSceneUrl: 'https://prod.spline.design/your-scene-id/scene.splinecode',
  }}
/>
```

## Тестування

### Юніт-тести

Для юніт-тестування використовується Jest та React Testing Library:

```bash
npm run test
# або
yarn test
```

Приклад тесту:

```tsx
// src/components/example-component.test.tsx
import { render, screen } from '@testing-library/react';
import ExampleComponent from './example-component';

describe('ExampleComponent', () => {
  it('renders the title correctly', () => {
    render(<ExampleComponent title="Test Title" />);
    expect(screen.getByText('Test Title')).toBeInTheDocument();
  });
});
```

### Інтеграційні тести

Для інтеграційного тестування використовується Cypress:

```bash
npm run cypress
# або
yarn cypress
```

### Тестування продуктивності

Для тестування продуктивності використовується Lighthouse:

```bash
npm run lighthouse
# або
yarn lighthouse
```

## Розгортання

### Підготовка до розгортання

1. Переконайтеся, що всі тести проходять успішно:

```bash
npm run test
# або
yarn test
```

2. Створіть оптимізовану збірку:

```bash
npm run build
# або
yarn build
```

3. Перевірте збірку локально:

```bash
npm run start
# або
yarn start
```

### Розгортання на Cloudflare

Проект налаштований для розгортання на Cloudflare Pages:

1. Налаштуйте проект у Cloudflare Pages:
   - Підключіть репозиторій GitHub
   - Встановіть команду збірки: `npm run build`
   - Встановіть директорію вихідних файлів: `.next`
   - Налаштуйте змінні середовища

2. Розгорніть проект:
   - Автоматично при пуші в основну гілку
   - Або вручну через панель керування Cloudflare

### CI/CD

Проект використовує GitHub Actions для CI/CD:

- `.github/workflows/ci.yml` - Перевірка коду та тести
- `.github/workflows/deploy.yml` - Розгортання на Cloudflare Pages

## Рекомендації та найкращі практики

### Продуктивність

1. **Оптимізуйте зображення** - Використовуйте компонент `Image` з Next.js
2. **Ліниве завантаження** - Використовуйте `React.lazy` та `Suspense`
3. **Оптимізуйте 3D-моделі** - Зменшуйте кількість полігонів та розмір текстур
4. **Використовуйте кешування** - Кешуйте статичні дані та API-відповіді

### Доступність

1. **Семантичний HTML** - Використовуйте правильні HTML-теги
2. **ARIA-атрибути** - Додавайте ARIA-атрибути для складних компонентів
3. **Клавіатурна навігація** - Забезпечте можливість навігації клавіатурою
4. **Контраст кольорів** - Забезпечте достатній контраст між текстом та фоном

### SEO

1. **Метадані** - Додавайте метадані для кожної сторінки
2. **Структуровані дані** - Використовуйте JSON-LD для структурованих даних
3. **Семантичний HTML** - Використовуйте правильні HTML-теги
4. **Оптимізація зображень** - Додавайте атрибути `alt` для зображень
