'use client';

import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Model } from '@/types/models';
import AddToCartButton from './add-to-cart-button';
import { Eye, Heart, Download } from 'lucide-react';

interface ModelGridProps {
  models: Model[];
}

export default function ModelGrid({ models }: ModelGridProps) {
  if (models.length === 0) {
    return (
      <div className="text-center py-12 bg-card rounded-lg shadow-sm">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-muted-foreground mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <h3 className="text-xl font-medium mb-2">No models found</h3>
        <p className="text-muted-foreground">Try changing your filters or search criteria.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {models.map((model) => (
        <Card key={model.id} className="overflow-hidden h-full hover:shadow-md transition-shadow flex flex-col">
          <Link href={`/marketplace/${model.id}`} className="flex-1">
            <div className="aspect-square relative">
              <img
                src={model.thumbnail}
                alt={model.title}
                className="w-full h-full object-cover"
              />
              {model.price === 0 ? (
                <Badge className="absolute top-2 left-2 bg-green-500">Безкоштовно</Badge>
              ) : (
                <Badge className="absolute top-2 left-2 bg-blue-500">${model.price}</Badge>
              )}
            </div>
            <CardHeader className="p-4 pb-2">
              <CardTitle className="text-lg line-clamp-2">{model.title}</CardTitle>
            </CardHeader>
            <CardContent className="p-4 pt-0 pb-2">
              <p className="text-sm text-muted-foreground">від {model.designer.name}</p>
              <div className="flex items-center justify-between mt-2 text-sm text-muted-foreground">
                <div className="flex items-center">
                  <Heart className="h-4 w-4 mr-1" />
                  {model.likes}
                </div>
                <div className="flex items-center">
                  <Download className="h-4 w-4 mr-1" />
                  {model.downloads}
                </div>
                <div className="flex items-center">
                  <Eye className="h-4 w-4 mr-1" />
                  {model.views || 0}
                </div>
              </div>
            </CardContent>
          </Link>
          <CardFooter className="p-4 pt-0">
            <AddToCartButton
              model={{
                id: model.id,
                name: model.title,
                price: model.price,
                thumbnail_url: model.thumbnail,
                is_free: model.price === 0,
                user_id: model.designer.id || '',
              }}
              size="sm"
              className="w-full"
            />
          </CardFooter>
        </Card>
      ))}
    </div>
  );
}
