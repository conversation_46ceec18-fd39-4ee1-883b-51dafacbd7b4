'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { ImportModelDialog } from '@/components/marketplace/import-model-dialog';
import { BatchImportDialog } from '@/components/marketplace/batch-import-dialog';
import { ScrapingHealthStatus } from '@/components/marketplace/scraping-health-status';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
// Визначаємо локальні типи, які відповідають типам з @/types/models
interface Designer {
  id?: string;
  name: string;
  avatar?: string;
  models?: number;
  followers?: number;
}

interface Model {
  id: string;
  title: string;
  description?: string;
  thumbnail: string;
  designer: Designer;
  price: number;
  category: string;
  likes: number;
  downloads: number;
  tags: string[];
  isFeatured?: boolean;
  images: string[];
  designerAvatar?: string;
  designerModels?: number;
  designerFollowers?: number;
  fileFormats: string[];
  fileSize: string;
}

interface ModelsQueryParams {
  page?: number;
  limit?: number;
  category?: string;
  search?: string;
  tags?: string[];
  minPrice?: number;
  maxPrice?: number;
  sortBy?: 'newest' | 'popular' | 'downloads' | 'price_asc' | 'price_desc';
}

// Categories based on thangs.com
const CATEGORIES = [
  'All',
  'Toys & Games',
  'Functional',
  'Home',
  'Miniatures',
  'Costumes & Cosplay',
  'Art',
  'Articulated',
  'Gridfinity',
  'Print In Place',
];

// Popular tags
const POPULAR_TAGS = [
  'dragon', 'articulated', 'vase', 'organizer', 'cosplay',
  'miniature', 'functional', 'decor', 'toy', 'fantasy'
];

// Формати файлів можуть бути додані пізніше, якщо потрібно

interface ClientMarketplaceProps {
  initialModels: Model[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
  initialSearchParams: {
    page?: number;
    category?: string;
    search?: string;
    sortBy?: ModelsQueryParams['sortBy'];
    minPrice?: number;
    maxPrice?: number;
    fileFormats?: string[];
  };
}

export default function ClientMarketplace({
  initialModels,
  pagination,
  initialSearchParams
}: ClientMarketplaceProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  // State for filters and models
  const [models, setModels] = useState<Model[]>(initialModels || []);

  // Оновлюємо моделі при зміні initialModels
  useEffect(() => {
    if (initialModels && initialModels.length > 0) {
      setModels(initialModels);
    }
  }, [initialModels]);
  const [selectedCategory, setSelectedCategory] = useState(initialSearchParams.category || 'All');
  const [sortBy, setSortBy] = useState(initialSearchParams.sortBy || 'popular');
  // Визначаємо початковий фільтр ціни на основі параметрів
  const initialPriceFilter = (() => {
    if (initialSearchParams.maxPrice === 0) return 'free';
    if (initialSearchParams.minPrice && initialSearchParams.minPrice > 0) return 'paid';
    if (initialSearchParams.minPrice !== undefined || initialSearchParams.maxPrice !== undefined) return 'all';
    return 'all';
  })();

  // Визначаємо початковий діапазон цін
  const initialPriceRange = [
    initialSearchParams.minPrice !== undefined ? initialSearchParams.minPrice : 0,
    initialSearchParams.maxPrice !== undefined ? initialSearchParams.maxPrice : 50
  ];

  const [priceFilter, setPriceFilter] = useState(initialPriceFilter);
  const [priceRange, setPriceRange] = useState<[number, number]>(initialPriceRange as [number, number]);
  const [searchQuery, setSearchQuery] = useState(initialSearchParams.search || '');
  const [isSearching, setIsSearching] = useState(false);
  const [selectedFileFormats, setSelectedFileFormats] = useState<string[]>(initialSearchParams.fileFormats || []);

  // Оновлюємо стан при зміні URL
  useEffect(() => {
    // Отримуємо параметри з URL
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const sortBy = searchParams.get('sortBy');
    const minPrice = searchParams.get('minPrice');
    const maxPrice = searchParams.get('maxPrice');
    const fileFormats = searchParams.get('fileFormats');

    if (category) {
      setSelectedCategory(category);
    }

    // Оновлюємо вибрані формати файлів
    if (fileFormats) {
      setSelectedFileFormats(fileFormats.split(','));
    }

    if (search) {
      setSearchQuery(search);
    }

    if (sortBy) {
      // Перевіряємо, чи є значення sortBy допустимим
      const validSortValues = ['newest', 'popular', 'downloads', 'price_asc', 'price_desc'] as const;
      type ValidSortType = typeof validSortValues[number];

      // Перевіряємо, чи є значення допустимим
      const isValidSort = (value: string): value is ValidSortType =>
        validSortValues.includes(value as ValidSortType);

      if (isValidSort(sortBy)) {
        setSortBy(sortBy);
      } else {
        // Якщо значення недопустиме, встановлюємо значення за замовчуванням
        setSortBy('popular');
      }
    }

    // Оновлюємо фільтр ціни
    if (maxPrice === '0') {
      setPriceFilter('free');
    } else if (minPrice && parseFloat(minPrice) > 0) {
      setPriceFilter('paid');
    } else if (minPrice || maxPrice) {
      setPriceFilter('all');

      // Оновлюємо діапазон цін
      const newPriceRange: [number, number] = [
        minPrice ? parseFloat(minPrice) : 0,
        maxPrice ? parseFloat(maxPrice) : 50
      ];
      setPriceRange(newPriceRange);
    }
  }, [searchParams]);

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSearching(true);

    // Build query parameters
    const params = new URLSearchParams();
    if (searchQuery) params.set('search', searchQuery);
    if (selectedCategory !== 'All') params.set('category', selectedCategory);
    if (sortBy) params.set('sortBy', sortBy);
    if (priceFilter === 'free') params.set('maxPrice', '0');
    if (priceFilter === 'paid') params.set('minPrice', '0.01');
    if (priceFilter === 'all' && (priceRange[0] > 0 || priceRange[1] < 50)) {
      params.set('minPrice', priceRange[0].toString());
      params.set('maxPrice', priceRange[1].toString());
    }
    // Додаємо формати файлів, якщо вони вибрані
    if (selectedFileFormats.length > 0) {
      params.set('fileFormats', selectedFileFormats.join(','));
    }

    // Navigate to the new URL
    router.push(`/marketplace?${params.toString()}`);

    // Скидаємо прапорець пошуку після переходу
    setTimeout(() => setIsSearching(false), 500);
  };

  // Handle category selection
  const handleCategorySelect = (category: string) => {
    setSelectedCategory(category);

    // Build query parameters
    const params = new URLSearchParams(searchParams.toString());
    if (category !== 'All') {
      params.set('category', category);
    } else {
      params.delete('category');
    }

    // Navigate to the new URL
    router.push(`/marketplace?${params.toString()}`);
  };

  // Handle sort change
  const handleSortChange = (value: string) => {
    // Перевіряємо, чи є значення sortBy допустимим
    const validSortValues = ['newest', 'popular', 'downloads', 'price_asc', 'price_desc'] as const;
    type ValidSortType = typeof validSortValues[number];

    // Перевіряємо, чи є значення допустимим
    const isValidSort = (value: string): value is ValidSortType =>
      validSortValues.includes(value as ValidSortType);

    if (isValidSort(value)) {
      setSortBy(value);
    } else {
      // Якщо значення недопустиме, встановлюємо значення за замовчуванням
      setSortBy('popular');
      // Змінюємо value на значення за замовчуванням для URL
      value = 'popular';
    }

    // Build query parameters
    const params = new URLSearchParams(searchParams.toString());
    params.set('sortBy', value);

    // Navigate to the new URL
    router.push(`/marketplace?${params.toString()}`);
  };

  return (
    <div className="flex flex-col lg:flex-row gap-8">
      {/* Sidebar with filters */}
      <aside className="w-full lg:w-1/4 space-y-6">
        <div className="bg-card rounded-lg p-6 shadow-sm">
          <h2 className="text-xl font-semibold mb-4">Категорії</h2>
          <div className="space-y-2">
            {CATEGORIES.map((category) => (
              <button
                key={category}
                className={`block w-full text-left px-3 py-2 rounded-md transition-colors ${
                  selectedCategory === category
                    ? 'bg-primary text-primary-foreground'
                    : 'hover:bg-muted'
                }`}
                onClick={() => handleCategorySelect(category)}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        <div className="bg-card rounded-lg p-6 shadow-sm">
          <h2 className="text-xl font-semibold mb-4">Ціна</h2>
          <div className="space-y-4">
            <div className="flex space-x-2">
              <Button
                variant={priceFilter === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  setPriceFilter('all');
                  // Застосовуємо фільтр одразу
                  const params = new URLSearchParams(searchParams.toString());
                  params.delete('minPrice');
                  params.delete('maxPrice');
                  router.push(`/marketplace?${params.toString()}`);
                }}
                className="flex-1"
              >
                Всі
              </Button>
              <Button
                variant={priceFilter === 'free' ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  setPriceFilter('free');
                  // Застосовуємо фільтр одразу
                  const params = new URLSearchParams(searchParams.toString());
                  params.set('maxPrice', '0');
                  params.delete('minPrice');
                  router.push(`/marketplace?${params.toString()}`);
                }}
                className="flex-1"
              >
                Безкоштовні
              </Button>
              <Button
                variant={priceFilter === 'paid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  setPriceFilter('paid');
                  // Застосовуємо фільтр одразу
                  const params = new URLSearchParams(searchParams.toString());
                  params.set('minPrice', '0.01');
                  params.delete('maxPrice');
                  router.push(`/marketplace?${params.toString()}`);
                }}
                className="flex-1"
              >
                Платні
              </Button>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between">
                <span>${priceRange[0]}</span>
                <span>${priceRange[1]}</span>
              </div>
              <Slider
                defaultValue={[0, 50]}
                max={50}
                step={1}
                value={priceRange}
                onValueChange={(value: number[]) => {
                  // Явно приводимо тип до [number, number]
                  setPriceRange(value as [number, number]);
                }}
                onValueCommit={(value: number[]) => {
                  // Застосовуємо фільтр після завершення перетягування
                  const params = new URLSearchParams(searchParams.toString());
                  params.set('minPrice', value[0].toString());
                  params.set('maxPrice', value[1].toString());
                  // Встановлюємо фільтр на "all", оскільки використовуємо діапазон
                  setPriceFilter('all');
                  router.push(`/marketplace?${params.toString()}`);
                }}
              />
            </div>
          </div>
        </div>

        <div className="bg-card rounded-lg p-6 shadow-sm">
          <h2 className="text-xl font-semibold mb-4">Популярні теги</h2>
          <div className="flex flex-wrap gap-2">
            {POPULAR_TAGS.map((tag) => (
              <Badge
                key={tag}
                variant="outline"
                className="cursor-pointer hover:bg-primary hover:text-primary-foreground"
                onClick={() => {
                  setSearchQuery(tag);
                  // Застосовуємо фільтр одразу
                  const params = new URLSearchParams(searchParams.toString());
                  params.set('search', tag);
                  router.push(`/marketplace?${params.toString()}`);
                }}
              >
                {tag}
              </Badge>
            ))}
          </div>
        </div>

        {/* Формати файлів */}
        <div className="bg-card rounded-lg p-6 shadow-sm">
          <h2 className="text-xl font-semibold mb-4">Формати файлів</h2>
          <div className="flex flex-wrap gap-2">
            {selectedFileFormats.length > 0 ? (
              selectedFileFormats.map((format) => (
                <Badge
                  key={format}
                  variant="secondary"
                  className="cursor-pointer"
                  onClick={() => {
                    // Видаляємо формат з вибраних
                    const newFormats = selectedFileFormats.filter(f => f !== format);
                    setSelectedFileFormats(newFormats);

                    // Оновлюємо URL
                    const params = new URLSearchParams(searchParams.toString());
                    if (newFormats.length > 0) {
                      params.set('fileFormats', newFormats.join(','));
                    } else {
                      params.delete('fileFormats');
                    }
                    router.push(`/marketplace?${params.toString()}`);
                  }}
                >
                  {format} ✕
                </Badge>
              ))
            ) : (
              <p className="text-sm text-muted-foreground">Формати файлів не вибрано</p>
            )}
          </div>
        </div>

        {/* Scraping Health Status */}
        <ScrapingHealthStatus />
      </aside>

      {/* Main content with models */}
      <div className="w-full lg:w-3/4">
        {/* Search form */}
        <form onSubmit={handleSearch} className="mb-6">
          <div className="relative">
            <Input
              type="text"
              placeholder="Пошук моделей, дизайнерів або тегів..."
              className="w-full pl-10 py-6 text-lg"
              value={searchQuery}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                // Використовуємо безпечне приведення типу
                const value = e.target.value;
                setSearchQuery(value);
              }}
            />
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <Button
              type="submit"
              className="absolute right-2 top-1/2 transform -translate-y-1/2"
              disabled={isSearching}
            >
              {isSearching ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                  Пошук...
                </>
              ) : 'Пошук'}
            </Button>
          </div>
        </form>

        {/* Models header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
          <h2 className="text-2xl font-bold mb-2 sm:mb-0">
            {pagination.total} {pagination.total === 1 ? 'Модель' : 'Моделей'}
          </h2>
          <div className="flex items-center gap-4">
            {/* Import Model Button */}
            <ImportModelDialog
              onModelImported={(model: any) => {
                // Додаємо нову модель до списку
                setModels(prev => [model, ...prev]);
              }}
            />

            {/* Batch Import Button */}
            <BatchImportDialog
              onBatchCompleted={(results: any) => {
                // Оновлюємо список моделей після пакетного імпорту
                console.log('Batch import completed:', results);
                // Можна додати логіку для оновлення списку моделей
              }}
            />

            <div className="flex items-center">
              <span className="mr-2 text-muted-foreground">Сортувати за:</span>
              <Select value={sortBy} onValueChange={handleSortChange}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Сортування" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="popular">Найпопулярніші</SelectItem>
                  <SelectItem value="downloads">Найбільше завантажень</SelectItem>
                  <SelectItem value="newest">Найновіші</SelectItem>
                  <SelectItem value="price_asc">Ціна: від низької до високої</SelectItem>
                  <SelectItem value="price_desc">Ціна: від високої до низької</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Models grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {models.map((model) => (
            <Link href={`/marketplace/${model.id}`} key={model.id}>
              <Card className="overflow-hidden h-full hover:shadow-md transition-shadow">
                <div className="aspect-square relative">
                  <img
                    src={model.thumbnail}
                    alt={model.title}
                    className="w-full h-full object-cover"
                  />
                  {model.price === 0 ? (
                    <Badge className="absolute top-2 left-2 bg-green-500">Free</Badge>
                  ) : (
                    <Badge className="absolute top-2 left-2 bg-blue-500">${model.price}</Badge>
                  )}
                </div>
                <CardHeader className="p-4 pb-2">
                  <CardTitle className="text-lg">{model.title}</CardTitle>
                </CardHeader>
                <CardContent className="p-4 pt-0 pb-2">
                  <p className="text-sm text-muted-foreground">by {model.designer.name}</p>
                </CardContent>
                <CardFooter className="p-4 pt-0 flex justify-between">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                    {model.likes}
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    {model.downloads}
                  </div>
                </CardFooter>
              </Card>
            </Link>
          ))}
        </div>

        {/* Empty state */}
        {models.length === 0 && (
          <div className="text-center py-12 bg-card rounded-lg shadow-sm">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-muted-foreground mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="text-xl font-medium mb-2">Моделі не знайдено</h3>
            <p className="text-muted-foreground">Спробуйте змінити фільтри або критерії пошуку.</p>
          </div>
        )}

        {/* Pagination */}
        {pagination && pagination.pages > 1 && (
          <div className="flex justify-center mt-8">
            <nav className="flex items-center gap-1">
              {/* Кнопка "Попередня" */}
              {pagination.page > 1 && (
                <Link
                  href={`/marketplace?${(() => {
                    const params = new URLSearchParams(searchParams.toString());
                    params.set('page', (pagination.page - 1).toString());
                    return params.toString();
                  })()}`}
                  className="px-4 py-2 border rounded bg-background hover:bg-muted flex items-center"
                  aria-label="Попередня сторінка"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  Попередня
                </Link>
              )}

              {/* Номери сторінок */}
              {Array.from({ length: pagination.pages }, (_, i) => i + 1)
                // Показуємо лише поточну сторінку, першу, останню та сторінки поруч з поточною
                .filter(pageNum =>
                  pageNum === 1 ||
                  pageNum === pagination.pages ||
                  Math.abs(pageNum - pagination.page) <= 1
                )
                .map((pageNum, index, array) => {
                  // Додаємо "..." між непослідовними сторінками
                  const showEllipsis = index > 0 && pageNum - array[index - 1] > 1;

                  // Build query parameters for this page
                  const params = new URLSearchParams(searchParams.toString());
                  params.set('page', pageNum.toString());

                  return (
                    <React.Fragment key={pageNum}>
                      {showEllipsis && (
                        <span className="px-4 py-2">...</span>
                      )}
                      <Link
                        href={`/marketplace?${params.toString()}`}
                        className={`px-4 py-2 border rounded ${
                          pageNum === pagination.page
                            ? 'bg-primary text-primary-foreground'
                            : 'bg-background hover:bg-muted'
                        }`}
                        aria-label={`Сторінка ${pageNum}`}
                        aria-current={pageNum === pagination.page ? 'page' : undefined}
                      >
                        {pageNum}
                      </Link>
                    </React.Fragment>
                  );
                })
              }

              {/* Кнопка "Наступна" */}
              {pagination.page < pagination.pages && (
                <Link
                  href={`/marketplace?${(() => {
                    const params = new URLSearchParams(searchParams.toString());
                    params.set('page', (pagination.page + 1).toString());
                    return params.toString();
                  })()}`}
                  className="px-4 py-2 border rounded bg-background hover:bg-muted flex items-center"
                  aria-label="Наступна сторінка"
                >
                  Наступна
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
              )}
            </nav>
          </div>
        )}
      </div>
    </div>
  );
}
