import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { uploadFile, generateFileKey, getFileUrl } from '@/lib/storage';

export async function POST(request: NextRequest) {
  try {
    // Перевірка аутентифікації
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Отримання файлу з запиту
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      );
    }

    // Перевірка типу файлу
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'model/gltf-binary',
      'model/gltf+json',
      'application/octet-stream',
      'application/x-stl',
      'application/vnd.ms-pki.stl',
    ];

    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { success: false, error: 'File type not allowed' },
        { status: 400 }
      );
    }

    // Перевірка розміру файлу (максимум 100 МБ)
    const maxSize = 100 * 1024 * 1024; // 100 МБ

    if (file.size > maxSize) {
      return NextResponse.json(
        { success: false, error: 'File too large (max 100MB)' },
        { status: 400 }
      );
    }

    // Визначення типу файлу
    let fileType = 'other';

    if (file.type.startsWith('image/')) {
      fileType = 'image';
    } else if (
      file.type === 'model/gltf-binary' ||
      file.type === 'model/gltf+json' ||
      file.type === 'application/x-stl' ||
      file.type === 'application/vnd.ms-pki.stl'
    ) {
      fileType = 'model';
    }

    // Генерація ключа для файлу
    const fileKey = generateFileKey(file.name, `${fileType}/${session.user.id}`);

    // Завантаження файлу
    const metadata = {
      userId: session.user.id,
      originalName: file.name,
      contentType: file.type,
    };

    const result = await uploadFile(fileKey, file, metadata);

    // Отримання URL файлу
    const fileUrl = getFileUrl(fileKey);

    return NextResponse.json({
      success: true,
      data: {
        key: fileKey,
        url: fileUrl,
        size: result.size,
        type: file.type,
        name: file.name,
      }
    });
  } catch (error) {
    console.error('Error uploading file:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to upload file' },
      { status: 500 }
    );
  }
}
