import { Model, ModelResponse, ModelsQueryParams, ModelsResponse } from '@/types/models';

/**
 * Отримання списку моделей з API
 */
export async function getModels(params?: ModelsQueryParams): Promise<ModelsResponse> {
  // Формування параметрів запиту
  const queryParams = new URLSearchParams();

  if (params?.page) queryParams.append('page', params.page.toString());
  if (params?.limit) queryParams.append('limit', params.limit.toString());
  if (params?.category) queryParams.append('category', params.category);
  if (params?.search) queryParams.append('search', params.search);
  if (params?.tags) queryParams.append('tags', params.tags.join(','));
  if (params?.minPrice !== undefined) queryParams.append('minPrice', params.minPrice.toString());
  if (params?.maxPrice !== undefined) queryParams.append('maxPrice', params.maxPrice.toString());
  if (params?.sortBy) queryParams.append('sortBy', params.sortBy);

  // Виконання запиту
  const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
  const response = await fetch(`/api/models${queryString}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    cache: 'no-store', // Вимкнення кешування для отримання актуальних даних
  });

  if (!response.ok) {
    throw new Error('Failed to fetch models');
  }

  const data = await response.json() as any;
  return data.data;
}

/**
 * Отримання деталей моделі за ID
 */
export async function getModelById(id: string): Promise<ModelResponse> {
  const response = await fetch(`/api/models/${id}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    cache: 'no-store', // Вимкнення кешування для отримання актуальних даних
  });

  if (!response.ok) {
    throw new Error('Failed to fetch model details');
  }

  const data = await response.json() as any;
  return data.data;
}

/**
 * Створення нової моделі
 */
export async function createModel(modelData: Partial<Model>): Promise<Model> {
  const response = await fetch('/api/models', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(modelData),
  });

  if (!response.ok) {
    throw new Error('Failed to create model');
  }

  const data = await response.json() as any;
  return data.data.model;
}

/**
 * Оновлення моделі
 */
export async function updateModel(id: string, modelData: Partial<Model>): Promise<Model> {
  const response = await fetch(`/api/models/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(modelData),
  });

  if (!response.ok) {
    throw new Error('Failed to update model');
  }

  const data = await response.json() as any;
  return data.data.model;
}

/**
 * Видалення моделі
 */
export async function deleteModel(id: string): Promise<void> {
  const response = await fetch(`/api/models/${id}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error('Failed to delete model');
  }
}
