# Cloudflare Durable Objects для 3D Marketplace

## Огляд

Цей проект використовує Cloudflare Durable Objects для управління завантаженнями моделей, забезпечуючи надійність, масштабованість та консистентність даних.

## Архітектура

### DownloadManager Durable Object

`DownloadManager` - це основний Durable Object, який управляє сесіями завантаження моделей.

#### Основні функції:
- Створення та управління сесіями завантаження
- Перевірка прав доступу до моделей
- Генерація підписаних URL для завантаження
- Автоматичне очищення застарілих сесій
- Логування спроб завантаження

#### Ключові особливості:
- **Консистентність**: Кожна модель має свій власний Durable Object
- **Надійність**: Стан зберігається в persistent storage
- **Масштабованість**: Автоматичне масштабування на основі навантаження
- **Безпека**: Контроль доступу та обмеження спроб

## Використання

### 1. Створення сесії завантаження

```typescript
// POST /api/models/download
const response = await fetch('/api/models/download', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ modelId: 'model-123' })
});

const result = await response.json();
// { success: true, sessionId: 'uuid', expiresAt: timestamp }
```

### 2. Перевірка статусу сесії

```typescript
// GET /api/models/download?sessionId=uuid&modelId=model-123
const response = await fetch(`/api/models/download?sessionId=${sessionId}&modelId=${modelId}`);
const result = await response.json();
// { success: true, session: { status: 'pending|processing|completed|failed' } }
```

### 3. Обробка завантаження

```typescript
// POST /api/models/download/process
const response = await fetch('/api/models/download/process', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ sessionId, modelId })
});

const result = await response.json();
// { success: true, downloadUrl: 'signed-url', fileName: 'model.zip' }
```

## Конфігурація

### wrangler.toml

```toml
[[durable_objects.bindings]]
name = "DOWNLOAD_MANAGER"
class_name = "DownloadManager"
script_name = "3d-marketplace"

[[env.production.d1_databases]]
binding = "DB"
database_name = "3d-marketplace-prod"

[[env.production.r2_buckets]]
binding = "R2_BUCKET"
bucket_name = "3d-marketplace-models-prod"
```

### Environment Variables

```bash
# Stripe
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...

# OAuth
NEXTAUTH_SECRET=your-secret
GITHUB_CLIENT_ID=your-github-id
GITHUB_CLIENT_SECRET=your-github-secret

# Email
EMAIL_SERVER_HOST=smtp.gmail.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=your-email
EMAIL_SERVER_PASSWORD=your-password
```

## Розгортання

### 1. Підготовка

```bash
# Встановлення Wrangler CLI
npm install -g wrangler

# Авторизація
wrangler login
```

### 2. Автоматичне розгортання

```bash
# Розгортання в development
./scripts/deploy-cloudflare.sh development

# Розгортання в production
./scripts/deploy-cloudflare.sh production
```

### 3. Ручне розгортання

```bash
# Створення D1 бази даних
wrangler d1 create 3d-marketplace-prod

# Застосування міграцій
wrangler d1 execute 3d-marketplace-prod --file=schema.sql

# Створення R2 bucket
wrangler r2 bucket create 3d-marketplace-models-prod

# Створення KV namespace
wrangler kv:namespace create 3d-marketplace-cache-prod

# Розгортання Worker
wrangler deploy --env=production
```

## Моніторинг та Логування

### 1. Перегляд логів

```bash
# Реальний час
wrangler tail --env=production

# Фільтрація по рівню
wrangler tail --env=production --level=error
```

### 2. Метрики

```bash
# Аналітика
wrangler analytics --env=production

# Використання ресурсів
wrangler metrics --env=production
```

### 3. Health Check

```bash
# Перевірка стану
curl https://your-worker.your-subdomain.workers.dev/health
```

## Безпека

### 1. Контроль доступу

- Перевірка авторизації користувача
- Валідація прав на модель (власник, покупець, безкоштовна)
- Обмеження кількості спроб завантаження

### 2. Підписані URL

- Тимчасові URL з обмеженим терміном дії
- Захист від несанкціонованого доступу
- Автоматичне видалення застарілих посилань

### 3. Rate Limiting

- Обмеження кількості запитів на користувача
- Захист від DDoS атак
- Автоматичне блокування підозрілої активності

## Оптимізація

### 1. Кешування

```typescript
// Кешування в KV
await env.CACHE_KV.put(key, value, { expirationTtl: 3600 });
const cached = await env.CACHE_KV.get(key);
```

### 2. Аналітика

```typescript
// Трекінг подій
env.ANALYTICS.writeDataPoint({
  blobs: ['download', userId, modelId],
  doubles: [timestamp, fileSize],
  indexes: [sessionId]
});
```

### 3. Background Tasks

```typescript
// Фонові завдання
await env.BACKGROUND_QUEUE.send({
  type: 'cleanup_downloads',
  data: { olderThan: Date.now() - 3600000 }
});
```

## Troubleshooting

### Поширені проблеми

1. **Durable Object не відповідає**
   - Перевірте конфігурацію в wrangler.toml
   - Переконайтеся, що клас експортується правильно

2. **Помилки доступу до D1**
   - Перевірте binding в wrangler.toml
   - Застосуйте міграції: `wrangler d1 execute DB --file=schema.sql`

3. **R2 файли недоступні**
   - Перевірте права доступу до bucket
   - Налаштуйте CORS для веб-доступу

### Корисні команди

```bash
# Перезапуск Durable Object
wrangler durable-objects restart --name=DOWNLOAD_MANAGER

# Очищення KV cache
wrangler kv:key delete --namespace-id=ID --key=KEY

# Перегляд D1 даних
wrangler d1 execute DB --command="SELECT * FROM models LIMIT 10"
```

## Подальший розвиток

### Планові покращення

1. **Розширена аналітика**
   - Детальні метрики завантажень
   - A/B тестування функцій
   - Прогнозування навантаження

2. **Покращена безпека**
   - Двофакторна аутентифікація
   - Шифрування файлів
   - Аудит безпеки

3. **Нові функції**
   - Потокове завантаження
   - Резюмування перерваних завантажень
   - Пакетне завантаження

## Підтримка

Для отримання допомоги:
1. Перевірте документацію Cloudflare
2. Створіть issue в репозиторії
3. Зверніться до команди розробки
