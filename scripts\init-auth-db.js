#!/usr/bin/env node

/**
 * Скрипт для ініціалізації D1 бази даних для аутентифікації
 * Створює всі необхідні таблиці для NextAuth та користувачів
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Кольори для консолі
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function execCommand(command, description) {
  try {
    log(`\n${description}...`, 'cyan');
    const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    log(`✅ ${description} completed`, 'green');
    return output;
  } catch (error) {
    log(`❌ ${description} failed: ${error.message}`, 'red');
    throw error;
  }
}

async function main() {
  log('🚀 Ініціалізація D1 бази даних для аутентифікації', 'bright');
  
  try {
    // Перевіряємо, чи існує wrangler.toml
    const wranglerPath = path.join(process.cwd(), 'wrangler.toml');
    if (!fs.existsSync(wranglerPath)) {
      throw new Error('wrangler.toml не знайдено. Переконайтеся, що ви в корені проекту.');
    }

    // Читаємо SQL схему
    const schemaPath = path.join(process.cwd(), 'src/lib/auth/schema.sql');
    if (!fs.existsSync(schemaPath)) {
      throw new Error('schema.sql не знайдено в src/lib/auth/');
    }

    const schema = fs.readFileSync(schemaPath, 'utf8');
    log('📄 SQL схема завантажена', 'green');

    // Створюємо D1 бази даних для різних середовищ
    const environments = ['development', 'staging', 'production'];
    
    for (const env of environments) {
      log(`\n🔧 Налаштування бази даних для ${env}...`, 'yellow');
      
      const dbName = `3d-marketplace-${env}`;
      
      try {
        // Створюємо базу даних
        const createOutput = execCommand(
          `wrangler d1 create ${dbName}`,
          `Створення D1 бази даних ${dbName}`
        );
        
        // Витягуємо database_id з виводу
        const dbIdMatch = createOutput.match(/database_id = "([^"]+)"/);
        if (dbIdMatch) {
          const databaseId = dbIdMatch[1];
          log(`📝 Database ID для ${env}: ${databaseId}`, 'blue');
          
          // Оновлюємо wrangler.toml з правильним ID
          updateWranglerToml(env, databaseId);
        }
        
      } catch (error) {
        if (error.message.includes('already exists')) {
          log(`⚠️  База даних ${dbName} вже існує`, 'yellow');
        } else {
          throw error;
        }
      }

      // Застосовуємо схему до бази даних
      try {
        execCommand(
          `wrangler d1 execute ${dbName} --file=${schemaPath}`,
          `Застосування схеми до ${dbName}`
        );
      } catch (error) {
        log(`⚠️  Можливо, схема вже застосована до ${dbName}`, 'yellow');
      }
    }

    // Створюємо KV namespaces
    log('\n🗄️  Створення KV namespaces...', 'yellow');
    
    try {
      const kvOutput = execCommand(
        'wrangler kv:namespace create "CACHE_KV"',
        'Створення KV namespace для кешування'
      );
      
      const kvIdMatch = kvOutput.match(/id = "([^"]+)"/);
      if (kvIdMatch) {
        log(`📝 KV Namespace ID: ${kvIdMatch[1]}`, 'blue');
      }
      
      // Створюємо preview namespace
      const kvPreviewOutput = execCommand(
        'wrangler kv:namespace create "CACHE_KV" --preview',
        'Створення KV preview namespace'
      );
      
      const kvPreviewIdMatch = kvPreviewOutput.match(/preview_id = "([^"]+)"/);
      if (kvPreviewIdMatch) {
        log(`📝 KV Preview Namespace ID: ${kvPreviewIdMatch[1]}`, 'blue');
      }
      
    } catch (error) {
      if (error.message.includes('already exists')) {
        log('⚠️  KV namespace вже існує', 'yellow');
      } else {
        throw error;
      }
    }

    // Створюємо R2 buckets
    log('\n🪣 Створення R2 buckets...', 'yellow');
    
    for (const env of environments) {
      const bucketName = `3d-marketplace-models-${env}`;
      
      try {
        execCommand(
          `wrangler r2 bucket create ${bucketName}`,
          `Створення R2 bucket ${bucketName}`
        );
      } catch (error) {
        if (error.message.includes('already exists')) {
          log(`⚠️  R2 bucket ${bucketName} вже існує`, 'yellow');
        } else {
          throw error;
        }
      }
    }

    log('\n✅ Ініціалізація завершена успішно!', 'green');
    log('\n📋 Наступні кроки:', 'bright');
    log('1. Оновіть wrangler.toml з правильними ID', 'cyan');
    log('2. Додайте секрети: wrangler secret put GOOGLE_CLIENT_SECRET', 'cyan');
    log('3. Додайте секрети: wrangler secret put GITHUB_CLIENT_SECRET', 'cyan');
    log('4. Додайте секрети: wrangler secret put NEXTAUTH_SECRET', 'cyan');
    log('5. Запустіть проект: npm run dev', 'cyan');

  } catch (error) {
    log(`\n❌ Помилка ініціалізації: ${error.message}`, 'red');
    process.exit(1);
  }
}

function updateWranglerToml(env, databaseId) {
  const wranglerPath = path.join(process.cwd(), 'wrangler.toml');
  let content = fs.readFileSync(wranglerPath, 'utf8');
  
  // Замінюємо placeholder ID на реальний
  const placeholder = `your-${env}-database-id`;
  if (content.includes(placeholder)) {
    content = content.replace(placeholder, databaseId);
    fs.writeFileSync(wranglerPath, content);
    log(`📝 Оновлено wrangler.toml для ${env}`, 'green');
  }
}

// Запускаємо скрипт
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
