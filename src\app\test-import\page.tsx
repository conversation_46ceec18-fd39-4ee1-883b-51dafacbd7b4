'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Download, ArrowLeft, FileText, Package, CheckCircle } from 'lucide-react';

export default function TestImportPage() {
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadComplete, setDownloadComplete] = useState(false);

  const handleDownload = async () => {
    setIsDownloading(true);
    
    // Симуляція завантаження
    setTimeout(() => {
      setIsDownloading(false);
      setDownloadComplete(true);
      
      // Скидання стану через 3 секунди
      setTimeout(() => {
        setDownloadComplete(false);
      }, 3000);
    }, 2000);
  };

  return (
    <main className="min-h-screen bg-background">
      <div className="container mx-auto py-8 px-4">
        {/* Навігація назад */}
        <div className="mb-6">
          <Link href="/" className="flex items-center text-muted-foreground hover:text-primary transition-colors">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Назад на головну
          </Link>
        </div>

        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                <Package className="h-8 w-8 text-primary" />
              </div>
              <CardTitle className="text-2xl">Безкоштовне завантаження</CardTitle>
              <CardDescription>
                Завантажте високоякісну 3D модель Terminator T800 Armor безкоштовно
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Інформація про модель */}
              <div className="bg-muted/50 rounded-lg p-4">
                <h3 className="font-semibold mb-2">Деталі моделі:</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Формат:</span>
                    <span className="ml-2 font-medium">STL</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Розмір:</span>
                    <span className="ml-2 font-medium">15.2 MB</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Категорія:</span>
                    <span className="ml-2 font-medium">Косплей</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Ліцензія:</span>
                    <span className="ml-2 font-medium">Creative Commons</span>
                  </div>
                </div>
              </div>

              {/* Рекомендації для друку */}
              <div className="bg-blue-50 dark:bg-blue-950/20 rounded-lg p-4">
                <h3 className="font-semibold mb-2 text-blue-900 dark:text-blue-100">
                  Рекомендації для друку:
                </h3>
                <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                  <li>• Матеріал: PLA або PETG</li>
                  <li>• Висота шару: 0.2mm</li>
                  <li>• Заповнення: 20%</li>
                  <li>• Підтримки: Мінімальні</li>
                  <li>• Час друку: ~8-10 годин</li>
                </ul>
              </div>

              {/* Кнопка завантаження */}
              <div className="text-center">
                {downloadComplete ? (
                  <div className="flex items-center justify-center text-green-600 dark:text-green-400">
                    <CheckCircle className="h-5 w-5 mr-2" />
                    <span className="font-medium">Завантаження завершено!</span>
                  </div>
                ) : (
                  <Button 
                    onClick={handleDownload}
                    disabled={isDownloading}
                    size="lg"
                    className="w-full sm:w-auto"
                  >
                    {isDownloading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Завантаження...
                      </>
                    ) : (
                      <>
                        <Download className="h-4 w-4 mr-2" />
                        Завантажити безкоштовно
                      </>
                    )}
                  </Button>
                )}
              </div>

              {/* Додаткова інформація */}
              <div className="text-center text-sm text-muted-foreground">
                <p>
                  Завантажуючи цю модель, ви погоджуєтесь з{' '}
                  <Link href="/terms" className="text-primary hover:underline">
                    умовами використання
                  </Link>
                </p>
              </div>

              {/* Посилання на повну версію */}
              <div className="border-t pt-4">
                <div className="text-center">
                  <p className="text-sm text-muted-foreground mb-2">
                    Хочете побачити більше деталей?
                  </p>
                  <Link href="/models/printables_1286204_terminator_t800">
                    <Button variant="outline">
                      <FileText className="h-4 w-4 mr-2" />
                      Переглянути повну інформацію
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </main>
  );
}
