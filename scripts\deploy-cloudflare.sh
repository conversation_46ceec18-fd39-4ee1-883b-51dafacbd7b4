#!/bin/bash

# Скрипт для розгортання 3D Marketplace на Cloudflare
# Використання: ./scripts/deploy-cloudflare.sh [environment]
# environment: development, staging, production (за замовчуванням: development)

set -e

# Кольори для виводу
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Функція для виводу кольорових повідомлень
log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Перевірка аргументів
ENVIRONMENT=${1:-development}

if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
    error "Невірне середовище. Використовуйте: development, staging, або production"
    exit 1
fi

log "Розгортання 3D Marketplace в середовищі: $ENVIRONMENT"

# Перевірка наявності wrangler
if ! command -v wrangler &> /dev/null; then
    error "Wrangler CLI не знайдено. Встановіть його: npm install -g wrangler"
    exit 1
fi

# Перевірка авторизації
if ! wrangler whoami &> /dev/null; then
    error "Ви не авторизовані в Cloudflare. Виконайте: wrangler login"
    exit 1
fi

# Створення D1 бази даних (якщо не існує)
log "Перевірка D1 бази даних..."
DB_NAME="3d-marketplace-$ENVIRONMENT"

if ! wrangler d1 list | grep -q "$DB_NAME"; then
    log "Створення D1 бази даних: $DB_NAME"
    wrangler d1 create "$DB_NAME"
    
    # Отримання ID бази даних
    DB_ID=$(wrangler d1 list | grep "$DB_NAME" | awk '{print $2}')
    warn "Оновіть wrangler.toml з ID бази даних: $DB_ID"
else
    log "D1 база даних вже існує: $DB_NAME"
fi

# Застосування міграцій до D1
log "Застосування міграцій до D1..."
if [ -f "schema.sql" ]; then
    wrangler d1 execute "$DB_NAME" --file=schema.sql
    log "Міграції застосовано успішно"
else
    warn "Файл schema.sql не знайдено"
fi

# Створення R2 bucket (якщо не існує)
log "Перевірка R2 bucket..."
BUCKET_NAME="3d-marketplace-models-$ENVIRONMENT"

if ! wrangler r2 bucket list | grep -q "$BUCKET_NAME"; then
    log "Створення R2 bucket: $BUCKET_NAME"
    wrangler r2 bucket create "$BUCKET_NAME"
else
    log "R2 bucket вже існує: $BUCKET_NAME"
fi

# Створення KV namespace (якщо не існує)
log "Перевірка KV namespace..."
KV_NAME="3d-marketplace-cache-$ENVIRONMENT"

if ! wrangler kv:namespace list | grep -q "$KV_NAME"; then
    log "Створення KV namespace: $KV_NAME"
    wrangler kv:namespace create "$KV_NAME"
    
    # Отримання ID namespace
    KV_ID=$(wrangler kv:namespace list | grep "$KV_NAME" | jq -r '.id')
    warn "Оновіть wrangler.toml з ID KV namespace: $KV_ID"
else
    log "KV namespace вже існує: $KV_NAME"
fi

# Створення Queue (якщо не існує)
log "Перевірка Queue..."
QUEUE_NAME="3d-marketplace-tasks-$ENVIRONMENT"

if ! wrangler queues list | grep -q "$QUEUE_NAME"; then
    log "Створення Queue: $QUEUE_NAME"
    wrangler queues create "$QUEUE_NAME"
else
    log "Queue вже існує: $QUEUE_NAME"
fi

# Налаштування секретів (тільки для production)
if [ "$ENVIRONMENT" = "production" ]; then
    log "Налаштування секретів для production..."
    
    # Перевірка наявності секретів
    SECRETS=(
        "STRIPE_SECRET_KEY"
        "STRIPE_WEBHOOK_SECRET"
        "NEXTAUTH_SECRET"
        "GITHUB_CLIENT_ID"
        "GITHUB_CLIENT_SECRET"
        "GOOGLE_CLIENT_ID"
        "GOOGLE_CLIENT_SECRET"
        "EMAIL_SERVER_HOST"
        "EMAIL_SERVER_PORT"
        "EMAIL_SERVER_USER"
        "EMAIL_SERVER_PASSWORD"
        "EMAIL_FROM"
    )
    
    for secret in "${SECRETS[@]}"; do
        if [ -n "${!secret}" ]; then
            echo "${!secret}" | wrangler secret put "$secret" --env="$ENVIRONMENT"
            log "Секрет $secret встановлено"
        else
            warn "Змінна середовища $secret не встановлена"
        fi
    done
fi

# Збірка проекту
log "Збірка проекту..."
npm run build

# Розгортання Worker
log "Розгортання Worker..."
wrangler deploy --env="$ENVIRONMENT"

# Перевірка розгортання
log "Перевірка розгортання..."
WORKER_URL=$(wrangler subdomain | grep -o 'https://[^/]*')

if [ -n "$WORKER_URL" ]; then
    log "Перевірка health endpoint..."
    if curl -s "$WORKER_URL/health" | grep -q "healthy"; then
        log "✅ Розгортання успішне!"
        info "Worker URL: $WORKER_URL"
    else
        error "❌ Health check не пройшов"
        exit 1
    fi
else
    warn "Не вдалося отримати URL Worker"
fi

# Додаткові налаштування для production
if [ "$ENVIRONMENT" = "production" ]; then
    log "Налаштування custom domain (опціонально)..."
    # wrangler route add "yourdomain.com/*" --zone-id="your-zone-id"
    
    log "Налаштування Analytics Engine..."
    # Додаткові налаштування для аналітики
fi

log "🎉 Розгортання завершено успішно!"
log "Середовище: $ENVIRONMENT"
log "Наступні кроки:"
log "1. Оновіть DNS записи (якщо потрібно)"
log "2. Налаштуйте custom domain"
log "3. Перевірте всі функції"

# Показати корисні команди
info "Корисні команди:"
info "  Перегляд логів: wrangler tail --env=$ENVIRONMENT"
info "  Оновлення секретів: wrangler secret put SECRET_NAME --env=$ENVIRONMENT"
info "  Перегляд метрик: wrangler analytics --env=$ENVIRONMENT"
