'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Loader2, RefreshCw, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';
import { PlatformHealth, ModelSource } from '@/types/models';

interface HealthData {
  status: 'healthy' | 'degraded' | 'down';
  timestamp: string;
  platforms: Record<ModelSource, PlatformHealth>;
  overall: {
    totalPlatforms: number;
    operationalPlatforms: number;
    degradedPlatforms: number;
    downPlatforms: number;
  };
}

export const ScrapingHealthStatus: React.FC = () => {
  const [healthData, setHealthData] = useState<HealthData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const platforms: Record<string, { name: string; icon: string }> = {
    printables: { name: 'Printables', icon: '🖨️' },
    makerworld: { name: 'MakerWorld', icon: '🌍' },
    thangs: { name: 'Thangs', icon: '🔧' },
  };

  const fetchHealthData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/scraping/health');
      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to fetch health data');
      }

      setHealthData(result.data);
      setLastUpdated(new Date());
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchHealthData();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchHealthData, 30000);
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (status: PlatformHealth['status']) => {
    switch (status) {
      case 'operational':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'degraded':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'down':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <XCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: PlatformHealth['status']) => {
    switch (status) {
      case 'operational':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'degraded':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'down':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const formatResponseTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const formatLastUpdated = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const seconds = Math.floor(diff / 1000);
    
    if (seconds < 60) return `${seconds}s ago`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h ago`;
  };

  if (isLoading && !healthData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            Loading Health Status...
          </CardTitle>
        </CardHeader>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <XCircle className="h-4 w-4" />
            Health Check Failed
          </CardTitle>
          <CardDescription>{error}</CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={fetchHealthData} variant="outline" className="gap-2">
            <RefreshCw className="h-4 w-4" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!healthData) return null;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              {getStatusIcon(healthData.status)}
              Scraping System Health
              <Badge 
                variant="outline" 
                className={getStatusColor(healthData.status)}
              >
                {healthData.status.toUpperCase()}
              </Badge>
            </CardTitle>
            <CardDescription>
              {lastUpdated && `Last updated: ${formatLastUpdated(lastUpdated)}`}
            </CardDescription>
          </div>
          <Button 
            onClick={fetchHealthData} 
            variant="ghost" 
            size="sm"
            disabled={isLoading}
            className="gap-2"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Overall Statistics */}
        <div className="grid grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold">{healthData.overall.totalPlatforms}</div>
            <div className="text-sm text-gray-600">Total</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-600">{healthData.overall.operationalPlatforms}</div>
            <div className="text-sm text-gray-600">Operational</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-yellow-600">{healthData.overall.degradedPlatforms}</div>
            <div className="text-sm text-gray-600">Degraded</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-red-600">{healthData.overall.downPlatforms}</div>
            <div className="text-sm text-gray-600">Down</div>
          </div>
        </div>

        {/* Platform Details */}
        <div className="space-y-3">
          <h4 className="font-medium">Platform Status</h4>
          {Object.entries(healthData.platforms)
            .filter(([platform]) => ['printables', 'makerworld', 'thangs'].includes(platform))
            .map(([platform, health]) => (
              <div key={platform} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  {getStatusIcon(health.status)}
                  <div>
                    <div className="flex items-center gap-2">
                      <span>{platforms[platform]?.icon}</span>
                      <span className="font-medium">{platforms[platform]?.name}</span>
                      <Badge 
                        variant="outline" 
                        className={`text-xs ${getStatusColor(health.status)}`}
                      >
                        {health.status}
                      </Badge>
                    </div>
                    <div className="text-sm text-gray-600">
                      Response: {formatResponseTime(health.responseTime)}
                    </div>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="text-sm font-medium">
                    Rate Limit: {health.rateLimitRemaining}/min
                  </div>
                  <Progress 
                    value={(health.rateLimitRemaining / 15) * 100} 
                    className="w-20 h-2 mt-1"
                  />
                </div>
              </div>
            ))}
        </div>

        {/* System Information */}
        <div className="text-xs text-gray-500 pt-2 border-t">
          <div>System timestamp: {new Date(healthData.timestamp).toLocaleString()}</div>
          <div>Auto-refresh: Every 30 seconds</div>
        </div>
      </CardContent>
    </Card>
  );
};
