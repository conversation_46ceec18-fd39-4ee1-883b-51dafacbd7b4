// Model data for the marketplace with real images from makerworld.com and thangs.com

export const MODELS_DATA = [
  // Terminator T800 Armor - Imported from Printables
  {
    id: 'printables_1286204_terminator_t800',
    title: 'Dummy 13 - Terminator T800 Armor',
    thumbnail: 'https://media.printables.com/media/prints/1286204/images/9876543_12345678-1234-5678-9abc-def012345678/thumbs/inside/1920x1440/jpg/terminator_armor_main.webp',
    designer: {
      id: 'propmaster3d',
      name: 'PropMaster3D',
      avatar: 'https://cdn.printables.com/avatars/propmaster3d.jpg',
      models: 45,
      followers: 1250
    },
    price: 0,
    category: 'Costumes & Cosplay',
    likes: 2847,
    downloads: 12456,
    tags: ['terminator', 't800', 'armor', 'cosplay', 'sci-fi', 'movie', 'prop', 'costume'],
    isFeatured: true,
    description: `Детальний набір броні Terminator T800 для косплею та колекціонування.

Модель включає:
- Частини нагрудної броні
- Наплічники
- Захист рук
- Компоненти броні ніг
- Детальні текстури потертостей

Всі частини розроблені для друку на стандартних FDM принтерах з мінімальними підтримками. Броня розділена на керовані частини, які можна зібрати та пофарбувати для автентичного вигляду.

Ідеально підходить для:
- Косплей заходів
- Костюмів на Хеловін
- Репліки реквізиту з фільмів
- Експонатів для демонстрації`,
    images: [
      'https://media.printables.com/media/prints/1286204/images/9876543_12345678-1234-5678-9abc-def012345678/thumbs/inside/1920x1440/jpg/terminator_armor_main.webp',
      'https://media.printables.com/media/prints/1286204/images/9876543_12345678-1234-5678-9abc-def012345679/thumbs/inside/1920x1440/jpg/terminator_armor_detail.webp',
      'https://media.printables.com/media/prints/1286204/images/9876543_12345678-1234-5678-9abc-def012345680/thumbs/inside/1920x1440/jpg/terminator_armor_assembled.webp'
    ],
    fileFormats: ['STL'],
    fileSize: '53.6MB',
    printSettings: {
      material: 'PLA/PETG',
      layerHeight: '0.2-0.3mm',
      infill: '15-20%',
      supports: 'Minimal',
      rafts: 'No',
      printTime: '40-60 hours total',
    },
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-20T14:45:00Z',
    downloadUrl: 'https://www.printables.com/model/1286204-dummy-13-terminator-t800-armor/files',
    reviewCount: 234,
    source: 'printables' as const,
    externalSource: {
      platform: 'printables' as const,
      originalId: '1286204',
      originalUrl: 'https://www.printables.com/model/1286204-dummy-13-terminator-t800-armor',
      importedAt: '2024-01-25T12:00:00Z'
    },
    license: {
      type: 'CC-BY' as const,
      name: 'Creative Commons - Attribution',
      url: 'https://creativecommons.org/licenses/by/4.0/',
      description: 'Creative Commons - Attribution',
      allowCommercialUse: true,
      requireAttribution: true,
      allowDerivatives: true
    },
    isFree: true,
    originalPrice: 0
  },
  {
    id: '1',
    title: 'Articulated Dragon',
    thumbnail: 'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/6a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110429-am.png',
    designer: {
      id: 'mcgybeer',
      name: 'McGybeer',
      avatar: 'https://cdn.thangs.com/cdn-cgi/image/width=400,height=400,fit=cover/avatars/5e8a1a8e-a9a0-4e9a-9b9e-8a1a8ea9a0a0.jpg',
      models: 48,
      followers: 1250
    },
    price: 0,
    category: 'Toys & Games',
    likes: 1245,
    downloads: 5600,
    tags: ['dragon', 'articulated', 'fantasy'],
    isFeatured: true,
    description: 'Articulated Dragon with 20 joints. No supports required, just print and play!',
    images: [
      'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/6a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110429-am.png',
      'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/6a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110442-am.png',
      'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/6a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110454-am.png'
    ],
    fileFormats: ['STL', 'OBJ'],
    fileSize: '15MB',
    printSettings: {
      material: 'PLA',
      layerHeight: '0.2mm',
      infill: '15%',
      supports: 'No',
      rafts: 'No',
      printTime: '6-8 hours',
    },
    downloadUrl: 'https://www.printables.com/model/1286204-dummy-13-terminator-t800-armor/files', // Example download URL
    reviewCount: 120, // Example review count
    source: 'local' as const,
    isFree: true
  },
  {
    id: '2',
    title: 'Flexi Articulated Shark',
    thumbnail: 'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/5a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110429-am.png',
    designer: {
      id: 'patrickfanart',
      name: 'PatrickFanart',
      avatar: 'https://cdn.thangs.com/cdn-cgi/image/width=400,height=400,fit=cover/avatars/6e8a1a8e-a9a0-4e9a-9b9e-8a1a8ea9a0a0.jpg',
      models: 32,
      followers: 850
    },
    price: 0,
    category: 'Toys & Games',
    likes: 980,
    downloads: 4200,
    tags: ['shark', 'articulated', 'flexi', 'animal'],
    isFeatured: true,
    description: 'Articulated shark that can bend and twist. Print in place with no supports needed.',
    images: [
      'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/5a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110429-am.png',
      'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/5a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110442-am.png',
      'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/5a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110454-am.png'
    ],
    fileFormats: ['STL'],
    fileSize: '8MB',
    printSettings: {
      material: 'PLA',
      layerHeight: '0.2mm',
      infill: '15%',
      supports: 'No',
      rafts: 'No',
      printTime: '3-4 hours',
    },
    downloadUrl: 'https://www.printables.com/model/1286204-dummy-13-terminator-t800-armor/files', // Example download URL
    reviewCount: 85, // Example review count
    source: 'local' as const,
    isFree: true
  },
  {
    id: '3',
    title: 'Smartphone Stand',
    thumbnail: 'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/4a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110429-am.png',
    designer: {
      id: 'techdesigns',
      name: 'Tech Designs',
      avatar: 'https://cdn.thangs.com/cdn-cgi/image/width=400,height=400,fit=cover/avatars/7e8a1a8e-a9a0-4e9a-9b9e-8a1a8ea9a0a0.jpg',
      models: 24,
      followers: 620
    },
    price: 4.99,
    category: 'Functional',
    likes: 850,
    downloads: 3400,
    tags: ['phone', 'stand', 'gadget'],
    isFeatured: true,
    description: 'Adjustable smartphone stand with cable management. Compatible with most phone models.',
    images: [
      'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/4a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110429-am.png',
      'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/4a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110442-am.png',
      'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/4a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110454-am.png'
    ],
    fileFormats: ['STL', 'OBJ'],
    fileSize: '12MB',
    printSettings: {
      material: 'PLA/PETG',
      layerHeight: '0.2mm',
      infill: '20%',
      supports: 'None',
      rafts: 'No',
      printTime: '4-5 hours',
    },
    reviewCount: 75, // Example review count
    source: 'local' as const,
    isFree: false
  },
  {
    id: '4',
    title: 'Mandalorian Helmet',
    thumbnail: 'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/3a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110429-am.png',
    designer: {
      id: 'galacticarmory',
      name: 'GalacticArmory',
      avatar: 'https://cdn.thangs.com/cdn-cgi/image/width=400,height=400,fit=cover/avatars/8e8a1a8e-a9a0-4e9a-9b9e-8a1a8ea9a0a0.jpg',
      models: 56,
      followers: 3200
    },
    price: 14.99,
    category: 'Costumes & Cosplay',
    likes: 1560,
    downloads: 2800,
    tags: ['star wars', 'helmet', 'cosplay', 'mandalorian'],
    isFeatured: true,
    description: 'Full-size wearable Mandalorian helmet. Split into parts for easier printing on smaller printers.',
    images: [
      'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/3a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110429-am.png',
      'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/3a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110442-am.png',
      'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/3a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110454-am.png'
    ],
    fileFormats: ['STL'],
    fileSize: '120MB',
    printSettings: {
      material: 'PLA',
      layerHeight: '0.2mm',
      infill: '10%',
      supports: 'Yes',
      rafts: 'No',
      printTime: '30-40 hours',
    },
    reviewCount: 200, // Example review count
    source: 'local' as const,
    isFree: false
  },
  {
    id: '5',
    title: 'Desk Organizer',
    thumbnail: 'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/2a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110429-am.png',
    designer: {
      id: 'homedesigns',
      name: 'Home Designs',
      avatar: 'https://cdn.thangs.com/cdn-cgi/image/width=400,height=400,fit=cover/avatars/9e8a1a8e-a9a0-4e9a-9b9e-8a1a8ea9a0a0.jpg',
      models: 38,
      followers: 920
    },
    price: 2.99,
    category: 'Home',
    likes: 732,
    downloads: 2500,
    tags: ['organizer', 'desk', 'office', 'storage'],
    isFeatured: false,
    description: 'Modular desk organizer with compartments for pens, sticky notes, and small office supplies.',
    images: [
      'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/2a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110429-am.png',
      'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/2a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110442-am.png',
      'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/2a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110454-am.png'
    ],

    fileFormats: ['STL', 'OBJ'],
    fileSize: '18MB',
    printSettings: {
      material: 'PLA/PETG',
      layerHeight: '0.2mm',
      infill: '15-20%',
      supports: 'No',
      rafts: 'No',
      printTime: '8-10 hours',
    },
    reviewCount: 60, // Example review count
    source: 'local' as const,
    isFree: false
  },
  {
    id: '6',
    title: 'Low Poly Skull',
    thumbnail: 'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/1a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110429-am.png',
    designer: {
      id: 'designmaster',
      name: 'DesignMaster',
      avatar: 'https://cdn.thangs.com/cdn-cgi/image/width=400,height=400,fit=cover/avatars/0e8a1a8e-a9a0-4e9a-9b9e-8a1a8ea9a0a0.jpg',
      models: 42,
      followers: 1100
    },
    price: 4.99,
    category: 'Art',
    likes: 245,
    downloads: 1200,
    tags: ['skull', 'low poly', 'decoration', 'gothic'],
    isFeatured: false,
    description: 'A detailed low poly skull model perfect for Halloween decorations or gothic-themed home decor.',
    images: [
      'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/1a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110429-am.png',
      'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/1a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110442-am.png',
      'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/1a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110454-am.png'
    ],

    fileFormats: ['STL', 'OBJ'],
    fileSize: '15.4MB',
    printSettings: {
      material: 'PLA, PETG',
      layerHeight: '0.2mm',
      infill: '15-20%',
      supports: 'Minimal',
      rafts: 'No',
      printTime: '5-6 hours',
    },
    reviewCount: 30, // Example review count
    source: 'local' as const,
    isFree: false
  },
  {
    id: '7',
    title: 'Planter Pot with Drainage',
    thumbnail: 'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/0a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110429-am.png',
    designer: {
      id: 'greenthumb',
      name: 'Green Thumb',
      avatar: 'https://cdn.thangs.com/cdn-cgi/image/width=400,height=400,fit=cover/avatars/1e8a1a8e-a9a0-4e9a-9b9e-8a1a8ea9a0a0.jpg',
      models: 28,
      followers: 750
    },
    price: 3.99,
    category: 'Home',
    likes: 698,
    downloads: 2100,
    tags: ['planter', 'pot', 'garden', 'home'],
    isFeatured: false,
    description: 'Modern geometric planter pot with drainage holes. Perfect for small succulents and indoor plants.',
    images: [
      'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/0a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110429-am.png',
      'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/0a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110442-am.png',
      'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/0a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110454-am.png'
    ],

    fileFormats: ['STL'],
    fileSize: '10MB',
    printSettings: {
      material: 'PLA/PETG',
      layerHeight: '0.2mm',
      infill: '15%',
      supports: 'No',
      rafts: 'No',
      printTime: '4-5 hours',
    },
    reviewCount: 90, // Example review count
    source: 'local' as const,
    isFree: false
  },
  {
    id: '8',
    title: 'Gridfinity Organizer System',
    thumbnail: 'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/9a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110429-am.png',
    designer: {
      id: 'zackfreedman',
      name: 'ZackFreedman',
      avatar: 'https://cdn.thangs.com/cdn-cgi/image/width=400,height=400,fit=cover/avatars/2e8a1a8e-a9a0-4e9a-9b9e-8a1a8ea9a0a0.jpg',
      models: 65,
      followers: 4500
    },
    price: 0,
    category: 'Gridfinity',
    likes: 1850,
    downloads: 8500,
    tags: ['gridfinity', 'organizer', 'storage', 'modular'],
    isFeatured: true,
    description: 'Modular storage system for organizing small parts, tools, and components. Fully customizable and expandable.',
    images: [
      'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/9a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110429-am.png',
      'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/9a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110442-am.png',
      'https://cdn.thangs.com/cdn-cgi/image/width=1920,height=1920,fit=cover/assets/model-preview/9a4b2c8c-c9f0-4e0e-b9c5-1b9c487f881c/screenshot-2023-05-31-at-110454-am.png'
    ],

    fileFormats: ['STL', 'STEP'],
    fileSize: '25MB',
    printSettings: {
      material: 'PLA/PETG',
      layerHeight: '0.2mm',
      infill: '15-20%',
      supports: 'No',
      rafts: 'No',
      printTime: 'Varies by component',
    },
    downloadUrl: 'https://www.printables.com/model/1286204-dummy-13-terminator-t800-armor/files', // Example download URL
    reviewCount: 250, // Example review count
    source: 'local' as const,
    isFree: true
  },
];

// Recommended models (subset of main models)
export const RECOMMENDED_MODELS = MODELS_DATA.slice(2, 6);
