'use client';

import { useState } from 'react';
import { <PERSON>Viewer } from '@/components/model-viewer/model-viewer';
import { CategoryExplorer } from '@/components/model-viewer/category-explorer';
import { PrintSimulation } from '@/components/model-viewer/print-simulation';
import { ModelComparison } from '@/components/model-viewer/model-comparison';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from '@/components/ui/button';
import Link from 'next/link';

// Sample data
const sampleModel = {
  id: '1',
  name: 'Robot Hunter',
  description: 'A fully Articulated Dragon model with movable joints. Perfect for testing your printer\'s capabilities and as a desk toy.',
  splineSceneUrl: 'https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode',
  thumbnailUrl: '/models/dragon-thumbnail.jpg',
  fileSize: '15.2 MB',
  fileFormat: 'STL',
  category: 'Toys',
  tags: ['Dragon', 'Articulated', 'Fantasy', 'Desk Toy'],
  creator: {
    name: '<PERSON>Master3D',
    avatarUrl: '/avatars/creator1.jpg'
  },
  price: 'Free' as const,
  downloadCount: 12543,
  createdAt: '2023-05-15'
};

const sampleModelB = {
  id: '2',
  name: 'Dragon Lite',
  description: 'A simplified version of the Articulated Dragon with fewer moving parts but faster print time.',
  splineSceneUrl: 'https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode',
  thumbnailUrl: '/models/dragon-lite-thumbnail.jpg',
  fileSize: '8.7 MB',
  fileFormat: 'STL',
  category: 'Toys',
  tags: ['Dragon', 'Fantasy', 'Easy Print'],
  creator: {
    name: 'DragonMaster3D',
    avatarUrl: '/avatars/creator1.jpg'
  },
  price: 'Free' as const,
  downloadCount: 8721,
  createdAt: '2023-06-22'
};

const comparisonModelA = {
  id: '1',
  name: 'Articulated Dragon',
  splineSceneUrl: 'https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode',
  fileSize: '15.2 MB',
  printTime: '8h 45m',
  materialUsage: '120g',
  resolution: 'High (0.1mm)',
  supportRequired: true,
  complexity: 'High' as const,
  price: 'Free' as const
};

const comparisonModelB = {
  id: '2',
  name: 'Dragon Lite',
  splineSceneUrl: 'https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode',
  fileSize: '8.7 MB',
  printTime: '5h 20m',
  materialUsage: '85g',
  resolution: 'Medium (0.2mm)',
  supportRequired: false,
  complexity: 'Medium' as const,
  price: 'Free' as const
};

const categories = [
  {
    id: 'toys',
    name: 'Toys & Games',
    count: 1245,
    description: 'Fun toys, games, and articulated models for entertainment and play.',
    splineObjectName: 'toys_category'
  },
  {
    id: 'home',
    name: 'Home Decor',
    count: 987,
    description: 'Beautiful decorative items to enhance your living space.',
    splineObjectName: 'home_category'
  },
  {
    id: 'gadgets',
    name: 'Gadgets & Electronics',
    count: 654,
    description: 'Useful gadgets, electronic enclosures, and functional items.',
    splineObjectName: 'gadgets_category'
  },
  {
    id: 'art',
    name: 'Art & Sculptures',
    count: 1532,
    description: 'Artistic creations, sculptures, and decorative pieces.',
    splineObjectName: 'art_category'
  },
  {
    id: 'tools',
    name: 'Tools & Utilities',
    count: 876,
    description: 'Practical tools, fixtures, and utility items for everyday use.',
    splineObjectName: 'tools_category'
  }
];

export default function SplineIntegrationPage() {
  const [selectedCategory, setSelectedCategory] = useState(categories[0]);

  const handleCategorySelect = (category: typeof categories[0]) => {
    setSelectedCategory(category);
    console.log(`Selected category: ${category.name}`);
  };

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-2">3D Model Marketplace with Spline</h1>
      <p className="text-muted-foreground mb-8">
        Interactive 3D experiences powered by Spline integration
      </p>

      <Tabs defaultValue="model-viewer" className="space-y-8">
        <TabsList className="grid grid-cols-4 w-full max-w-3xl mx-auto">
          <TabsTrigger value="model-viewer">Model Viewer</TabsTrigger>
          <TabsTrigger value="category-explorer">Category Explorer</TabsTrigger>
          <TabsTrigger value="print-simulation">Print Simulation</TabsTrigger>
          <TabsTrigger value="model-comparison">Model Comparison</TabsTrigger>
        </TabsList>

        <TabsContent value="model-viewer" className="space-y-4">
          <h2 className="text-2xl font-semibold">Enhanced Model Viewer</h2>
          <p className="text-muted-foreground mb-4">
            An interactive 3D model viewer with detailed information and controls.
          </p>
          <ModelViewer model={sampleModel} />
        </TabsContent>

        <TabsContent value="category-explorer" className="space-y-4">
          <h2 className="text-2xl font-semibold">3D Category Explorer</h2>
          <p className="text-muted-foreground mb-4">
            Navigate through model categories in an immersive 3D environment.
          </p>
          <CategoryExplorer
            splineSceneUrl="https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode"
            categories={categories}
            onCategorySelect={handleCategorySelect}
          />
          <div className="mt-4 p-4 bg-muted rounded-lg">
            <h3 className="font-medium">Selected Category: {selectedCategory.name}</h3>
            <p className="text-sm text-muted-foreground mt-1">{selectedCategory.description}</p>
          </div>
        </TabsContent>

        <TabsContent value="print-simulation" className="space-y-4">
          <h2 className="text-2xl font-semibold">3D Print Simulation</h2>
          <p className="text-muted-foreground mb-4">
            Visualize how your model will print layer by layer before sending it to your printer.
          </p>
          <PrintSimulation
            modelName={sampleModel.name}
            splineSceneUrl="https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode"
            estimatedPrintTime="8h 45m"
          />
        </TabsContent>

        <TabsContent value="model-comparison" className="space-y-4">
          <h2 className="text-2xl font-semibold">Model Comparison Tool</h2>
          <p className="text-muted-foreground mb-4">
            Compare different models side by side to find the one that best fits your needs.
          </p>
          <ModelComparison
            modelA={comparisonModelA}
            modelB={comparisonModelB}
          />
        </TabsContent>
      </Tabs>

      <div className="mt-12 text-center">
        <p className="text-muted-foreground mb-4">
          These components demonstrate how Spline can be integrated into your 3D marketplace
          to create interactive and engaging experiences.
        </p>
        <Link href="/">
          <Button variant="outline">Back to Home</Button>
        </Link>
      </div>
    </div>
  );
}
