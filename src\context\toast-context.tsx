'use client';

import { useToast } from '@/hooks/use-toast';
import { createContext, useContext, ReactNode } from 'react';

interface ToastContextType {
  toast: (options: {
    title: string;
    description?: string;
    duration?: number;
    type?: 'default' | 'success' | 'error' | 'warning' | 'info';
  }) => string;
  dismissToast: (id: string) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export function ToastProvider({ children }: { children: ReactNode }) {
  const { toast, dismissToast } = useToast();

  return (
    <ToastContext.Provider value={{ toast, dismissToast }}>
      {children}
    </ToastContext.Provider>
  );
}

export function useToastContext() {
  const context = useContext(ToastContext);
  if (context === undefined) {
    throw new Error('useToastContext must be used within a ToastProvider');
  }
  return context;
}
