'use client';

import React from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';

interface AnimatedLogoProps {
  className?: string;
  showText?: boolean;
}

const AnimatedLogo: React.FC<AnimatedLogoProps> = ({
  className = "h-8 w-8",
  showText = true
}) => {
  return (
    <Link href="/" className="flex items-center space-x-3 group">
      <motion.div
        className="relative"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
        transition={{ type: "spring", stiffness: 400, damping: 17 }}
      >
        {/* Outer glow effect */}
        <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-blue-500/20 to-purple-500/20 blur-md opacity-60" />

        {/* Main logo container */}
        <motion.div
          className={`relative ${className} flex items-center justify-center`}
          animate={{
            rotate: [0, 360],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "linear",
          }}
        >
          {/* 3D Cube SVG */}
          <svg
            viewBox="0 0 100 100"
            className="w-full h-full"
            style={{ filter: 'drop-shadow(0 4px 8px rgba(0,0,0,0.2))' }}
          >
            <defs>
              <linearGradient id="logoGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#3b82f6" />
                <stop offset="100%" stopColor="#1d4ed8" />
              </linearGradient>
              <linearGradient id="logoGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#8b5cf6" />
                <stop offset="100%" stopColor="#7c3aed" />
              </linearGradient>
              <linearGradient id="logoGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#06b6d4" />
                <stop offset="100%" stopColor="#0891b2" />
              </linearGradient>
            </defs>

            {/* Cube faces */}
            <path
              d="M20 30 L50 15 L80 30 L50 45 Z"
              fill="url(#logoGradient1)"
            />
            <path
              d="M20 30 L20 60 L50 75 L50 45 Z"
              fill="url(#logoGradient2)"
            />
            <path
              d="M50 45 L50 75 L80 60 L80 30 Z"
              fill="url(#logoGradient3)"
            />

            {/* Inner lines for 3D effect */}
            <line
              x1="35" y1="22.5" x2="35" y2="52.5"
              stroke="rgba(255,255,255,0.3)"
              strokeWidth="1"
            />
            <line
              x1="65" y1="22.5" x2="65" y2="52.5"
              stroke="rgba(255,255,255,0.3)"
              strokeWidth="1"
            />
            <line
              x1="35" y1="37.5" x2="65" y2="37.5"
              stroke="rgba(255,255,255,0.3)"
              strokeWidth="1"
            />
          </svg>
        </motion.div>
      </motion.div>

      {showText && (
        <motion.div
          className="flex flex-col"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
        >
          <motion.span
            className="font-bold text-lg bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent group-hover:from-blue-500 group-hover:to-purple-500 transition-all duration-300"
            whileHover={{ scale: 1.05 }}
          >
            CUBE 3D
          </motion.span>
          <motion.span
            className="text-xs text-muted-foreground font-medium tracking-wide"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8, duration: 0.4 }}
          >
            MARKETPLACE
          </motion.span>
        </motion.div>
      )}
    </Link>
  );
};

export default AnimatedLogo;
