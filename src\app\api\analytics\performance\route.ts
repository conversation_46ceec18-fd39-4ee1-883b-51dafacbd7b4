/**
 * API endpoint для трекінгу метрик продуктивності в Cloudflare Analytics
 * Інтеграція з cloudflare-observability MCP
 */

import { NextRequest, NextResponse } from 'next/server';
import { withFullApiMiddleware, CloudflareObservabilityContext } from '@/lib/cloudflare/middleware';

interface PerformanceMetricRequest {
  metric: string;
  value: number;
  unit?: string;
  timestamp?: string;
  url?: string;
}

async function handlePerformanceMetric(request: NextRequest, context: CloudflareObservabilityContext) {
  try {
    const body: PerformanceMetricRequest = await request.json();
    const { metric, value, unit = 'ms', timestamp, url } = body;

    if (!metric || typeof value !== 'number') {
      return NextResponse.json(
        { success: false, error: 'Metric name and numeric value are required' },
        { status: 400 }
      );
    }

    // Трекінг метрики продуктивності
    await context.trackPerformance(metric, value, unit);

    // Додатковий трекінг як події для детального аналізу
    await context.trackEvent('performance_metric', {
      metric,
      value,
      unit,
      url: url || request.headers.get('referer'),
      timestamp: timestamp || new Date().toISOString(),
      userAgent: request.headers.get('user-agent'),
      country: request.headers.get('cf-ipcountry')
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to track performance metric:', error);
    await context.trackError(error as Error, 'analytics_performance_api');
    
    return NextResponse.json(
      { success: false, error: 'Failed to track performance metric' },
      { status: 500 }
    );
  }
}

export const POST = withFullApiMiddleware(handlePerformanceMetric);
