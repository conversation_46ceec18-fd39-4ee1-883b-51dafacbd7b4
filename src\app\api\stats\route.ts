import { NextRequest, NextResponse } from 'next/server';
import { withApiMiddleware } from '@/lib/db/middleware';

/**
 * GET: Отримання статистики маркетплейсу
 */
export const GET = withApiMiddleware(async (
  _request: NextRequest,
  { db }
) => {
  try {
    // Загальна кількість моделей
    const totalModelsResult = await db.prepare(`
      SELECT COUNT(*) as count FROM models
    `).first<{ count: number }>();
    
    // Загальна кількість користувачів
    const totalUsersResult = await db.prepare(`
      SELECT COUNT(*) as count FROM users
    `).first<{ count: number }>();
    
    // Загальна кількість завантажень
    const totalDownloadsResult = await db.prepare(`
      SELECT SUM(download_count) as count FROM models
    `).first<{ count: number }>();
    
    // Загальна кількість переглядів
    const totalViewsResult = await db.prepare(`
      SELECT SUM(view_count) as count FROM models
    `).first<{ count: number }>();
    
    // Кількість безкоштовних моделей
    const freeModelsResult = await db.prepare(`
      SELECT COUNT(*) as count FROM models WHERE is_free = 1
    `).first<{ count: number }>();
    
    // Кількість платних моделей
    const paidModelsResult = await db.prepare(`
      SELECT COUNT(*) as count FROM models WHERE is_free = 0
    `).first<{ count: number }>();
    
    // Популярні категорії
    const popularCategoriesResult = await db.prepare(`
      SELECT category, COUNT(*) as count
      FROM models
      GROUP BY category
      ORDER BY count DESC
      LIMIT 5
    `).all<{ category: string; count: number }>();
    
    // Найпопулярніші моделі
    const popularModelsResult = await db.prepare(`
      SELECT m.id, m.name, m.thumbnail_url, m.download_count, m.view_count, u.name as author_name
      FROM models m
      JOIN users u ON m.user_id = u.id
      ORDER BY m.download_count DESC
      LIMIT 5
    `).all();
    
    // Останні додані моделі
    const latestModelsResult = await db.prepare(`
      SELECT m.id, m.name, m.thumbnail_url, m.created_at, u.name as author_name
      FROM models m
      JOIN users u ON m.user_id = u.id
      ORDER BY m.created_at DESC
      LIMIT 5
    `).all();
    
    // Формування відповіді
    const stats = {
      totalModels: totalModelsResult?.count || 0,
      totalUsers: totalUsersResult?.count || 0,
      totalDownloads: totalDownloadsResult?.count || 0,
      totalViews: totalViewsResult?.count || 0,
      freeModels: freeModelsResult?.count || 0,
      paidModels: paidModelsResult?.count || 0,
      popularCategories: popularCategoriesResult.results,
      popularModels: popularModelsResult.results,
      latestModels: latestModelsResult.results,
    };
    
    return NextResponse.json({ success: true, data: stats });
  } catch (error) {
    console.error('Error fetching stats:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch stats' },
      { status: 500 }
    );
  }
});
