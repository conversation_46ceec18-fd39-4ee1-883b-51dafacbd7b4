'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { MapPin, Calendar, Globe, Mail, Star, Download, Heart } from 'lucide-react';

export default function UserProfilePage() {
  const params = useParams();
  const username = params.username as string;

  // Mock user data - в реальному проекті це буде завантажуватися з API
  const user = {
    username: username,
    name: username.charAt(0).toUpperCase() + username.slice(1),
    avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${username}`,
    bio: `Талановитий 3D дизайнер та ентузіаст 3D друку. Спеціалізуюся на створенні функціональних та декоративних моделей.`,
    location: 'Київ, Україна',
    website: `https://${username}.design`,
    email: `${username}@example.com`,
    joinDate: '2023-01-15',
    stats: {
      models: 42,
      downloads: 15420,
      likes: 3240,
      followers: 890,
      following: 156
    },
    badges: ['Verified Designer', 'Top Contributor', 'Community Favorite']
  };

  const recentModels = [
    {
      id: 1,
      title: 'Mechanical Keyboard Keycap',
      thumbnail: 'https://images.unsplash.com/photo-1541140532154-b024d705b90a?q=80&w=400',
      downloads: 1240,
      likes: 89
    },
    {
      id: 2,
      title: 'Phone Stand Organizer',
      thumbnail: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?q=80&w=400',
      downloads: 856,
      likes: 67
    },
    {
      id: 3,
      title: 'Miniature Dragon',
      thumbnail: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?q=80&w=400',
      downloads: 2340,
      likes: 156
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Profile Header */}
        <Card className="p-8 mb-8">
          <div className="flex flex-col md:flex-row items-start gap-6">
            <Avatar className="w-24 h-24">
              <AvatarImage src={user.avatar} alt={user.name} />
              <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
            </Avatar>
            
            <div className="flex-1">
              <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
                <div>
                  <h1 className="text-3xl font-bold mb-2">{user.name}</h1>
                  <p className="text-muted-foreground mb-4">{user.bio}</p>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline">Підписатися</Button>
                  <Button>Написати</Button>
                </div>
              </div>

              <div className="flex flex-wrap gap-4 text-sm text-muted-foreground mb-4">
                <div className="flex items-center gap-1">
                  <MapPin className="h-4 w-4" />
                  {user.location}
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  Приєднався {new Date(user.joinDate).toLocaleDateString('uk-UA')}
                </div>
                <div className="flex items-center gap-1">
                  <Globe className="h-4 w-4" />
                  <a href={user.website} className="hover:text-primary" target="_blank" rel="noopener noreferrer">
                    {user.website}
                  </a>
                </div>
              </div>

              <div className="flex flex-wrap gap-2 mb-4">
                {user.badges.map((badge, index) => (
                  <Badge key={index} variant="secondary">{badge}</Badge>
                ))}
              </div>

              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{user.stats.models}</div>
                  <div className="text-sm text-muted-foreground">Моделей</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{user.stats.downloads.toLocaleString()}</div>
                  <div className="text-sm text-muted-foreground">Завантажень</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{user.stats.likes.toLocaleString()}</div>
                  <div className="text-sm text-muted-foreground">Лайків</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{user.stats.followers}</div>
                  <div className="text-sm text-muted-foreground">Підписників</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{user.stats.following}</div>
                  <div className="text-sm text-muted-foreground">Підписок</div>
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* Recent Models */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold">Останні моделі</h2>
            <Button variant="outline" asChild>
              <Link href={`/marketplace?creator=${username}`}>Переглянути всі</Link>
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {recentModels.map((model) => (
              <Card key={model.id} className="overflow-hidden group cursor-pointer">
                <div className="aspect-square relative">
                  <img
                    src={model.thumbnail}
                    alt={model.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="p-4">
                  <h3 className="font-semibold mb-2 group-hover:text-primary transition-colors">
                    {model.title}
                  </h3>
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Download className="h-4 w-4" />
                      {model.downloads}
                    </div>
                    <div className="flex items-center gap-1">
                      <Heart className="h-4 w-4" />
                      {model.likes}
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* Back to marketplace */}
        <div className="text-center">
          <Button variant="outline" asChild>
            <Link href="/marketplace">← Повернутися до маркетплейсу</Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
