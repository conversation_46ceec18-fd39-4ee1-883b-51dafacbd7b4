import { authOptions } from '@/lib/auth';
import { execute, generateId, query, queryOne } from '@/lib/db';
import { ModelsQueryParams } from '@/types/models';
import { getServerSession } from 'next-auth/next';
import { NextRequest, NextResponse } from 'next/server';

// GET handler for models list with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const params: ModelsQueryParams = {
      page: Number(searchParams.get('page')) || 1,
      limit: Number(searchParams.get('limit')) || 20,
      category: searchParams.get('category') || undefined,
      search: searchParams.get('search') || undefined,
      tags: searchParams.get('tags')?.split(',') || undefined,
      minPrice: searchParams.get('minPrice') ? Number(searchParams.get('minPrice')) : undefined,
      maxPrice: searchParams.get('maxPrice') ? Number(searchParams.get('maxPrice')) : undefined,
      sortBy: (searchParams.get('sortBy') as ModelsQueryParams['sortBy']) || 'newest',
    };

    // Ensure page and limit are valid numbers
    const page = Math.max(1, params.page || 1); // Minimum value 1
    const limit = Math.max(1, Math.min(100, params.limit || 20)); // Between 1 and 100, default 20
    const offset = (page - 1) * limit;

    // Build SQL query
    let sql = `
      SELECT m.*, u.name as author_name, u.avatar_url as author_avatar
      FROM models m
      JOIN users u ON m.user_id = u.id
      WHERE 1=1
    `;

    const queryParams: any[] = [];

    // Filter by category
    if (params.category) {
      sql += ` AND m.category = ?`;
      queryParams.push(params.category);
    }

    // Filter by search query
    if (params.search) {
      sql += ` AND (
        m.name LIKE ? OR
        m.description LIKE ? OR
        m.tags LIKE ?
      )`;
      const searchTerm = `%${params.search}%`;
      queryParams.push(searchTerm, searchTerm, searchTerm);
    }

    // Filter by tags
    if (params.tags && params.tags.length > 0) {
      const tagConditions = params.tags.map(() => `m.tags LIKE ?`).join(' OR ');
      sql += ` AND (${tagConditions})`;
      params.tags.forEach(tag => queryParams.push(`%${tag}%`));
    }

    // Filter by price
    if (params.minPrice !== undefined) {
      sql += ` AND m.price >= ?`;
      queryParams.push(params.minPrice);
    }
    if (params.maxPrice !== undefined) {
      sql += ` AND m.price <= ?`;
      queryParams.push(params.maxPrice);
    }

    // Filter by free/paid
    if (params.isFree !== undefined) {
      sql += ` AND m.is_free = ?`;
      queryParams.push(params.isFree ? 1 : 0);
    }

    // Filter by date range
    if (params.dateRange) {
      const now = new Date();
      let dateFilter = '';

      switch (params.dateRange) {
        case 'today':
          dateFilter = `DATE(m.created_at) = DATE('now')`;
          break;
        case 'week':
          dateFilter = `m.created_at >= datetime('now', '-7 days')`;
          break;
        case 'month':
          dateFilter = `m.created_at >= datetime('now', '-1 month')`;
          break;
        case 'year':
          dateFilter = `m.created_at >= datetime('now', '-1 year')`;
          break;
      }

      if (dateFilter) {
        sql += ` AND ${dateFilter}`;
      }
    }

    // Filter by minimum downloads
    if (params.minDownloads !== undefined && params.minDownloads > 0) {
      sql += ` AND m.download_count >= ?`;
      queryParams.push(params.minDownloads);
    }

    // Filter by file formats
    if (params.fileFormats && params.fileFormats.length > 0) {
      const formatConditions = params.fileFormats.map(() => `m.file_formats LIKE ?`).join(' OR ');
      sql += ` AND (${formatConditions})`;
      params.fileFormats.forEach(format => queryParams.push(`%${format}%`));
    }

    // Sorting
    switch (params.sortBy) {
      case 'newest':
        sql += ` ORDER BY m.created_at DESC`;
        break;
      case 'oldest':
        sql += ` ORDER BY m.created_at ASC`;
        break;
      case 'popular':
        sql += ` ORDER BY m.download_count DESC`;
        break;
      case 'downloads':
        sql += ` ORDER BY m.download_count DESC`;
        break;
      case 'price_asc':
        sql += ` ORDER BY m.price ASC`;
        break;
      case 'price_desc':
        sql += ` ORDER BY m.price DESC`;
        break;
      case 'name':
        sql += ` ORDER BY m.name ASC`;
        break;
      default:
        sql += ` ORDER BY m.created_at DESC`;
        break;
    }

    // Pagination
    sql += ` LIMIT ? OFFSET ?`;
    queryParams.push(limit, offset);

    // Execute query
    const models = await query(sql, queryParams);

    // Get total count for pagination
    let countSql = `
      SELECT COUNT(*) as total
      FROM models m
      WHERE 1=1
    `;

    const countParams: any[] = [];

    // Apply the same filters to count query
    if (params.category) {
      countSql += ` AND m.category = ?`;
      countParams.push(params.category);
    }

    if (params.search) {
      countSql += ` AND (
        m.name LIKE ? OR
        m.description LIKE ? OR
        m.tags LIKE ?
      )`;
      const searchTerm = `%${params.search}%`;
      countParams.push(searchTerm, searchTerm, searchTerm);
    }

    if (params.tags && params.tags.length > 0) {
      const tagConditions = params.tags.map(() => `m.tags LIKE ?`).join(' OR ');
      countSql += ` AND (${tagConditions})`;
      params.tags.forEach(tag => countParams.push(`%${tag}%`));
    }

    if (params.minPrice !== undefined) {
      countSql += ` AND m.price >= ?`;
      countParams.push(params.minPrice);
    }
    if (params.maxPrice !== undefined) {
      countSql += ` AND m.price <= ?`;
      countParams.push(params.maxPrice);
    }

    // Apply same additional filters to count query
    if (params.isFree !== undefined) {
      countSql += ` AND m.is_free = ?`;
      countParams.push(params.isFree ? 1 : 0);
    }

    if (params.dateRange) {
      let dateFilter = '';

      switch (params.dateRange) {
        case 'today':
          dateFilter = `DATE(m.created_at) = DATE('now')`;
          break;
        case 'week':
          dateFilter = `m.created_at >= datetime('now', '-7 days')`;
          break;
        case 'month':
          dateFilter = `m.created_at >= datetime('now', '-1 month')`;
          break;
        case 'year':
          dateFilter = `m.created_at >= datetime('now', '-1 year')`;
          break;
      }

      if (dateFilter) {
        countSql += ` AND ${dateFilter}`;
      }
    }

    if (params.minDownloads !== undefined && params.minDownloads > 0) {
      countSql += ` AND m.download_count >= ?`;
      countParams.push(params.minDownloads);
    }

    if (params.fileFormats && params.fileFormats.length > 0) {
      const formatConditions = params.fileFormats.map(() => `m.file_formats LIKE ?`).join(' OR ');
      countSql += ` AND (${formatConditions})`;
      params.fileFormats.forEach((format: string) => countParams.push(`%${format}%`));
    }

    const totalResult = await queryOne<{ total: number }>(countSql, countParams);
    const total = totalResult?.total || 0;

    // Format response
    return NextResponse.json({
      success: true,
      data: {
        models,
        pagination: {
          total,
          page,
          limit,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching models:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch models' },
      { status: 500 }
    );
  }
}

// POST handler for creating a new model
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json() as any;

    // Validate required fields
    if (!body || !body.name || !body.model_url) {
      return NextResponse.json(
        { success: false, error: 'Name and model URL are required' },
        { status: 400 }
      );
    }

    const modelId = generateId();

    // Create new model
    await execute(
      `INSERT INTO models (
        id, user_id, name, description, price, category, tags,
        thumbnail_url, model_url, is_free, license, additional_files,
        additional_images, print_settings, file_formats, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
      [
        modelId,
        session.user.id,
        body.name,
        body.description || '',
        body.price || 0,
        body.category || 'Other',
        body.tags ? (Array.isArray(body.tags) ? body.tags.join(',') : body.tags) : '',
        body.thumbnail_url || '',
        body.model_url,
        body.price === 0 || body.is_free ? 1 : 0,
        body.license || 'standard',
        body.additional_files ? JSON.stringify(body.additional_files) : null,
        body.additional_images ? JSON.stringify(body.additional_images) : null,
        body.print_settings ? JSON.stringify(body.print_settings) : null,
        body.file_formats || '',
      ]
    );

    // Get created model
    const model = await queryOne(
      `SELECT m.*, u.name as author_name, u.avatar_url as author_avatar
       FROM models m
       JOIN users u ON m.user_id = u.id
       WHERE m.id = ?`,
      [modelId]
    );

    return NextResponse.json({
      success: true,
      data: model
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating model:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create model' },
      { status: 500 }
    );
  }
}
