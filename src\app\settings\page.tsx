'use client';

import { useSession } from 'next-auth/react';
import { useState, Suspense } from 'react';
import { User, Mail, Lock, Bell, Shield, CreditCard } from 'lucide-react';
import ProtectedRoute from '@/components/auth/protected-route';

function SettingsPageContent() {
  const { data: session } = useSession();
  const [activeTab, setActiveTab] = useState('profile');

  const tabs = [
    { id: 'profile', label: 'Профіль', icon: <User className="h-5 w-5" /> },
    { id: 'account', label: 'Обліковий запис', icon: <Mail className="h-5 w-5" /> },
    { id: 'password', label: 'Пароль', icon: <Lock className="h-5 w-5" /> },
    { id: 'notifications', label: 'Сповіщення', icon: <Bell className="h-5 w-5" /> },
    { id: 'privacy', label: 'Конфіденційність', icon: <Shield className="h-5 w-5" /> },
    { id: 'billing', label: 'Оплата', icon: <CreditCard className="h-5 w-5" /> },
  ];

  return (
    <ProtectedRoute>
      <main className="container py-10">
        <h1 className="text-3xl font-bold mb-8">Налаштування</h1>

        <div className="flex flex-col md:flex-row gap-8">
          {/* Sidebar */}
          <aside className="w-full md:w-1/4">
            <div className="bg-card rounded-lg shadow-sm p-4 sticky top-20">
              <nav className="space-y-1">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    className={`flex items-center gap-2 w-full px-3 py-2 text-sm rounded-md transition-colors ${
                      activeTab === tab.id
                        ? 'bg-primary text-primary-foreground'
                        : 'text-muted-foreground hover:bg-muted hover:text-foreground'
                    }`}
                    onClick={() => setActiveTab(tab.id)}
                    aria-label={tab.label}
                    tabIndex={0}
                  >
                    {tab.icon}
                    {tab.label}
                  </button>
                ))}
              </nav>
            </div>
          </aside>

          {/* Main content */}
          <div className="flex-1">
            <div className="bg-card rounded-lg shadow-sm p-6">
              {activeTab === 'profile' && (
                <div>
                  <h2 className="text-xl font-semibold mb-6">Налаштування профілю</h2>
                  <div className="space-y-6">
                    <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
                      <div className="h-20 w-20 rounded-full bg-blue-600 flex items-center justify-center text-white">
                        {session?.user?.image ? (
                          <img
                            src={session.user.image}
                            alt={session.user.name || 'Аватар користувача'}
                            className="h-20 w-20 rounded-full"
                          />
                        ) : (
                          <User className="h-10 w-10" />
                        )}
                      </div>
                      <div>
                        <h3 className="text-lg font-medium">{session?.user?.name || 'Користувач'}</h3>
                        <p className="text-sm text-muted-foreground">{session?.user?.email}</p>
                        <button
                          className="mt-2 text-sm text-primary hover:text-primary/80 transition-colors"
                          aria-label="Змінити фото профілю"
                          tabIndex={0}
                        >
                          Змінити фото профілю
                        </button>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 gap-6">
                      <div>
                        <label htmlFor="name" className="block text-sm font-medium text-foreground mb-1">
                          Ім'я
                        </label>
                        <input
                          type="text"
                          id="name"
                          name="name"
                          className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50"
                          defaultValue={session?.user?.name || ''}
                          aria-label="Ім'я"
                        />
                      </div>

                      <div>
                        <label htmlFor="bio" className="block text-sm font-medium text-foreground mb-1">
                          Про себе
                        </label>
                        <textarea
                          id="bio"
                          name="bio"
                          rows={4}
                          className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50"
                          placeholder="Розкажіть про себе..."
                          aria-label="Про себе"
                        />
                      </div>

                      <div>
                        <label htmlFor="location" className="block text-sm font-medium text-foreground mb-1">
                          Місцезнаходження
                        </label>
                        <input
                          type="text"
                          id="location"
                          name="location"
                          className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50"
                          placeholder="Місто, Країна"
                          aria-label="Місцезнаходження"
                        />
                      </div>

                      <div>
                        <label htmlFor="website" className="block text-sm font-medium text-foreground mb-1">
                          Веб-сайт
                        </label>
                        <input
                          type="url"
                          id="website"
                          name="website"
                          className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50"
                          placeholder="https://example.com"
                          aria-label="Веб-сайт"
                        />
                      </div>
                    </div>

                    <div className="flex justify-end">
                      <button
                        type="button"
                        className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
                        aria-label="Зберегти зміни"
                        tabIndex={0}
                      >
                        Зберегти зміни
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {activeTab !== 'profile' && (
                <div className="text-center py-10">
                  <div className="h-16 w-16 mx-auto mb-4 text-muted-foreground opacity-30">
                    {tabs.find(tab => tab.id === activeTab)?.icon}
                  </div>
                  <h2 className="text-xl font-semibold mb-2">
                    {tabs.find(tab => tab.id === activeTab)?.label}
                  </h2>
                  <p className="text-muted-foreground">
                    Ці налаштування будуть доступні найближчим часом
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </ProtectedRoute>
  );
}

export default function SettingsPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    }>
      <SettingsPageContent />
    </Suspense>
  );
}
