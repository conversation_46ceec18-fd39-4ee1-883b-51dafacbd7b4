'use client'

import { useState, useRef, useEffect, useCallback, Suspense, lazy } from 'react'
import { motion, useAnimation, useMotionValue, useTransform, AnimatePresence } from 'framer-motion'
import { ChevronLeft, ChevronRight, Info, X, Maximize2 } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

// Lazy load Spline to improve initial load performance
const Spline = lazy(() => import('@splinetool/react-spline'))

// Spotlight component for visual effects
type SpotlightProps = {
  className?: string
  size?: number
  fill?: string
}

function Spotlight({ className, size = 200, fill = 'white' }: SpotlightProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isHovered, setIsHovered] = useState(false)
  const [parentElement, setParentElement] = useState<HTMLElement | null>(null)

  const mouseX = useMotionValue(0)
  const mouseY = useMotionValue(0)

  const spotlightLeft = useTransform(mouseX, (x) => `${x - size / 2}px`)
  const spotlightTop = useTransform(mouseY, (y) => `${y - size / 2}px`)

  useEffect(() => {
    if (containerRef.current) {
      const parent = containerRef.current.parentElement
      if (parent) {
        parent.style.position = 'relative'
        parent.style.overflow = 'hidden'
        setParentElement(parent)
      }
    }
  }, [])

  const handleMouseMove = useCallback(
    (event: MouseEvent) => {
      if (!parentElement) return
      const { left, top } = parentElement.getBoundingClientRect()
      mouseX.set(event.clientX - left)
      mouseY.set(event.clientY - top)
    },
    [mouseX, mouseY, parentElement]
  )

  useEffect(() => {
    if (!parentElement) return

    parentElement.addEventListener('mousemove', handleMouseMove)
    parentElement.addEventListener('mouseenter', () => setIsHovered(true))
    parentElement.addEventListener('mouseleave', () => setIsHovered(false))

    return () => {
      parentElement.removeEventListener('mousemove', handleMouseMove)
      parentElement.removeEventListener('mouseenter', () => setIsHovered(true))
      parentElement.removeEventListener('mouseleave', () => setIsHovered(false))
    }
  }, [parentElement, handleMouseMove])

  return (
    <motion.div
      ref={containerRef}
      className={cn(
        'pointer-events-none absolute rounded-full blur-xl transition-opacity duration-200',
        'bg-[radial-gradient(circle_at_center,var(--tw-gradient-stops),transparent_80%)]',
        fill === 'white' ? 'from-zinc-50 via-zinc-100 to-zinc-200' : 'from-zinc-800 via-zinc-700 to-zinc-900',
        isHovered ? 'opacity-100' : 'opacity-0',
        className
      )}
      style={{
        width: size,
        height: size,
        left: spotlightLeft,
        top: spotlightTop,
      }}
    />
  )
}

// SplineScene component for rendering 3D models
interface SplineSceneProps {
  scene: string
  className?: string
}

function SplineScene({ scene, className }: SplineSceneProps) {
  return (
    <Suspense 
      fallback={
        <div className="w-full h-full flex items-center justify-center">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
        </div>
      }
    >
      <Spline
        scene={scene}
        className={className}
      />
    </Suspense>
  )
}

// Model data interface
interface Model3D {
  id: string
  name: string
  description: string
  scene: string
  thumbnail?: string
}

// Main component
interface ModelShowcaseProps {
  models: Model3D[]
  className?: string
}

function ModelShowcase({ models, className }: ModelShowcaseProps) {
  const [activeIndex, setActiveIndex] = useState(0)
  const [isExpanded, setIsExpanded] = useState(false)
  const [showInfo, setShowInfo] = useState(false)
  const controls = useAnimation()
  const containerRef = useRef<HTMLDivElement>(null)

  const activeModel = models[activeIndex]

  const nextModel = () => {
    setActiveIndex((prev) => (prev + 1) % models.length)
  }

  const prevModel = () => {
    setActiveIndex((prev) => (prev - 1 + models.length) % models.length)
  }

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded)
  }

  const toggleInfo = () => {
    setShowInfo(!showInfo)
  }

  return (
    <div className={cn("w-full", className)} ref={containerRef}>
      <Card className={cn(
        "relative overflow-hidden transition-all duration-500 bg-background/95 border-border",
        isExpanded ? "fixed inset-4 z-50 m-auto" : "w-full h-[600px]"
      )}>
        <Spotlight
          className="-top-40 left-0 md:left-60 md:-top-20"
          fill="white"
          size={300}
        />
        
        {/* Close button when expanded */}
        {isExpanded && (
          <Button 
            variant="ghost" 
            size="icon" 
            className="absolute top-4 right-4 z-20 bg-background/80 hover:bg-background"
            onClick={toggleExpanded}
          >
            <X className="h-5 w-5" />
          </Button>
        )}
        
        <div className="flex flex-col h-full">
          {/* Header with model name */}
          <div className="p-4 border-b border-border flex justify-between items-center">
            <h2 className="text-xl font-semibold text-foreground">{activeModel.name}</h2>
            <div className="flex gap-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" onClick={toggleInfo}>
                      <Info className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>View model information</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" onClick={toggleExpanded}>
                      <Maximize2 className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Toggle fullscreen</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
          
          {/* Main content area */}
          <div className="flex-1 flex flex-col md:flex-row relative">
            {/* 3D model display */}
            <div className="flex-1 relative">
              <SplineScene 
                scene={activeModel.scene}
                className="w-full h-full"
              />
            </div>
            
            {/* Info panel */}
            <AnimatePresence>
              {showInfo && (
                <motion.div 
                  initial={{ opacity: 0, x: 100 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 100 }}
                  transition={{ duration: 0.3 }}
                  className="absolute md:relative top-0 right-0 w-full md:w-80 h-full bg-background/95 border-l border-border p-6 overflow-y-auto z-10"
                >
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium">Model Details</h3>
                    <Button variant="ghost" size="icon" onClick={toggleInfo}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-muted-foreground">{activeModel.description}</p>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
          
          {/* Gallery navigation */}
          <div className="p-4 border-t border-border">
            <div className="flex justify-between items-center">
              <Button variant="outline" size="icon" onClick={prevModel}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              
              <div className="flex-1 overflow-hidden mx-4">
                <div className="flex gap-2 justify-center">
                  {models.map((model, idx) => (
                    <button
                      key={model.id}
                      onClick={() => setActiveIndex(idx)}
                      className={cn(
                        "w-2 h-2 rounded-full transition-all",
                        idx === activeIndex 
                          ? "bg-primary w-6" 
                          : "bg-muted hover:bg-primary/50"
                      )}
                      aria-label={`View ${model.name}`}
                    />
                  ))}
                </div>
              </div>
              
              <Button variant="outline" size="icon" onClick={nextModel}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </Card>
    </div>
  )
}

// Example usage
export default function ModelShowcaseExample() {
  // Example model data
  const exampleModels: Model3D[] = [
    {
      id: "model1",
      name: "Interactive Cube",
      description: "A simple interactive cube that responds to user interaction. You can rotate, zoom, and click on different parts of the model to see it react.",
      scene: "https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode"
    },
    {
      id: "model2",
      name: "Floating Sphere",
      description: "An animated floating sphere with dynamic lighting effects. This model demonstrates physics-based animations and material properties.",
      scene: "https://prod.spline.design/rrBJALXxKfGiGHFr/scene.splinecode"
    },
    {
      id: "model3",
      name: "Abstract Composition",
      description: "A complex abstract composition featuring multiple interactive elements. Explore the model to discover hidden animations and effects.",
      scene: "https://prod.spline.design/6Gjr0XkIyLG2fmXW/scene.splinecode"
    }
  ]

  return (
    <div className="container py-10">
      <h1 className="text-3xl font-bold mb-6 text-center">3D Model Showcase</h1>
      <p className="text-muted-foreground text-center mb-8">
        Explore our interactive 3D models with Spline integration
      </p>
      <ModelShowcase models={exampleModels} />
    </div>
  )
}
