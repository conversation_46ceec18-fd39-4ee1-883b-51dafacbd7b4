const fs = require('fs');
const path = require('path');

// Створюємо папку для статичних файлів
const distDir = path.join(__dirname, '..', 'dist');
const nextDir = path.join(__dirname, '..', '.next');

// Очищуємо папку dist
if (fs.existsSync(distDir)) {
  fs.rmSync(distDir, { recursive: true });
}
fs.mkdirSync(distDir, { recursive: true });

// Копіюємо статичні файли
const staticDir = path.join(nextDir, 'static');
if (fs.existsSync(staticDir)) {
  fs.cpSync(staticDir, path.join(distDir, '_next', 'static'), { recursive: true });
}

// Копіюємо HTML файли
const serverAppDir = path.join(nextDir, 'server', 'app');
if (fs.existsSync(serverAppDir)) {
  // Копіюємо index.html як головну сторінку
  const indexHtml = path.join(serverAppDir, 'index.html');
  if (fs.existsSync(indexHtml)) {
    fs.copyFileSync(indexHtml, path.join(distDir, 'index.html'));
  }
  
  // Копіюємо інші HTML файли
  const htmlFiles = [
    'cart.html',
    'community.html', 
    'marketplace.html',
    'models.html',
    'print-services.html',
    'profile.html',
    'settings.html',
    'shadcn-demo.html',
    'spline-demo.html',
    'spline-integration.html',
    'spline-showcase.html'
  ];
  
  htmlFiles.forEach(file => {
    const srcFile = path.join(serverAppDir, file);
    if (fs.existsSync(srcFile)) {
      const destFile = path.join(distDir, file);
      fs.copyFileSync(srcFile, destFile);
    }
  });
  
  // Копіюємо auth папку
  const authDir = path.join(serverAppDir, 'auth');
  if (fs.existsSync(authDir)) {
    const authDistDir = path.join(distDir, 'auth');
    fs.mkdirSync(authDistDir, { recursive: true });
    
    const authFiles = fs.readdirSync(authDir);
    authFiles.forEach(file => {
      if (file.endsWith('.html')) {
        fs.copyFileSync(
          path.join(authDir, file),
          path.join(authDistDir, file)
        );
      }
    });
  }
}

console.log('✅ Статичні файли підготовлені для Cloudflare Pages');
