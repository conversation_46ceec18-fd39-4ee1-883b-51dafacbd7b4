'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/components/ui/alert';
import { 
  Download, 
  Play, 
  RefreshCw, 
  CheckCircle, 
  AlertTriangle,
  Clock,
  Database,
  TrendingUp
} from 'lucide-react';

interface ImportProgress {
  platform: string;
  total: number;
  completed: number;
  successful: number;
  failed: number;
  jobId: string;
}

interface ImportSummary {
  totalPlatforms: number;
  totalModels: number;
  completedModels: number;
  successfulModels: number;
  failedModels: number;
}

export default function PopularModelsImportPanel() {
  const [adminKey, setAdminKey] = useState('');
  const [importType, setImportType] = useState('sample');
  const [selectedPlatform, setSelectedPlatform] = useState('');
  const [modelsPerPlatform, setModelsPerPlatform] = useState(3);
  const [isImporting, setIsImporting] = useState(false);
  const [progress, setProgress] = useState<ImportProgress[]>([]);
  const [summary, setSummary] = useState<ImportSummary | null>(null);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  const platforms = [
    { value: 'thingiverse', label: 'Thingiverse' },
    { value: 'myminifactory', label: 'MyMiniFactory' },
    { value: 'printables', label: 'Printables' },
    { value: 'thangs', label: 'Thangs' },
    { value: 'makerworld', label: 'MakerWorld' },
  ];

  const importTypes = [
    { value: 'sample', label: 'Зразки (3-5 моделей з кожної платформи)' },
    { value: 'all', label: 'Всі популярні моделі (~50 моделей)' },
    { value: 'platform', label: 'Конкретна платформа' },
  ];

  // Автоматичне оновлення прогресу
  useEffect(() => {
    if (isImporting) {
      const interval = setInterval(fetchProgress, 3000);
      return () => clearInterval(interval);
    }
  }, [isImporting, adminKey]);

  const fetchProgress = async () => {
    if (!adminKey) return;

    try {
      const response = await fetch(`/api/admin/import-popular/progress?adminKey=${encodeURIComponent(adminKey)}`);
      const result = await response.json();

      if (result.success) {
        setProgress(result.data.progress);
        setSummary(result.data.summary);
        setIsImporting(result.data.isActive);
      }
    } catch (error) {
      console.error('Failed to fetch progress:', error);
    }
  };

  const startImport = async () => {
    if (!adminKey) {
      setError('Введіть адмін ключ');
      return;
    }

    setError('');
    setMessage('');
    setIsImporting(true);

    try {
      const requestBody = {
        type: importType,
        platform: importType === 'platform' ? selectedPlatform : undefined,
        modelsPerPlatform: importType === 'sample' ? modelsPerPlatform : undefined,
        adminKey
      };

      const response = await fetch('/api/admin/import-popular', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      const result = await response.json();

      if (result.success) {
        setMessage(`Імпорт запущено! Очікуваний час: ${result.data.estimatedDuration}`);
        // Почати моніторинг прогресу
        setTimeout(fetchProgress, 2000);
      } else {
        setError(result.error.message);
        setIsImporting(false);
      }
    } catch (error) {
      setError('Помилка запуску імпорту');
      setIsImporting(false);
    }
  };

  const getProgressColor = (successful: number, total: number) => {
    const rate = total > 0 ? (successful / total) * 100 : 0;
    if (rate >= 90) return 'bg-green-500';
    if (rate >= 70) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const formatPlatformName = (platform: string) => {
    return platforms.find(p => p.value === platform)?.label || platform;
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="w-5 h-5" />
            Імпорт популярних 3D моделей
          </CardTitle>
          <CardDescription>
            Автоматичний імпорт найпопулярніших моделей з усіх підтримуваних платформ
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Admin Key */}
          <div className="space-y-2">
            <Label htmlFor="adminKey">Адмін ключ</Label>
            <Input
              id="adminKey"
              type="password"
              value={adminKey}
              onChange={(e) => setAdminKey(e.target.value)}
              placeholder="Введіть адмін ключ"
            />
          </div>

          {/* Import Type */}
          <div className="space-y-2">
            <Label>Тип імпорту</Label>
            <Select value={importType} onValueChange={setImportType}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {importTypes.map(type => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Platform Selection (if needed) */}
          {importType === 'platform' && (
            <div className="space-y-2">
              <Label>Платформа</Label>
              <Select value={selectedPlatform} onValueChange={setSelectedPlatform}>
                <SelectTrigger>
                  <SelectValue placeholder="Оберіть платформу" />
                </SelectTrigger>
                <SelectContent>
                  {platforms.map(platform => (
                    <SelectItem key={platform.value} value={platform.value}>
                      {platform.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Models per platform (for sample) */}
          {importType === 'sample' && (
            <div className="space-y-2">
              <Label htmlFor="modelsPerPlatform">Моделей з кожної платформи</Label>
              <Input
                id="modelsPerPlatform"
                type="number"
                min="1"
                max="10"
                value={modelsPerPlatform}
                onChange={(e) => setModelsPerPlatform(parseInt(e.target.value) || 3)}
              />
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-2">
            <Button 
              onClick={startImport} 
              disabled={isImporting || !adminKey}
              className="flex items-center gap-2"
            >
              {isImporting ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Play className="w-4 h-4" />
              )}
              {isImporting ? 'Імпорт триває...' : 'Запустити імпорт'}
            </Button>

            <Button 
              variant="outline" 
              onClick={fetchProgress}
              disabled={!adminKey}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Оновити прогрес
            </Button>
          </div>

          {/* Messages */}
          {message && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertTitle>Успіх</AlertTitle>
              <AlertDescription>{message}</AlertDescription>
            </Alert>
          )}

          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Помилка</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Progress Summary */}
      {summary && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              Загальний прогрес
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{summary.totalModels}</div>
                <div className="text-sm text-muted-foreground">Всього моделей</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{summary.successfulModels}</div>
                <div className="text-sm text-muted-foreground">Успішно</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{summary.failedModels}</div>
                <div className="text-sm text-muted-foreground">Помилок</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{summary.totalPlatforms}</div>
                <div className="text-sm text-muted-foreground">Платформ</div>
              </div>
            </div>
            
            <Progress 
              value={(summary.completedModels / summary.totalModels) * 100} 
              className="h-3"
            />
            <div className="text-center text-sm text-muted-foreground mt-2">
              {summary.completedModels} з {summary.totalModels} завершено 
              ({Math.round((summary.completedModels / summary.totalModels) * 100)}%)
            </div>
          </CardContent>
        </Card>
      )}

      {/* Platform Progress */}
      {progress.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="w-5 h-5" />
              Прогрес по платформах
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {progress.map((platform) => (
                <div key={platform.platform} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">
                        {formatPlatformName(platform.platform)}
                      </span>
                      <Badge variant="outline">
                        {platform.completed}/{platform.total}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      {platform.completed === platform.total ? (
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      ) : (
                        <Clock className="w-4 h-4 text-blue-500" />
                      )}
                      <span className="text-sm text-muted-foreground">
                        {platform.successful} успішно, {platform.failed} помилок
                      </span>
                    </div>
                  </div>
                  
                  <Progress 
                    value={(platform.completed / platform.total) * 100}
                    className={`h-2 ${getProgressColor(platform.successful, platform.total)}`}
                  />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
