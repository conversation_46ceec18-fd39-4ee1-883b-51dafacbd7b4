/**
 * Скрипт для імпорту найпопулярніших 3D моделей
 * Використовує нашу систему черг та Cloudflare інтеграцію
 */

import { jobQueueManager } from '@/lib/queue/job-queue-manager';
import { cloudflareMonitoring } from '@/lib/observability/cloudflare-monitoring';
import { ScraperManager } from '@/lib/scrapers/scraper-manager';

// Найпопулярніші моделі з різних платформ
const POPULAR_MODELS = {
  thingiverse: [
    'https://www.thingiverse.com/thing:3495390', // Articulated Dragon
    'https://www.thingiverse.com/thing:2975429', // Flexi Rex
    'https://www.thingiverse.com/thing:3148793', // Baby Groot
    'https://www.thingiverse.com/thing:2187167', // Low Poly Pokemon
    'https://www.thingiverse.com/thing:3364987', // Octopus
    'https://www.thingiverse.com/thing:1363023', // Benchy
    'https://www.thingiverse.com/thing:763622',  // Moon Lamp
    'https://www.thingiverse.com/thing:2738344', // Fidget Spinner
    'https://www.thingiverse.com/thing:1278865', // Whistle
    'https://www.thingiverse.com/thing:2064029', // Spiral Vase
  ],
  myminifactory: [
    'https://www.myminifactory.com/object/3d-print-articulated-dragon-mcgybeer-738',
    'https://www.myminifactory.com/object/3d-print-baby-groot-865',
    'https://www.myminifactory.com/object/3d-print-flexi-rex-with-stronger-links-12026',
    'https://www.myminifactory.com/object/3d-print-octopus-1953',
    'https://www.myminifactory.com/object/3d-print-low-poly-pokemon-bulbasaur-13127',
    'https://www.myminifactory.com/object/3d-print-cute-mini-octopus-2648',
    'https://www.myminifactory.com/object/3d-print-keychain-phone-stand-4321',
    'https://www.myminifactory.com/object/3d-print-miniature-house-5432',
    'https://www.myminifactory.com/object/3d-print-desk-organizer-6543',
    'https://www.myminifactory.com/object/3d-print-phone-holder-7654',
  ],
  printables: [
    'https://www.printables.com/model/25202-flexi-rex-with-stronger-links',
    'https://www.printables.com/model/164919-baby-groot',
    'https://www.printables.com/model/136925-articulated-dragon',
    'https://www.printables.com/model/178432-octopus-flexible',
    'https://www.printables.com/model/145678-pokemon-collection',
    'https://www.printables.com/model/123456-desk-organizer',
    'https://www.printables.com/model/234567-phone-stand',
    'https://www.printables.com/model/345678-keychain-collection',
    'https://www.printables.com/model/456789-miniature-furniture',
    'https://www.printables.com/model/567890-tool-holder',
  ],
  thangs: [
    'https://thangs.com/designer/McGybeer/3d-model/Articulated%20Dragon-31297',
    'https://thangs.com/designer/rocketpiggames/3d-model/Baby%20Groot-58291',
    'https://thangs.com/designer/kirbs/3d-model/Flexi%20Rex-42156',
    'https://thangs.com/designer/FLOWALISTIK/3d-model/Octopus-73829',
    'https://thangs.com/designer/loubie/3d-model/Low%20Poly%20Pokemon-91847',
    'https://thangs.com/designer/CreativeTools/3d-model/Desk%20Organizer-18374',
    'https://thangs.com/designer/MakerBot/3d-model/Phone%20Stand-29485',
    'https://thangs.com/designer/Thingiverse/3d-model/Keychain-37596',
    'https://thangs.com/designer/MyMiniFactory/3d-model/Tool%20Holder-48607',
    'https://thangs.com/designer/Printables/3d-model/Miniature%20Set-59718',
  ],
  makerworld: [
    'https://makerworld.com/models/detail/123456',
    'https://makerworld.com/models/detail/234567',
    'https://makerworld.com/models/detail/345678',
    'https://makerworld.com/models/detail/456789',
    'https://makerworld.com/models/detail/567890',
    'https://makerworld.com/models/detail/678901',
    'https://makerworld.com/models/detail/789012',
    'https://makerworld.com/models/detail/890123',
    'https://makerworld.com/models/detail/901234',
    'https://makerworld.com/models/detail/012345',
  ]
};

interface ImportProgress {
  platform: string;
  total: number;
  completed: number;
  successful: number;
  failed: number;
  jobId: string;
}

export class PopularModelsImporter {
  private scraperManager: ScraperManager;
  private importProgress: Map<string, ImportProgress> = new Map();

  constructor() {
    this.scraperManager = new ScraperManager();
  }

  /**
   * Запуск імпорту всіх популярних моделей
   */
  async importAllPopularModels(): Promise<void> {
    console.log('🚀 Початок імпорту найпопулярніших 3D моделей...');
    
    await cloudflareMonitoring.logEvent({
      level: 'info',
      message: 'Starting popular models import',
      service: 'popular-importer',
      metadata: {
        totalPlatforms: Object.keys(POPULAR_MODELS).length,
        totalModels: Object.values(POPULAR_MODELS).flat().length
      }
    });

    // Запуск імпорту для кожної платформи
    const importPromises = Object.entries(POPULAR_MODELS).map(([platform, urls]) =>
      this.importPlatformModels(platform, urls)
    );

    await Promise.all(importPromises);
    
    console.log('✅ Імпорт завершено!');
    await this.printFinalSummary();
  }

  /**
   * Імпорт моделей з конкретної платформи
   */
  private async importPlatformModels(platform: string, urls: string[]): Promise<void> {
    console.log(`📥 Імпорт з ${platform}: ${urls.length} моделей`);

    try {
      // Створення завдання в черзі
      const jobId = await this.scraperManager.addBatchImportJob(urls, 'system');
      
      // Ініціалізація прогресу
      this.importProgress.set(platform, {
        platform,
        total: urls.length,
        completed: 0,
        successful: 0,
        failed: 0,
        jobId
      });

      await cloudflareMonitoring.trackJobQueueOperation('job_created', jobId, {
        platform,
        urlCount: urls.length
      });

      // Моніторинг прогресу
      await this.monitorImportProgress(platform, jobId);

    } catch (error) {
      console.error(`❌ Помилка імпорту з ${platform}:`, error);
      
      await cloudflareMonitoring.logEvent({
        level: 'error',
        message: `Failed to import from ${platform}`,
        service: 'popular-importer',
        platform: platform as any,
        metadata: { error: error instanceof Error ? error.message : 'Unknown error' }
      });
    }
  }

  /**
   * Моніторинг прогресу імпорту
   */
  private async monitorImportProgress(platform: string, jobId: string): Promise<void> {
    return new Promise((resolve) => {
      const checkProgress = async () => {
        const job = jobQueueManager.getJob(jobId);
        if (!job) {
          console.log(`⚠️ Завдання ${jobId} не знайдено`);
          resolve();
          return;
        }

        const progress = this.importProgress.get(platform);
        if (progress) {
          progress.completed = job.progress.current;
          progress.successful = job.result?.data?.summary?.successful || 0;
          progress.failed = job.result?.data?.summary?.failed || 0;
        }

        // Виведення прогресу
        console.log(`📊 ${platform}: ${job.progress.current}/${job.progress.total} (${job.progress.percentage}%) - ${job.progress.message}`);

        if (job.status === 'completed' || job.status === 'failed') {
          console.log(`✅ ${platform} завершено: ${progress?.successful} успішно, ${progress?.failed} помилок`);
          
          await cloudflareMonitoring.trackJobQueueOperation(
            job.status === 'completed' ? 'job_completed' : 'job_failed',
            jobId,
            {
              platform,
              successful: progress?.successful,
              failed: progress?.failed,
              duration: job.completedAt ? 
                new Date(job.completedAt).getTime() - new Date(job.createdAt).getTime() : 0
            }
          );
          
          resolve();
        } else {
          // Перевірка через 5 секунд
          setTimeout(checkProgress, 5000);
        }
      };

      checkProgress();
    });
  }

  /**
   * Виведення підсумкової статистики
   */
  private async printFinalSummary(): Promise<void> {
    console.log('\n📈 ПІДСУМКОВА СТАТИСТИКА ІМПОРТУ:');
    console.log('=' .repeat(50));

    let totalModels = 0;
    let totalSuccessful = 0;
    let totalFailed = 0;

    for (const [platform, progress] of this.importProgress.entries()) {
      totalModels += progress.total;
      totalSuccessful += progress.successful;
      totalFailed += progress.failed;

      const successRate = ((progress.successful / progress.total) * 100).toFixed(1);
      
      console.log(`${platform.toUpperCase()}:`);
      console.log(`  📊 Всього: ${progress.total}`);
      console.log(`  ✅ Успішно: ${progress.successful} (${successRate}%)`);
      console.log(`  ❌ Помилок: ${progress.failed}`);
      console.log('');
    }

    const overallSuccessRate = ((totalSuccessful / totalModels) * 100).toFixed(1);
    
    console.log('ЗАГАЛЬНА СТАТИСТИКА:');
    console.log(`📊 Всього моделей: ${totalModels}`);
    console.log(`✅ Успішно імпортовано: ${totalSuccessful} (${overallSuccessRate}%)`);
    console.log(`❌ Помилок: ${totalFailed}`);
    console.log(`🎯 Платформ: ${this.importProgress.size}`);

    // Запис фінальних метрик
    await cloudflareMonitoring.recordMetric({
      name: 'popular_import.total_models',
      value: totalModels,
      tags: { type: 'final_summary' }
    });

    await cloudflareMonitoring.recordMetric({
      name: 'popular_import.successful_models',
      value: totalSuccessful,
      tags: { type: 'final_summary' }
    });

    await cloudflareMonitoring.recordMetric({
      name: 'popular_import.success_rate',
      value: parseFloat(overallSuccessRate),
      unit: 'percent',
      tags: { type: 'final_summary' }
    });

    await cloudflareMonitoring.logEvent({
      level: 'info',
      message: 'Popular models import completed',
      service: 'popular-importer',
      metadata: {
        totalModels,
        totalSuccessful,
        totalFailed,
        successRate: overallSuccessRate,
        platforms: Array.from(this.importProgress.keys())
      }
    });
  }

  /**
   * Отримання поточного прогресу
   */
  getProgress(): ImportProgress[] {
    return Array.from(this.importProgress.values());
  }

  /**
   * Імпорт тільки з конкретної платформи
   */
  async importFromPlatform(platform: keyof typeof POPULAR_MODELS): Promise<void> {
    const urls = POPULAR_MODELS[platform];
    if (!urls) {
      throw new Error(`Платформа ${platform} не підтримується`);
    }

    console.log(`🎯 Імпорт тільки з ${platform}...`);
    await this.importPlatformModels(platform, urls);
  }

  /**
   * Імпорт обмеженої кількості моделей з кожної платформи
   */
  async importSampleModels(modelsPerPlatform: number = 3): Promise<void> {
    console.log(`🎲 Імпорт зразків: ${modelsPerPlatform} моделей з кожної платформи`);

    const sampleModels: Record<string, string[]> = {};
    
    for (const [platform, urls] of Object.entries(POPULAR_MODELS)) {
      sampleModels[platform] = urls.slice(0, modelsPerPlatform);
    }

    const importPromises = Object.entries(sampleModels).map(([platform, urls]) =>
      this.importPlatformModels(platform, urls)
    );

    await Promise.all(importPromises);
    await this.printFinalSummary();
  }
}

// Експорт для використання в інших файлах
export const popularModelsImporter = new PopularModelsImporter();
