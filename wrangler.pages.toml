# Cloudflare Pages configuration for 3D Marketplace
name = "3d-marketplace"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# Pages configuration
pages_build_output_dir = ".next"

# Environment variables
[env.production.vars]
ENVIRONMENT = "production"
NEXT_PUBLIC_APP_URL = "https://3d-marketplace.pages.dev"
NEXT_PUBLIC_APP_NAME = "3D Marketplace"
NODE_ENV = "production"
ENABLE_CLOUDFLARE_ANALYTICS = "true"
ENABLE_MONITORING = "true"
RATE_LIMIT_ENABLED = "true"

[env.staging.vars]
ENVIRONMENT = "staging"
NEXT_PUBLIC_APP_URL = "https://3d-marketplace-staging.pages.dev"
NEXT_PUBLIC_APP_NAME = "3D Marketplace (Staging)"
NODE_ENV = "staging"
ENABLE_CLOUDFLARE_ANALYTICS = "true"
ENABLE_MONITORING = "true"
RATE_LIMIT_ENABLED = "false"

# D1 Database bindings
[[env.production.d1_databases]]
binding = "DB"
database_name = "3d-marketplace-enhanced"
database_id = "9dc65021-64f6-48f8-b6c3-bfc817746584"

[[env.staging.d1_databases]]
binding = "DB"
database_name = "3d-marketplace-staging"
database_id = "bc8fb549-1e21-4652-804c-2b5412d57050"

# KV Storage bindings
[[env.production.kv_namespaces]]
binding = "CACHE_KV"
id = "768cd42bbee14bce81819a0ef3666930"

[[env.production.kv_namespaces]]
binding = "JOB_QUEUE_KV"
id = "dc0b307ecd034ccd8d44223242adea57"

[[env.staging.kv_namespaces]]
binding = "CACHE_KV"
id = "3d5f7712807c403a93c41f0dfd6401d4"

# R2 Storage bindings
[[env.production.r2_buckets]]
binding = "R2_BUCKET"
bucket_name = "3d-marketplace-models-prod"

[[env.staging.r2_buckets]]
binding = "R2_BUCKET"
bucket_name = "3d-marketplace-models-staging"

# Analytics Engine bindings
[[env.production.analytics_engine_datasets]]
binding = "ANALYTICS"
dataset = "3d_marketplace_analytics"

[[env.staging.analytics_engine_datasets]]
binding = "ANALYTICS"
dataset = "3d_marketplace_analytics_staging"
