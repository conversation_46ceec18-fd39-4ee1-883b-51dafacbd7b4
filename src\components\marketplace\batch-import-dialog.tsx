'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Download, AlertCircle, CheckCircle, Upload, X } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { ModelSource } from '@/types/models';

interface BatchImportDialogProps {
  onBatchCompleted?: (results: any) => void;
}

interface BatchJob {
  id: string;
  status: 'processing' | 'completed' | 'failed';
  total: number;
  completed: number;
  failed: number;
  results: Array<{
    url: string;
    status: 'success' | 'failed';
    modelId?: string;
    error?: string;
  }>;
}

export const BatchImportDialog: React.FC<BatchImportDialogProps> = ({ onBatchCompleted }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [urls, setUrls] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [batchJob, setBatchJob] = useState<BatchJob | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Platform configurations
  const platforms: Record<string, { name: string; icon: string }> = {
    printables: { name: 'Printables', icon: '🖨️' },
    makerworld: { name: 'MakerWorld', icon: '🌍' },
    thangs: { name: 'Thangs', icon: '🔧' },
  };

  const detectPlatform = (url: string): ModelSource | null => {
    if (/^https?:\/\/(www\.)?printables\.com\/model\/\d+/.test(url)) return 'printables';
    if (/^https?:\/\/(www\.)?makerworld\.com\/(en\/)?models\/\d+/.test(url)) return 'makerworld';
    if (/^https?:\/\/(www\.)?thangs\.com\/designer\/[^\/]+\/model\/\d+/.test(url)) return 'thangs';
    return null;
  };

  const parseUrls = (text: string): string[] => {
    return text
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0 && line.startsWith('http'));
  };

  const validateUrls = (urlList: string[]): { valid: string[]; invalid: string[] } => {
    const valid: string[] = [];
    const invalid: string[] = [];

    urlList.forEach(url => {
      if (detectPlatform(url)) {
        valid.push(url);
      } else {
        invalid.push(url);
      }
    });

    return { valid, invalid };
  };

  const handleStartBatch = async () => {
    const urlList = parseUrls(urls);
    
    if (urlList.length === 0) {
      setError('Please enter at least one URL');
      return;
    }

    if (urlList.length > 50) {
      setError('Maximum 50 URLs allowed per batch');
      return;
    }

    const { valid, invalid } = validateUrls(urlList);
    
    if (invalid.length > 0) {
      setError(`${invalid.length} invalid URLs found. Please check your URLs and try again.`);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/scraping/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          urls: valid,
          options: {
            parallel: 3,
            retryFailed: true,
            includeFiles: true,
            includeImages: true,
          },
        }),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to start batch import');
      }

      setBatchJob({
        id: result.data.batchId,
        status: 'processing',
        total: result.data.total,
        completed: 0,
        failed: 0,
        results: [],
      });

      // Start polling for status
      pollBatchStatus(result.data.batchId);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
      setIsLoading(false);
    }
  };

  const pollBatchStatus = async (batchId: string) => {
    const poll = async () => {
      try {
        const response = await fetch(`/api/scraping/batch?batchId=${batchId}`);
        const result = await response.json();

        if (result.success) {
          setBatchJob(result.data);

          if (result.data.status === 'completed' || result.data.status === 'failed') {
            setIsLoading(false);
            onBatchCompleted?.(result.data);
          } else {
            // Continue polling
            setTimeout(poll, 2000);
          }
        }
      } catch (error) {
        console.error('Polling error:', error);
        setTimeout(poll, 5000); // Retry after 5 seconds on error
      }
    };

    poll();
  };

  const resetForm = () => {
    setUrls('');
    setBatchJob(null);
    setError(null);
    setIsLoading(false);
  };

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      resetForm();
    }
  };

  const urlList = parseUrls(urls);
  const { valid, invalid } = validateUrls(urlList);
  const progress = batchJob ? (batchJob.completed + batchJob.failed) / batchJob.total * 100 : 0;

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button variant="outline" className="gap-2">
          <Upload className="h-4 w-4" />
          Batch Import
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Batch Import Models</DialogTitle>
          <DialogDescription>
            Import multiple models from Printables, MakerWorld, or Thangs at once
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* URL Input */}
          <div className="space-y-2">
            <Label htmlFor="urls">Model URLs (one per line)</Label>
            <Textarea
              id="urls"
              placeholder={`https://www.printables.com/model/123456-example-model
https://makerworld.com/en/models/789012
https://thangs.com/designer/user/model/345678`}
              value={urls}
              onChange={(e) => setUrls(e.target.value)}
              disabled={isLoading}
              rows={8}
              className="font-mono text-sm"
            />
            
            {/* URL Statistics */}
            {urlList.length > 0 && (
              <div className="flex gap-4 text-sm">
                <span className="text-green-600">✓ {valid.length} valid</span>
                {invalid.length > 0 && (
                  <span className="text-red-600">✗ {invalid.length} invalid</span>
                )}
                <span className="text-gray-600">Total: {urlList.length}</span>
              </div>
            )}
          </div>

          {/* Platform Distribution */}
          {valid.length > 0 && (
            <div className="space-y-2">
              <Label>Platform Distribution</Label>
              <div className="flex flex-wrap gap-2">
                {Object.entries(
                  valid.reduce((acc, url) => {
                    const platform = detectPlatform(url);
                    if (platform) {
                      acc[platform] = (acc[platform] || 0) + 1;
                    }
                    return acc;
                  }, {} as Record<string, number>)
                ).map(([platform, count]) => (
                  <Badge key={platform} variant="outline" className="gap-1">
                    <span>{platforms[platform]?.icon}</span>
                    {platforms[platform]?.name}: {count}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Error Alert */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Batch Progress */}
          {batchJob && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  {batchJob.status === 'processing' && <Loader2 className="h-4 w-4 animate-spin" />}
                  {batchJob.status === 'completed' && <CheckCircle className="h-4 w-4 text-green-600" />}
                  Batch Import Progress
                </CardTitle>
                <CardDescription>
                  {batchJob.completed + batchJob.failed} of {batchJob.total} models processed
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Progress value={progress} className="w-full" />
                
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{batchJob.completed}</div>
                    <div className="text-gray-600">Completed</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{batchJob.failed}</div>
                    <div className="text-gray-600">Failed</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {batchJob.total - batchJob.completed - batchJob.failed}
                    </div>
                    <div className="text-gray-600">Remaining</div>
                  </div>
                </div>

                {/* Results */}
                {batchJob.results.length > 0 && (
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    <Label>Results</Label>
                    {batchJob.results.map((result, index) => (
                      <div key={index} className="flex items-center gap-2 text-sm p-2 bg-gray-50 rounded">
                        {result.status === 'success' ? (
                          <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                        ) : (
                          <X className="h-4 w-4 text-red-600 flex-shrink-0" />
                        )}
                        <span className="truncate flex-1">{result.url}</span>
                        {result.status === 'failed' && result.error && (
                          <span className="text-red-600 text-xs">{result.error}</span>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            {batchJob?.status === 'completed' ? 'Close' : 'Cancel'}
          </Button>
          {!batchJob && (
            <Button 
              onClick={handleStartBatch} 
              disabled={isLoading || valid.length === 0}
              className="gap-2"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Upload className="h-4 w-4" />
              )}
              Start Batch Import ({valid.length} models)
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
