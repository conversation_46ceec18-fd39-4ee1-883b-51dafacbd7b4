# Contributing to 3D Printing Marketplace

Thank you for considering contributing to the 3D Printing Marketplace! This document outlines the process for contributing to the project and how to get started.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Workflow](#development-workflow)
- [Pull Request Process](#pull-request-process)
- [Coding Standards](#coding-standards)
- [Testing](#testing)
- [Documentation](#documentation)
- [Community](#community)

## Code of Conduct

Our project adheres to a Code of Conduct that we expect all contributors to follow. Please read [CODE_OF_CONDUCT.md](CODE_OF_CONDUCT.md) before contributing.

## Getting Started

1. **Fork the repository** on GitHub
2. **Clone your fork** locally
   ```bash
   git clone https://github.com/YOUR-USERNAME/3d-marketplace.git
   cd 3d-marketplace
   ```
3. **Add the upstream repository** as a remote
   ```bash
   git remote add upstream https://github.com/ORIGINAL-OWNER/3d-marketplace.git
   ```
4. **Install dependencies**
   ```bash
   npm install
   ```
5. **Create a new branch** for your feature or bugfix
   ```bash
   git checkout -b feature/your-feature-name
   ```

## Development Workflow

1. **Make your changes** in your feature branch
2. **Write or update tests** for your changes
3. **Run tests** to ensure they pass
   ```bash
   npm test
   ```
4. **Run linting** to ensure code quality
   ```bash
   npm run lint
   ```
5. **Commit your changes** with a descriptive commit message
   ```bash
   git commit -m "Add feature: your feature description"
   ```
6. **Push to your fork**
   ```bash
   git push origin feature/your-feature-name
   ```
7. **Create a Pull Request** from your fork to the main repository

## Pull Request Process

1. **Update the README.md** with details of changes if applicable
2. **Update the documentation** if you're changing functionality
3. **Ensure all tests pass** and linting is successful
4. **Request a review** from at least one maintainer
5. **Address any feedback** from reviewers
6. **Once approved**, your PR will be merged by a maintainer

## Coding Standards

We follow specific coding standards to maintain consistency across the codebase:

### JavaScript/TypeScript

- Use TypeScript for all new code
- Follow the [Airbnb JavaScript Style Guide](https://github.com/airbnb/javascript)
- Use async/await instead of Promises where possible
- Use meaningful variable and function names

### React

- Use functional components with hooks
- Follow the [React Hooks guidelines](https://reactjs.org/docs/hooks-rules.html)
- Use Next.js best practices for routing and data fetching
- Keep components small and focused on a single responsibility

### CSS/Styling

- Use Tailwind CSS for styling
- Follow the [Tailwind CSS best practices](https://tailwindcss.com/docs/utility-first)
- Use CSS modules for component-specific styles when needed

## Testing

We use Jest and React Testing Library for testing:

- **Unit tests** for individual functions and components
- **Integration tests** for component interactions
- **End-to-end tests** for critical user flows

When writing tests:

- Test the behavior, not the implementation
- Mock external dependencies
- Aim for high test coverage on critical paths
- Run tests before submitting a PR

## Documentation

Good documentation is crucial for the project:

- Document all public APIs and components
- Update documentation when changing functionality
- Use JSDoc comments for functions and components
- Keep the README and other documentation up to date

## Community

Join our community channels to get help and discuss the project:

- **Discord**: [Join our server](https://discord.gg/example)
- **GitHub Discussions**: For feature requests and general discussion
- **GitHub Issues**: For bug reports and specific tasks

Thank you for contributing to the 3D Printing Marketplace!
