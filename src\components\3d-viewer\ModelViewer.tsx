'use client';

import { useEffect, useRef, useState } from 'react';
import { Canvas, useThree } from '@react-three/fiber';
import { OrbitControls, useGLTF, Environment, Stage } from '@react-three/drei';

interface ModelViewerProps {
  modelUrl: string;
  backgroundColor?: string;
  autoRotate?: boolean;
  showControls?: boolean;
}

function Model({ url }: { url: string }) {
  const { scene } = useGLTF(url);
  
  return <primitive object={scene} />;
}

function CameraController() {
  const { camera, gl } = useThree();
  
  useEffect(() => {
    camera.position.set(0, 0, 5);
    camera.lookAt(0, 0, 0);
  }, [camera]);
  
  return null;
}

export default function ModelViewer({
  modelUrl,
  backgroundColor = '#f3f4f6',
  autoRotate = true,
  showControls = true,
}: ModelViewerProps) {
  const [loading, setLoading] = useState(true);
  const containerRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    // Simulate loading time
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1500);
    
    return () => clearTimeout(timer);
  }, []);
  
  return (
    <div 
      ref={containerRef}
      className="relative w-full h-full min-h-[400px] rounded-lg overflow-hidden"
      style={{ backgroundColor }}
    >
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-75 z-10">
          <div className="flex flex-col items-center">
            <svg className="animate-spin h-10 w-10 text-blue-600 mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span className="text-gray-700">Loading 3D model...</span>
          </div>
        </div>
      )}
      
      <Canvas>
        <CameraController />
        <ambientLight intensity={0.5} />
        <spotLight position={[10, 10, 10]} angle={0.15} penumbra={1} />
        <pointLight position={[-10, -10, -10]} />
        
        <Stage environment="city" intensity={0.6}>
          {!loading && <Model url={modelUrl} />}
        </Stage>
        
        {showControls && <OrbitControls autoRotate={autoRotate} autoRotateSpeed={1} />}
        <Environment preset="sunset" />
      </Canvas>
      
      {showControls && (
        <div className="absolute bottom-4 right-4 bg-white bg-opacity-75 rounded-lg p-2 text-xs text-gray-700">
          <p>Left click + drag: Rotate</p>
          <p>Right click + drag: Pan</p>
          <p>Scroll: Zoom</p>
        </div>
      )}
    </div>
  );
}
