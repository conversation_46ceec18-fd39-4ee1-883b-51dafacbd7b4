{"framework": "nextjs", "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm install", "devCommand": "npm run dev", "env": {"NODE_ENV": "production", "RATE_LIMIT_PRINTABLES": "10", "RATE_LIMIT_MAKERWORLD": "15", "RATE_LIMIT_THANGS": "12", "SCRAPING_TIMEOUT": "30000", "SCRAPING_RETRY_ATTEMPTS": "3", "SCRAPING_RETRY_DELAY": "1000", "SCRAPING_USER_AGENT": "3D-Marketplace-Bot/1.0", "PRINTABLES_BASE_URL": "https://www.printables.com", "MAKERWORLD_BASE_URL": "https://makerworld.com", "THANGS_BASE_URL": "https://thangs.com"}, "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "rewrites": [{"source": "/api/scraping/(.*)", "destination": "/api/scraping/$1"}]}