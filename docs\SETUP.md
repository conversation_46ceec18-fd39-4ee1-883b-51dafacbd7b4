# Setup Guide for 3D Printing Marketplace

This guide provides detailed instructions for setting up the 3D Printing Marketplace project for development.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Environment Configuration](#environment-configuration)
- [Database Setup](#database-setup)
- [Authentication Setup](#authentication-setup)
- [Stripe Integration](#stripe-integration)
- [3D Model Viewer Setup](#3d-model-viewer-setup)
- [Running the Application](#running-the-application)
- [Troubleshooting](#troubleshooting)

## Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (v18.0.0 or later)
  - [Download from nodejs.org](https://nodejs.org/)
  - Verify with `node -v`

- **npm** (v8.0.0 or later) or **yarn** (v1.22.0 or later)
  - npm comes with Node.js
  - Verify with `npm -v` or `yarn -v`

- **Git**
  - [Download from git-scm.com](https://git-scm.com/)
  - Verify with `git --version`

- **Code Editor** (recommended: Visual Studio Code)
  - [Download from code.visualstudio.com](https://code.visualstudio.com/)

## Installation

1. **Clone the repository**

   ```bash
   git clone https://github.com/yourusername/3d-marketplace.git
   cd 3d-marketplace
   ```

2. **Install dependencies**

   Using npm:

   ```bash
   npm install
   ```

   Or using yarn:

   ```bash
   yarn install
   ```

## Environment Configuration

1. **Create environment file**

   Copy the example environment file:

   ```bash
   cp .env.example .env.local
   ```

2. **Configure environment variables**

   Open `.env.local` in your editor and update the following variables:

   ```
   # Base URL
   NEXT_PUBLIC_BASE_URL=http://localhost:3000

   # Supabase Configuration
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

   # NextAuth Configuration
   NEXTAUTH_URL=http://localhost:3000
   NEXTAUTH_SECRET=your_nextauth_secret

   # OAuth Providers (if using)
   GOOGLE_CLIENT_ID=your_google_client_id
   GOOGLE_CLIENT_SECRET=your_google_client_secret
   GITHUB_ID=your_github_id
   GITHUB_SECRET=your_github_secret

   # Stripe Configuration
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
   STRIPE_SECRET_KEY=your_stripe_secret_key
   STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
   ```

## Database Setup

We use Supabase as our database provider. Follow these steps to set up your database:

1. **Create a Supabase account**
   - Sign up at [supabase.com](https://supabase.com/)

2. **Create a new project**
   - Note down your project URL and API keys

3. **Set up database schema**
   - Run the SQL scripts in the `database/schema` directory in your Supabase SQL editor
   - Or use the Supabase migration tool:

     ```bash
     npm run db:migrate
     ```

4. **Seed the database** (optional)
   - Run the seed script to populate your database with test data:

     ```bash
     npm run db:seed
     ```

## Authentication Setup

1. **Configure NextAuth.js**
   - Update the NextAuth configuration in `src/app/api/auth/[...nextauth]/route.ts`
   - Add your OAuth providers if needed

2. **Set up OAuth providers** (optional)
   - Create developer accounts with providers (Google, GitHub, etc.)
   - Configure the callback URLs to point to your NextAuth endpoints

## Stripe Integration

1. **Create a Stripe account**
   - Sign up at [stripe.com](https://stripe.com/)

2. **Get API keys**
   - Obtain your publishable and secret keys from the Stripe dashboard
   - Add them to your `.env.local` file

3. **Set up webhook**
   - Create a webhook endpoint in your Stripe dashboard
   - Point it to `https://your-domain.com/api/webhooks/stripe`
   - For local development, use Stripe CLI or a service like ngrok

## 3D Model Viewer Setup

The project uses Three.js and React Three Fiber for 3D model visualization:

1. **Install dependencies**
   - These should be installed automatically with `npm install`

2. **Configure model formats**
   - The viewer supports STL, OBJ, and GLTF formats
   - Update supported formats in `src/components/3d-viewer/ModelViewer.tsx` if needed

## Running the Application

1. **Start the development server**

   ```bash
   npm run dev
   ```

   Or using yarn:

   ```bash
   yarn dev
   ```

2. **Access the application**
   - Open your browser and navigate to [http://localhost:3000](http://localhost:3000)

3. **Build for production** (when ready)

   ```bash
   npm run build
   npm start
   ```

## Troubleshooting

### Common Issues

1. **"Module not found" errors**
   - Ensure all dependencies are installed: `npm install`
   - Check for typos in import statements

2. **Environment variable issues**
   - Verify your `.env.local` file contains all required variables
   - Restart the development server after changing environment variables

3. **Database connection errors**
   - Check your Supabase credentials
   - Ensure your IP is allowed in Supabase settings

4. **3D model viewer issues**
   - Check browser console for WebGL errors
   - Ensure your browser supports WebGL

### Getting Help

If you encounter issues not covered here:

1. Check the [GitHub Issues](https://github.com/yourusername/3d-marketplace/issues) for similar problems
2. Join our [Discord community](https://discord.gg/example) for real-time help
3. Create a new issue with detailed information about your problem
