#!/usr/bin/env tsx

/**
 * API Import Testing Script
 * Tests the 3D model import functionality through API endpoints
 */

interface ApiResponse {
  success: boolean;
  data?: any;
  error?: { message: string };
}

interface JobProgress {
  current: number;
  total: number;
  percentage: number;
  message: string;
}

interface BatchJob {
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: JobProgress;
  result?: {
    data?: {
      summary?: {
        successful: number;
        failed: number;
        total: number;
      };
    };
  };
}

async function testAPIImport() {
  console.log('🌐 Testing API Import Functionality');
  console.log('='.repeat(50));

  const testUrls = [
    'https://www.printables.com/model/274230-articulated-dragon',
    'https://makerworld.com/en/models/16343',
    'https://thangs.com/designer/CreativeTools/3d-model/Articulated%20Dragon%20-%20Print%20in%20Place-81468',
    'https://www.thingiverse.com/thing:3495390',
    'https://www.myminifactory.com/object/3d-print-articulated-dragon-mcgybeer-738'
  ];

  // URL Validation Test
  console.log('\n🔍 Testing URL Validation...');
  for (const url of testUrls) {
    try {
      const response = await fetch(`http://localhost:3000/api/scraping/single-import/validate?url=${encodeURIComponent(url)}`);
      const result = await response.json() as ApiResponse;

      console.log(`📋 ${url}:`);
      console.log(`  Platform: ${result.data?.platform}`);
      console.log(`  Supported: ${result.data?.supported}`);
      console.log(`  Can Import: ${result.data?.canImport}`);
      console.log(`  Estimated Time: ${result.data?.estimatedTime}s`);

      if (result.data?.warnings?.length > 0) {
        console.log(`  Warnings: ${result.data.warnings.join(', ')}`);
      }
    } catch (error) {
      console.error(`❌ Validation error for ${url}:`, error);
    }
  }

  // Single Import Test
  console.log('\n📥 Testing Single Model Import...');
  for (const url of testUrls) {
    try {
      const response = await fetch('http://localhost:3000/api/scraping/single-import', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          url,
          options: { saveToDatabase: false } // Don't save to database for testing
        })
      });

      const result = await response.json() as ApiResponse;

      if (result.success) {
        console.log(`✅ Successful import from ${result.data.platform}:`);
        console.log(`  Name: ${result.data.model.name}`);
        console.log(`  Author: ${result.data.model.author_name}`);
        console.log(`  Category: ${result.data.model.category}`);
        console.log(`  Free: ${result.data.model.is_free}`);
        console.log(`  File Formats: ${result.data.model.file_formats?.join(', ')}`);
      } else {
        console.error(`❌ Import error for ${url}:`, result.error?.message || 'Unknown error');
      }
    } catch (error) {
      console.error(`💥 Critical error for ${url}:`, error);
    }
  }

  // Batch Import Test
  console.log('\n📦 Testing Batch Import...');
  try {
    const response = await fetch('http://localhost:3000/api/scraping/batch-import', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        urls: testUrls.slice(0, 2), // Only 2 URLs for testing
        options: {
          parallel: 1,
          retryFailed: true,
          includeFiles: true,
          includeImages: true
        }
      })
    });

    const result = await response.json() as ApiResponse;

    if (result.success) {
      console.log(`✅ Batch job created:`);
      console.log(`  Job ID: ${result.data.jobId}`);
      console.log(`  URL Count: ${result.data.urlCount}`);
      console.log(`  Estimated Duration: ${result.data.estimatedDuration}s`);

      // Progress monitoring
      console.log('\n📊 Monitoring progress...');
      await monitorBatchJob(result.data.jobId);
    } else {
      console.error('❌ Batch job creation error:', result.error?.message || 'Unknown error');
    }
  } catch (error) {
    console.error('💥 Critical batch import error:', error);
  }

  console.log('\n🎉 API testing completed!');
}

async function monitorBatchJob(jobId: string) {
  const maxAttempts = 30; // 30 attempts x 5 seconds = 2.5 minutes
  let attempts = 0;

  while (attempts < maxAttempts) {
    try {
      const response = await fetch(`http://localhost:3000/api/scraping/batch-import?jobId=${jobId}`);
      const result = await response.json() as ApiResponse;

      if (result.success) {
        const job = result.data as BatchJob;
        console.log(`📈 Progress: ${job.progress.current}/${job.progress.total} (${job.progress.percentage}%) - ${job.progress.message}`);

        if (job.status === 'completed') {
          console.log('✅ Job completed successfully!');
          if (job.result?.data?.summary) {
            const summary = job.result.data.summary;
            console.log(`📊 Summary: ${summary.successful} successful, ${summary.failed} failed out of ${summary.total} total`);
          }
          break;
        } else if (job.status === 'failed') {
          console.log('❌ Job failed with error');
          break;
        }
      } else {
        console.error('❌ Error getting job status:', result.error?.message || 'Unknown error');
        break;
      }

      attempts++;
      await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
    } catch (error) {
      console.error('💥 Monitoring error:', error);
      break;
    }
  }

  if (attempts >= maxAttempts) {
    console.log('⏰ Job monitoring timeout');
  }
}

// Run the test
if (require.main === module) {
  testAPIImport().catch(console.error);
}
