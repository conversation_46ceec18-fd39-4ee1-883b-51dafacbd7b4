'use client';

import React from 'react';
import Image from 'next/image';
import { X, Minus, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { CartItem as CartItemType } from '@/types/cart';
import { useCart } from '@/context/cart-context';
import { formatPrice } from '@/lib/utils';

interface CartItemProps {
  item: CartItemType;
}

export const CartItem: React.FC<CartItemProps> = ({ item }) => {
  const { updateQuantity, removeFromCart } = useCart();

  const handleIncreaseQuantity = () => {
    updateQuantity(item.id, item.quantity + 1);
  };

  const handleDecreaseQuantity = () => {
    if (item.quantity > 1) {
      updateQuantity(item.id, item.quantity - 1);
    }
  };

  const handleRemove = () => {
    removeFromCart(item.id);
  };

  return (
    <div className="flex items-center py-4 border-b border-border">
      <div className="relative h-20 w-20 rounded-md overflow-hidden flex-shrink-0">
        <Image
          src={item.thumbnail}
          alt={item.title}
          fill
          className="object-cover"
        />
      </div>
      
      <div className="ml-4 flex-grow">
        <h3 className="font-medium text-foreground">{item.title}</h3>
        {item.designer && (
          <p className="text-sm text-muted-foreground">by {item.designer}</p>
        )}
      </div>
      
      <div className="flex items-center space-x-2 mx-4">
        <Button
          variant="outline"
          size="icon"
          className="h-8 w-8"
          onClick={handleDecreaseQuantity}
          disabled={item.quantity <= 1}
        >
          <Minus className="h-4 w-4" />
          <span className="sr-only">Decrease quantity</span>
        </Button>
        
        <span className="w-8 text-center">{item.quantity}</span>
        
        <Button
          variant="outline"
          size="icon"
          className="h-8 w-8"
          onClick={handleIncreaseQuantity}
        >
          <Plus className="h-4 w-4" />
          <span className="sr-only">Increase quantity</span>
        </Button>
      </div>
      
      <div className="text-right min-w-[80px]">
        <p className="font-medium">{formatPrice(item.price * item.quantity)}</p>
        <p className="text-sm text-muted-foreground">
          {item.quantity > 1 && `${formatPrice(item.price)} each`}
        </p>
      </div>
      
      <Button
        variant="ghost"
        size="icon"
        className="ml-4"
        onClick={handleRemove}
      >
        <X className="h-4 w-4" />
        <span className="sr-only">Remove</span>
      </Button>
    </div>
  );
};
