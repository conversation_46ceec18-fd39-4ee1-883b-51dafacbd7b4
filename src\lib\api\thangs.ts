/**
 * Thangs.com web scraping integration
 */

import { BaseScraper, ScrapingError } from '@/lib/scraping/base-scraper';
import { ScrapedModel, ScrapedImage, ScrapedFile, ScrapedDesigner } from '@/types/models';

export class ThangsScraper extends BaseScraper {
  constructor() {
    super('thangs', {
      userAgent: 'Mozilla/5.0 (compatible; 3DMarketplace-Thangs/1.0)',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
      rateLimit: 12,
    });
  }

  /**
   * Validate if URL is from Thangs
   */
  validateUrl(url: string): boolean {
    return /^https?:\/\/(www\.)?thangs\.com\/designer\/[^\/]+\/model\/\d+/.test(url);
  }

  /**
   * Extract model ID from Thangs URL
   */
  extractModelId(url: string): string | null {
    const match = url.match(/thangs\.com\/designer\/[^\/]+\/model\/(\d+)/);
    return match ? match[1] : null;
  }

  /**
   * Scrape model data from Thangs.com
   */
  async scrapeModel(url: string): Promise<ScrapedModel> {
    if (!this.validateUrl(url)) {
      throw new ScrapingError('INVALID_URL', 'Invalid Thangs URL format', this.platform, url);
    }

    const modelId = this.extractModelId(url);
    if (!modelId) {
      throw new ScrapingError('INVALID_MODEL_ID', 'Could not extract model ID from URL', this.platform, url);
    }

    try {
      // Fetch the HTML content
      const html = await this.fetchHtml(url);
      const $ = this.parseHtml(html);

      // Extract basic information
      const title = this.extractTitle($);
      const description = this.extractDescription($);
      const images = this.extractImages($);
      const files = this.extractFiles($, url);
      const designer = this.extractDesigner($);
      const stats = this.extractStats($);
      const printSettings = this.extractPrintSettings($);
      const tags = this.extractTags($);
      const category = this.extractCategory($);
      const license = this.detectLicense($.html());

      return {
        title,
        description,
        summary: this.extractSummary($),
        images,
        thumbnail: images.length > 0 ? images[0].url : '',
        files,
        fileFormats: this.extractFileFormats(files),
        totalSize: this.calculateTotalSize(files),
        designer,
        tags,
        category,
        license,
        stats,
        platform: 'thangs',
        originalId: modelId,
        originalUrl: url,
        scrapedAt: new Date().toISOString(),
        printSettings,
        isFree: this.checkIfFree($),
      };
    } catch (error) {
      if (error instanceof ScrapingError) {
        throw error;
      }
      throw new ScrapingError(
        'SCRAPING_FAILED',
        `Failed to scrape Thangs model: ${error instanceof Error ? error.message : 'Unknown error'}`,
        this.platform,
        url
      );
    }
  }

  /**
   * Extract model title
   */
  private extractTitle($: any): string {
    const selectors = [
      'h1[data-testid="model-title"]',
      'h1.model-title',
      '.model-header h1',
      '.title-section h1',
      'h1',
    ];

    for (const selector of selectors) {
      const title = this.extractText($, selector);
      if (title) return title;
    }

    throw new ScrapingError('MISSING_TITLE', 'Could not extract model title', this.platform);
  }

  /**
   * Extract model description
   */
  private extractDescription($: any): string {
    const selectors = [
      '[data-testid="model-description"]',
      '.model-description',
      '.description-content',
      '.model-details .description',
      '.about-section',
    ];

    for (const selector of selectors) {
      const description = $(selector).html();
      if (description) {
        return description.trim();
      }
    }

    return '';
  }

  /**
   * Extract model summary
   */
  private extractSummary($: any): string {
    const selectors = [
      '.model-summary',
      '.model-excerpt',
      '.short-description',
      '.model-intro',
      '.summary-text',
    ];

    for (const selector of selectors) {
      const summary = this.extractText($, selector);
      if (summary) return summary;
    }

    return '';
  }

  /**
   * Extract images
   */
  private extractImages($: any): ScrapedImage[] {
    const images: ScrapedImage[] = [];
    const selectors = [
      '.model-gallery img',
      '.image-gallery img',
      '.model-images img',
      '.gallery-item img',
      '.carousel-item img',
      '.preview-images img',
    ];

    selectors.forEach(selector => {
      $(selector).each((index: number, element: any) => {
        const $img = $(element);
        const src = $img.attr('src') || $img.attr('data-src') || $img.attr('data-lazy');
        const alt = $img.attr('alt') || '';

        if (src && this.isValidImageUrl(src)) {
          images.push({
            id: `img_${index}`,
            url: this.normalizeUrl(src),
            alt: alt || undefined,
          });
        }
      });
    });

    return this.deduplicateImages(images);
  }

  /**
   * Extract files information
   */
  private extractFiles($: any, baseUrl: string): ScrapedFile[] {
    const files: ScrapedFile[] = [];
    const selectors = [
      '.file-list .file-item',
      '.download-files .file',
      '.model-files .file-entry',
      '.attachments .file',
      '.downloads .download-item',
    ];

    selectors.forEach(selector => {
      $(selector).each((index: number, element: any) => {
        const $file = $(element);
        const name = this.extractText($file, '.file-name, .filename, .name, .download-name');
        const sizeText = this.extractText($file, '.file-size, .size, .download-size');
        const downloadLink = $file.find('a[href*="download"], a[href*="file"], .download-link').attr('href');

        if (name && downloadLink) {
          const format = this.extractFileFormat(name);
          const size = this.parseFileSize(sizeText);

          files.push({
            id: `file_${index}`,
            name,
            url: this.normalizeUrl(downloadLink, baseUrl),
            downloadUrl: this.normalizeUrl(downloadLink, baseUrl),
            size,
            format,
          });
        }
      });
    });

    return files;
  }

  /**
   * Extract designer information
   */
  private extractDesigner($: any): ScrapedDesigner {
    const nameSelectors = [
      '[data-testid="designer-name"]',
      '.designer-name',
      '.author-name',
      '.creator-name',
      '.user-name',
      '.profile-name',
    ];

    const avatarSelectors = [
      '.designer-avatar img',
      '.author-avatar img',
      '.creator-avatar img',
      '.user-avatar img',
      '.profile-avatar img',
    ];

    let name = 'Unknown Designer';
    for (const selector of nameSelectors) {
      const extractedName = this.extractText($, selector);
      if (extractedName) {
        name = extractedName;
        break;
      }
    }

    let avatar: string | undefined;
    for (const selector of avatarSelectors) {
      const avatarSrc = $(selector).attr('src');
      if (avatarSrc && this.isValidImageUrl(avatarSrc)) {
        avatar = this.normalizeUrl(avatarSrc);
        break;
      }
    }

    return {
      id: `thangs_${name.toLowerCase().replace(/\s+/g, '_')}`,
      name,
      avatar,
    };
  }

  /**
   * Extract statistics
   */
  private extractStats($: any) {
    const extractStat = (selectors: string[]): number => {
      for (const selector of selectors) {
        const text = this.extractText($, selector);
        if (text) {
          return this.extractNumber(text);
        }
      }
      return 0;
    };

    return {
      views: extractStat(['.views-count', '.view-count', '[data-testid="views"]', '.view-stats']),
      downloads: extractStat(['.downloads-count', '.download-count', '[data-testid="downloads"]', '.download-stats']),
      likes: extractStat(['.likes-count', '.like-count', '[data-testid="likes"]', '.heart-count', '.favorite-count']),
      comments: extractStat(['.comments-count', '.comment-count', '[data-testid="comments"]', '.comment-stats']),
    };
  }

  /**
   * Extract print settings
   */
  private extractPrintSettings($: any) {
    const extractSetting = (label: string): string => {
      const $setting = $(`*:contains("${label}")`).closest('.setting, .print-setting, .parameter, .spec-item, .print-info');
      return $setting.find('.value, .setting-value, .spec-value, .info-value').text().trim() || 'Unknown';
    };

    return {
      material: extractSetting('Material') || extractSetting('Filament') || 'PLA',
      layerHeight: extractSetting('Layer Height') || extractSetting('Layer') || '0.2mm',
      infill: extractSetting('Infill') || extractSetting('Density') || '20%',
      supports: extractSetting('Supports') || extractSetting('Support') || 'No',
      rafts: extractSetting('Rafts') || extractSetting('Raft') || 'No',
      printTime: extractSetting('Print Time') || extractSetting('Duration') || 'Unknown',
    };
  }

  /**
   * Extract tags
   */
  private extractTags($: any): string[] {
    const tags: string[] = [];
    const selectors = [
      '.tags .tag',
      '.model-tags .tag',
      '.tag-list .tag',
      '.labels .label',
      '.categories .category',
    ];

    selectors.forEach(selector => {
      $(selector).each((_: number, element: any) => {
        const tag = $(element).text().trim();
        if (tag && !tags.includes(tag)) {
          tags.push(tag);
        }
      });
    });

    return tags;
  }

  /**
   * Extract category
   */
  private extractCategory($: any): string {
    const selectors = [
      '.category-name',
      '.model-category',
      '.breadcrumb .category',
      '.category-tag',
      '.primary-category',
    ];

    for (const selector of selectors) {
      const category = this.extractText($, selector);
      if (category) return category;
    }

    return 'Other';
  }

  /**
   * Check if model is free
   */
  private checkIfFree($: any): boolean {
    const priceSelectors = [
      '.price',
      '.model-price',
      '.cost',
      '.premium-badge',
    ];

    for (const selector of priceSelectors) {
      const priceText = this.extractText($, selector);
      if (priceText && (priceText.includes('$') || priceText.includes('€') || priceText.includes('£') || priceText.toLowerCase().includes('premium'))) {
        return false;
      }
    }

    // Check for "Free" indicators
    const freeIndicators = [
      '.free-badge',
      '.free-label',
      '*:contains("Free")',
      '*:contains("free")',
    ];

    for (const selector of freeIndicators) {
      if ($(selector).length > 0) {
        return true;
      }
    }

    return true; // Default to free if no price found
  }

  /**
   * Helper methods
   */
  private extractFileFormat(filename: string): string {
    const extension = filename.split('.').pop()?.toUpperCase();
    return extension || 'UNKNOWN';
  }

  private parseFileSize(sizeText: string): number {
    if (!sizeText) return 0;

    const match = sizeText.match(/([\d.]+)\s*(B|KB|MB|GB)/i);
    if (!match) return 0;

    const value = parseFloat(match[1]);
    const unit = match[2].toUpperCase();

    const multipliers = {
      'B': 1,
      'KB': 1024,
      'MB': 1024 * 1024,
      'GB': 1024 * 1024 * 1024,
    };

    return Math.round(value * (multipliers[unit as keyof typeof multipliers] || 1));
  }

  private extractFileFormats(files: ScrapedFile[]): string[] {
    const formats = new Set<string>();
    files.forEach(file => {
      if (file.format && file.format !== 'UNKNOWN') {
        formats.add(file.format);
      }
    });
    return Array.from(formats);
  }

  private calculateTotalSize(files: ScrapedFile[]): number {
    return files.reduce((total, file) => total + file.size, 0);
  }
}

// Export singleton instance
export const thangsScraper = new ThangsScraper();

// Utility functions
export function extractThangsModelId(url: string): string | null {
  return thangsScraper.extractModelId(url);
}

export function isThangsUrl(url: string): boolean {
  return thangsScraper.validateUrl(url);
}

export async function importFromThangs(url: string): Promise<{ success: boolean; model?: ScrapedModel; error?: string }> {
  try {
    const model = await thangsScraper.scrapeModel(url);
    return { success: true, model };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}
