'use client';

import { useSession } from 'next-auth/react';
import { useState, useEffect, Suspense } from 'react';
import { User, Mail, Package, Heart, Settings, Upload, Download } from 'lucide-react';
import Link from 'next/link';
import ProtectedRoute from '@/components/auth/protected-route';
import UserModelsManager from '@/components/marketplace/user-models-manager';
import UserDownloads from '@/components/marketplace/user-downloads';

function ProfilePageContent() {
  const { data: session } = useSession();
  const [activeTab, setActiveTab] = useState('profile');

  const tabs = [
    { id: 'profile', label: 'Профіль', icon: <User className="h-5 w-5" /> },
    { id: 'orders', label: 'Замовлення', icon: <Package className="h-5 w-5" /> },
    { id: 'downloads', label: 'Завантаження', icon: <Download className="h-5 w-5" /> },
    { id: 'favorites', label: 'Обрані', icon: <Heart className="h-5 w-5" /> },
    { id: 'uploads', label: 'Мої моделі', icon: <Upload className="h-5 w-5" /> },
    { id: 'settings', label: 'Налаштування', icon: <Settings className="h-5 w-5" /> },
  ];

  return (
    <ProtectedRoute>
      <main className="container py-10">
        <div className="flex flex-col md:flex-row gap-8">
          {/* Sidebar */}
          <aside className="w-full md:w-1/4">
            <div className="bg-card rounded-lg shadow-sm p-6 sticky top-20">
              <div className="flex flex-col items-center mb-6">
                <div className="h-24 w-24 rounded-full bg-blue-600 flex items-center justify-center text-white mb-4">
                  {session?.user?.image ? (
                    <img
                      src={session.user.image}
                      alt={session.user.name || 'Аватар користувача'}
                      className="h-24 w-24 rounded-full"
                    />
                  ) : (
                    <User className="h-12 w-12" />
                  )}
                </div>
                <h2 className="text-xl font-semibold">{session?.user?.name || 'Користувач'}</h2>
                <p className="text-sm text-muted-foreground flex items-center mt-1">
                  <Mail className="h-4 w-4 mr-1" />
                  {session?.user?.email}
                </p>
              </div>

              <nav className="space-y-1">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    className={`flex items-center gap-2 w-full px-3 py-2 text-sm rounded-md transition-colors ${
                      activeTab === tab.id
                        ? 'bg-primary text-primary-foreground'
                        : 'text-muted-foreground hover:bg-muted hover:text-foreground'
                    }`}
                    onClick={() => setActiveTab(tab.id)}
                    aria-label={tab.label}
                    tabIndex={0}
                  >
                    {tab.icon}
                    {tab.label}
                  </button>
                ))}
              </nav>
            </div>
          </aside>

          {/* Main content */}
          <div className="flex-1">
            {activeTab === 'profile' && (
              <div className="bg-card rounded-lg shadow-sm p-6">
                <h1 className="text-2xl font-bold mb-6">Мій профіль</h1>

                <div className="space-y-6">
                  <div>
                    <h2 className="text-lg font-semibold mb-4">Особиста інформація</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-muted-foreground mb-1">Ім'я</label>
                        <div className="text-foreground">{session?.user?.name || 'Не вказано'}</div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-muted-foreground mb-1">Email</label>
                        <div className="text-foreground">{session?.user?.email}</div>
                      </div>
                    </div>
                  </div>

                  <div className="border-t border-border pt-6">
                    <h2 className="text-lg font-semibold mb-4">Статистика</h2>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                      <div className="bg-muted p-4 rounded-md">
                        <div className="text-3xl font-bold">0</div>
                        <div className="text-sm text-muted-foreground">Замовлень</div>
                      </div>
                      <div className="bg-muted p-4 rounded-md">
                        <div className="text-3xl font-bold">0</div>
                        <div className="text-sm text-muted-foreground">Обраних моделей</div>
                      </div>
                      <div className="bg-muted p-4 rounded-md">
                        <div className="text-3xl font-bold">0</div>
                        <div className="text-sm text-muted-foreground">Завантажених моделей</div>
                      </div>
                    </div>
                  </div>

                  <div className="border-t border-border pt-6">
                    <h2 className="text-lg font-semibold mb-4">Дії</h2>
                    <div className="flex flex-wrap gap-4">
                      <Link
                        href="/models/upload"
                        className="inline-flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
                      >
                        <Upload className="h-4 w-4" />
                        Завантажити модель
                      </Link>
                      <Link
                        href="/profile/settings"
                        className="inline-flex items-center gap-2 px-4 py-2 bg-muted text-foreground rounded-md hover:bg-muted/80 transition-colors"
                      >
                        <Settings className="h-4 w-4" />
                        Налаштування профілю
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'orders' && (
              <div className="bg-card rounded-lg shadow-sm p-6">
                <h1 className="text-2xl font-bold mb-6">Мої замовлення</h1>
                <div className="text-center py-10 text-muted-foreground">
                  <Package className="h-16 w-16 mx-auto mb-4 opacity-30" />
                  <p>У вас ще немає замовлень</p>
                </div>
              </div>
            )}

            {activeTab === 'downloads' && (
              <div className="bg-card rounded-lg shadow-sm p-6">
                <UserDownloads />
              </div>
            )}

            {activeTab === 'favorites' && (
              <div className="bg-card rounded-lg shadow-sm p-6">
                <h1 className="text-2xl font-bold mb-6">Обрані моделі</h1>
                <div className="text-center py-10 text-muted-foreground">
                  <Heart className="h-16 w-16 mx-auto mb-4 opacity-30" />
                  <p>У вас ще немає обраних моделей</p>
                </div>
              </div>
            )}

            {activeTab === 'uploads' && (
              <div className="bg-card rounded-lg shadow-sm p-6">
                <UserModelsManager userId={session?.user?.id} />
              </div>
            )}

            {activeTab === 'settings' && (
              <div className="bg-card rounded-lg shadow-sm p-6">
                <h1 className="text-2xl font-bold mb-6">Налаштування профілю</h1>
                <p className="text-muted-foreground mb-6">
                  Налаштування профілю будуть доступні найближчим часом
                </p>
              </div>
            )}
          </div>
        </div>
      </main>
    </ProtectedRoute>
  );
}

export default function ProfilePage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    }>
      <ProfilePageContent />
    </Suspense>
  );
}
