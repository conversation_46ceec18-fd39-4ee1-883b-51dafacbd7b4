/**
 * Tests for BaseScraper class
 */

import { BaseScraper, ScrapingError } from '../base-scraper';
import { ScrapedModel, ModelSource } from '@/types/models';

// Mock implementation for testing
class TestScraper extends BaseScraper {
  constructor() {
    super('printables', {
      userAgent: 'Test-Agent/1.0',
      timeout: 5000,
      retryAttempts: 2,
      retryDelay: 100,
      rateLimit: 10,
    });
  }

  validateUrl(url: string): boolean {
    return url.startsWith('https://test.com/');
  }

  extractModelId(url: string): string | null {
    const match = url.match(/test\.com\/model\/(\d+)/);
    return match ? match[1] : null;
  }

  async scrapeModel(url: string): Promise<ScrapedModel> {
    if (!this.validateUrl(url)) {
      throw new ScrapingError('INVALID_URL', 'Invalid URL format', this.platform, url);
    }

    const modelId = this.extractModelId(url);
    if (!modelId) {
      throw new ScrapingError('INVALID_MODEL_ID', 'Could not extract model ID', this.platform, url);
    }

    // Mock scraped model
    return {
      title: 'Test Model',
      description: 'Test description',
      summary: 'Test summary',
      images: [{ id: '1', url: 'https://test.com/image.jpg' }],
      thumbnail: 'https://test.com/thumb.jpg',
      files: [{ id: '1', name: 'test.stl', url: 'https://test.com/file.stl', downloadUrl: 'https://test.com/download/file.stl', size: 1024, format: 'STL' }],
      fileFormats: ['STL'],
      totalSize: 1024,
      designer: { id: 'test-designer', name: 'Test Designer' },
      tags: ['test', 'model'],
      category: 'Test',
      license: {
        type: 'CC-BY',
        name: 'Creative Commons Attribution 4.0',
        detected: true,
        confidence: 0.9,
        allowCommercialUse: true,
        requireAttribution: true,
        allowDerivatives: true,
      },
      stats: { views: 100, downloads: 50, likes: 25, comments: 10 },
      platform: 'printables',
      originalId: modelId,
      originalUrl: url,
      scrapedAt: new Date().toISOString(),
      isFree: true,
    };
  }
}

describe('BaseScraper', () => {
  let scraper: TestScraper;

  beforeEach(() => {
    scraper = new TestScraper();
  });

  describe('URL validation', () => {
    it('should validate correct URLs', () => {
      expect(scraper.validateUrl('https://test.com/model/123')).toBe(true);
    });

    it('should reject invalid URLs', () => {
      expect(scraper.validateUrl('https://other.com/model/123')).toBe(false);
      expect(scraper.validateUrl('invalid-url')).toBe(false);
    });
  });

  describe('Model ID extraction', () => {
    it('should extract model ID from valid URLs', () => {
      expect(scraper.extractModelId('https://test.com/model/123')).toBe('123');
      expect(scraper.extractModelId('https://test.com/model/456789')).toBe('456789');
    });

    it('should return null for invalid URLs', () => {
      expect(scraper.extractModelId('https://test.com/invalid')).toBeNull();
      expect(scraper.extractModelId('https://other.com/model/123')).toBeNull();
    });
  });

  describe('License detection', () => {
    it('should detect Creative Commons licenses', () => {
      const license = scraper['detectLicense']('This work is licensed under Creative Commons Attribution 4.0');
      expect(license.type).toBe('CC-BY');
      expect(license.detected).toBe(true);
      expect(license.confidence).toBeGreaterThan(0.8);
    });

    it('should detect GPL licenses', () => {
      const license = scraper['detectLicense']('This is released under GNU General Public License');
      expect(license.type).toBe('GPL');
      expect(license.detected).toBe(true);
    });

    it('should detect CC0/Public Domain', () => {
      const license = scraper['detectLicense']('This work is in the public domain CC0');
      expect(license.type).toBe('CC0');
      expect(license.detected).toBe(true);
    });

    it('should default to custom license for unknown text', () => {
      const license = scraper['detectLicense']('Some random text without license info');
      expect(license.type).toBe('Custom');
      expect(license.detected).toBe(false);
      expect(license.confidence).toBeLessThan(0.5);
    });
  });

  describe('URL normalization', () => {
    it('should handle protocol-relative URLs', () => {
      const normalized = scraper['normalizeUrl']('//example.com/image.jpg');
      expect(normalized).toBe('https://example.com/image.jpg');
    });

    it('should handle relative URLs with base URL', () => {
      const normalized = scraper['normalizeUrl']('/image.jpg', 'https://example.com/page');
      expect(normalized).toBe('https://example.com/image.jpg');
    });

    it('should leave absolute URLs unchanged', () => {
      const url = 'https://example.com/image.jpg';
      const normalized = scraper['normalizeUrl'](url);
      expect(normalized).toBe(url);
    });
  });

  describe('Image validation', () => {
    it('should validate image URLs by extension', () => {
      expect(scraper['isValidImageUrl']('https://example.com/image.jpg')).toBe(true);
      expect(scraper['isValidImageUrl']('https://example.com/image.png')).toBe(true);
      expect(scraper['isValidImageUrl']('https://example.com/image.webp')).toBe(true);
      expect(scraper['isValidImageUrl']('https://example.com/image.svg')).toBe(true);
    });

    it('should validate image URLs by content', () => {
      expect(scraper['isValidImageUrl']('https://example.com/thumb/image')).toBe(true);
      expect(scraper['isValidImageUrl']('https://example.com/api/image/123')).toBe(true);
    });

    it('should reject non-image URLs', () => {
      expect(scraper['isValidImageUrl']('https://example.com/document.pdf')).toBe(false);
      expect(scraper['isValidImageUrl']('https://example.com/video.mp4')).toBe(false);
      expect(scraper['isValidImageUrl']('')).toBe(false);
      expect(scraper['isValidImageUrl']('short')).toBe(false);
    });
  });

  describe('Image deduplication', () => {
    it('should remove duplicate images', () => {
      const images = [
        { url: 'https://example.com/image1.jpg', alt: 'Image 1' },
        { url: 'https://example.com/image2.jpg', alt: 'Image 2' },
        { url: 'https://example.com/image1.jpg', alt: 'Image 1 duplicate' },
        { url: 'https://example.com/image3.jpg', alt: 'Image 3' },
      ];

      const deduplicated = scraper['deduplicateImages'](images);
      expect(deduplicated).toHaveLength(3);
      expect(deduplicated.map(img => img.url)).toEqual([
        'https://example.com/image1.jpg',
        'https://example.com/image2.jpg',
        'https://example.com/image3.jpg',
      ]);
    });
  });

  describe('Text extraction and cleaning', () => {
    it('should extract and clean text content', () => {
      // Mock cheerio API
      const mockCheerio = jest.fn().mockReturnValue({
        text: () => '  Test   content  with   spaces  ',
      });

      const text = scraper['extractText'](mockCheerio as any, 'selector');
      expect(text).toBe('Test content with spaces');
    });

    it('should extract numbers from text', () => {
      expect(scraper['extractNumber']('Downloads: 1,234')).toBe(1234);
      expect(scraper['extractNumber']('Views: 567')).toBe(567);
      expect(scraper['extractNumber']('No numbers here')).toBe(0);
    });
  });

  describe('Error handling', () => {
    it('should throw ScrapingError for invalid URLs', async () => {
      await expect(scraper.scrapeModel('https://invalid.com/model/123')).rejects.toThrow(ScrapingError);
    });

    it('should throw ScrapingError for URLs without model ID', async () => {
      await expect(scraper.scrapeModel('https://test.com/invalid')).rejects.toThrow(ScrapingError);
    });

    it('should create ScrapingError with correct properties', () => {
      const error = new ScrapingError('TEST_ERROR', 'Test error message', 'printables', 'https://test.com');

      expect(error.code).toBe('TEST_ERROR');
      expect(error.message).toBe('Test error message');
      expect(error.platform).toBe('printables');
      expect(error.url).toBe('https://test.com');
      expect(error.name).toBe('ScrapingError');
    });
  });

  describe('License properties', () => {
    it('should correctly identify commercial use permissions', () => {
      expect(scraper['getAllowCommercialUse']('CC-BY')).toBe(true);
      expect(scraper['getAllowCommercialUse']('CC-BY-SA')).toBe(true);
      expect(scraper['getAllowCommercialUse']('CC-BY-NC')).toBe(false);
      expect(scraper['getAllowCommercialUse']('CC-BY-NC-SA')).toBe(false);
    });

    it('should correctly identify attribution requirements', () => {
      expect(scraper['getRequireAttribution']('CC-BY')).toBe(true);
      expect(scraper['getRequireAttribution']('CC-BY-SA')).toBe(true);
      expect(scraper['getRequireAttribution']('CC0')).toBe(false);
      expect(scraper['getRequireAttribution']('MIT')).toBe(false);
    });

    it('should correctly identify derivative permissions', () => {
      expect(scraper['getAllowDerivatives']('CC-BY')).toBe(true);
      expect(scraper['getAllowDerivatives']('CC-BY-SA')).toBe(true);
      expect(scraper['getAllowDerivatives']('GPL')).toBe(true);
      expect(scraper['getAllowDerivatives']('Commercial')).toBe(false);
    });
  });
});

describe('ScrapingError', () => {
  it('should create error with all properties', () => {
    const details = { statusCode: 404 };
    const error = new ScrapingError('NOT_FOUND', 'Model not found', 'printables', 'https://test.com', details);

    expect(error.code).toBe('NOT_FOUND');
    expect(error.message).toBe('Model not found');
    expect(error.platform).toBe('printables');
    expect(error.url).toBe('https://test.com');
    expect(error.details).toEqual(details);
    expect(error.name).toBe('ScrapingError');
  });

  it('should create error with minimal properties', () => {
    const error = new ScrapingError('GENERIC_ERROR', 'Something went wrong');

    expect(error.code).toBe('GENERIC_ERROR');
    expect(error.message).toBe('Something went wrong');
    expect(error.platform).toBeUndefined();
    expect(error.url).toBeUndefined();
    expect(error.details).toBeUndefined();
  });
});
