'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Search,
  Menu,
  User,
  ChevronDown,
  Home,
  Shapes,
  Gamepad,
  Building,
  Car,
  Sofa,
  Utensils,
  Box,
  Printer,
  Cpu,
  Layers,
  Loader2
} from 'lucide-react';
import { CartIcon } from '@/components/cart/cart-icon';
import UserAuthStatus from '@/components/auth/user-auth-status';
import AnimatedLogo from '@/components/ui/animated-logo';
import DownloadButton from '@/components/ui/download-button';

import { cn } from '@/lib/utils';
import { ThemeToggle } from '@/components/theme-toggle';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetClose
} from '@/components/ui/sheet';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuGroup,
} from '@/components/ui/dropdown-menu';

const categories = [
  { name: 'Всі категорії', icon: <Shapes className="h-4 w-4" />, href: '/marketplace' },
  { name: 'Абстракції', icon: <Shapes className="h-4 w-4" />, href: '/marketplace?category=abstract' },
  { name: 'Архітектура', icon: <Building className="h-4 w-4" />, href: '/marketplace?category=architecture' },
  { name: 'Транспорт', icon: <Car className="h-4 w-4" />, href: '/marketplace?category=vehicles' },
  { name: 'Ігри', icon: <Gamepad className="h-4 w-4" />, href: '/marketplace?category=games' },
  { name: 'Будинки', icon: <Home className="h-4 w-4" />, href: '/marketplace?category=houses' },
  { name: 'Меблі', icon: <Sofa className="h-4 w-4" />, href: '/marketplace?category=furniture' },
  { name: 'Кухня', icon: <Utensils className="h-4 w-4" />, href: '/marketplace?category=kitchen' },
  { name: 'Інше', icon: <Box className="h-4 w-4" />, href: '/marketplace?category=other' },
];

const MainHeader = () => {
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const pathname = usePathname();
  const router = useRouter();

  const isActive = (path: string) => {
    if (path === '/') {
      return pathname === path;
    }
    return pathname.startsWith(path);
  };

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    setIsLoading(true);
    try {
      // Симуляція пошуку з затримкою
      await new Promise(resolve => setTimeout(resolve, 1000));
      router.push(`/marketplace?search=${encodeURIComponent(searchQuery)}`);
    } finally {
      setIsLoading(false);
      setIsSearchOpen(false);
    }
  };

  return (
    <motion.header
      className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60"
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      <div className="container flex h-16 items-center justify-between">
        {/* Ліва частина - Логотип та навігація */}
        <div className="flex items-center gap-8">
          <AnimatedLogo className="h-10 w-10" showText={true} />
          <motion.nav
            className="hidden lg:flex items-center space-x-1 text-sm font-medium"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button variant="ghost" className={cn(
                    "group flex items-center gap-1 transition-all duration-300 hover:text-foreground/80 hover:bg-accent/50",
                    isActive('/marketplace') ? "text-foreground bg-accent/30" : "text-foreground/60"
                  )}>
                    3D Моделі
                    <ChevronDown className="h-4 w-4 transition-transform group-data-[state=open]:rotate-180" />
                  </Button>
                </motion.div>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" className="w-56">
                <DropdownMenuGroup>
                  {categories.map((category) => (
                    <DropdownMenuItem key={category.name} asChild>
                      <Link href={category.href} className="flex items-center gap-2">
                        {category.icon}
                        <span>{category.name}</span>
                      </Link>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>

            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Link
                href="/print-services"
                className={cn(
                  "transition-all duration-300 hover:text-foreground/80 px-3 py-2 rounded-md hover:bg-accent/50",
                  isActive('/print-services') ? "text-foreground bg-accent/30" : "text-foreground/60"
                )}
              >
                Друк
              </Link>
            </motion.div>

            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Link
                href="/community"
                className={cn(
                  "transition-all duration-300 hover:text-foreground/80 px-3 py-2 rounded-md hover:bg-accent/50",
                  isActive('/community') ? "text-foreground bg-accent/30" : "text-foreground/60"
                )}
              >
                Спільнота
              </Link>
            </motion.div>

            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Link
                href="/marketplace/equipment"
                className={cn(
                  "transition-all duration-300 hover:text-foreground/80 px-3 py-2 rounded-md hover:bg-accent/50",
                  isActive('/marketplace/equipment') ? "text-foreground bg-accent/30" : "text-foreground/60"
                )}
              >
                Обладнання
              </Link>
            </motion.div>

            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Link
                href="/marketplace/filament"
                className={cn(
                  "transition-all duration-300 hover:text-foreground/80 px-3 py-2 rounded-md hover:bg-accent/50",
                  isActive('/marketplace/filament') ? "text-foreground bg-accent/30" : "text-foreground/60"
                )}
              >
                Філамент
              </Link>
            </motion.div>
          </motion.nav>
        </div>

        {/* Центральна частина - Пошук */}
        <motion.div
          className="flex-1 flex justify-center max-w-md mx-auto"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.5 }}
        >
          {/* Пошук для десктопу */}
          <motion.form
            className="hidden md:flex relative w-full items-center"
            onSubmit={handleSearch}
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 400, damping: 17 }}
          >
            <Input
              type="search"
              placeholder="Пошук моделей..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pr-10 focus-visible:ring-primary transition-all duration-300"
              disabled={isLoading}
            />
            <Button
              type="submit"
              size="icon"
              variant="ghost"
              className="absolute right-0 top-0 h-full rounded-l-none hover:bg-primary/10 transition-colors"
              disabled={isLoading}
            >
              <AnimatePresence mode="wait">
                {isLoading ? (
                  <motion.div
                    key="loading"
                    initial={{ opacity: 0, rotate: 0 }}
                    animate={{ opacity: 1, rotate: 360 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Loader2 className="h-4 w-4 animate-spin" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="search"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Search className="h-4 w-4" />
                  </motion.div>
                )}
              </AnimatePresence>
              <span className="sr-only">Пошук</span>
            </Button>
          </motion.form>
        </motion.div>

        {/* Права частина - Дії користувача */}
        <motion.div
          className="flex items-center gap-2"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4, duration: 0.5 }}
        >
          {/* Пошук для мобільних */}
          <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden hover:bg-accent/50 transition-colors"
              onClick={() => setIsSearchOpen(!isSearchOpen)}
            >
              <Search className="h-5 w-5" />
              <span className="sr-only">Пошук</span>
            </Button>
          </motion.div>

          {/* Кнопка завантаження */}
          <div className="hidden lg:block">
            <DownloadButton />
          </div>

          {/* Перемикач теми */}
          <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
            <ThemeToggle />
          </motion.div>

          {/* Реєстрація/Авторизація - правий верхній кут */}
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="hidden md:block"
          >
            <UserAuthStatus />
          </motion.div>

          {/* Кошик - правий верхній кут */}
          <motion.div
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="relative"
          >
            <CartIcon />
          </motion.div>

          {/* Мобільне меню */}
          <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="md:hidden hover:bg-accent/50 transition-colors">
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Меню</span>
                </Button>
              </SheetTrigger>
            <SheetContent side="right" className="flex flex-col">
              <SheetHeader>
                <SheetTitle>Меню</SheetTitle>
              </SheetHeader>
              <div className="flex-1 py-6">
                <nav className="flex flex-col gap-4">
                  <SheetClose asChild>
                    <Link
                      href="/"
                      className="flex items-center gap-2 text-lg font-medium"
                    >
                      <Home className="h-5 w-5" />
                      3D моделі
                    </Link>
                  </SheetClose>
                  <SheetClose asChild>
                    <Link
                      href="/marketplace"
                      className="flex items-center gap-2 text-lg font-medium"
                    >
                      <Shapes className="h-5 w-5" />
                      Маркетплейс
                    </Link>
                  </SheetClose>

                  <div className="pl-7 flex flex-col gap-2 mt-2">
                    <div className="text-xs text-muted-foreground mb-1">Категорії 3D моделей:</div>
                    {categories.map((category) => (
                      <SheetClose key={category.name} asChild>
                        <Link
                          href={category.href}
                          className="flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
                        >
                          {category.icon}
                          {category.name}
                        </Link>
                      </SheetClose>
                    ))}
                  </div>
                  <SheetClose asChild>
                    <Link
                      href="/models"
                      className="flex items-center gap-2 text-lg font-medium"
                    >
                      <Shapes className="h-5 w-5" />
                      3D Моделі
                    </Link>
                  </SheetClose>
                  <SheetClose asChild>
                    <Link
                      href="/print-services"
                      className="flex items-center gap-2 text-lg font-medium"
                    >
                      <Printer className="h-5 w-5" />
                      Друк
                    </Link>
                  </SheetClose>
                  <SheetClose asChild>
                    <Link
                      href="/community"
                      className="flex items-center gap-2 text-lg font-medium"
                    >
                      <User className="h-5 w-5" />
                      Спільнота
                    </Link>
                  </SheetClose>
                  <SheetClose asChild>
                    <Link
                      href="/marketplace/equipment"
                      className="flex items-center gap-2 text-lg font-medium"
                    >
                      <Cpu className="h-5 w-5" />
                      Обладнання
                    </Link>
                  </SheetClose>
                  <SheetClose asChild>
                    <Link
                      href="/marketplace/filament"
                      className="flex items-center gap-2 text-lg font-medium"
                    >
                      <Layers className="h-5 w-5" />
                      Філамент
                    </Link>
                  </SheetClose>
                  <SheetClose asChild>
                    <Link
                      href="/admin/scraper"
                      className="flex items-center gap-2 text-lg font-medium"
                    >
                      <Cpu className="h-5 w-5" />
                      Адмін панель
                    </Link>
                  </SheetClose>
                </nav>
              </div>
              <div className="border-t border-border pt-6">
                <div className="flex flex-col gap-4">
                  {/* Компонент автентифікації буде відображатися в мобільному меню */}
                  <div className="px-2">
                    <UserAuthStatus />
                  </div>

                  {/* Кошик для мобільних */}
                  <div className="px-2 md:hidden">
                    <div className="flex items-center gap-2 p-2 rounded-md hover:bg-accent/50 transition-colors">
                      <CartIcon />
                      <span className="text-sm font-medium">Кошик</span>
                    </div>
                  </div>
                </div>
              </div>
            </SheetContent>
            </Sheet>
          </motion.div>
        </motion.div>
      </div>

      {/* Мобільний пошук */}
      {isSearchOpen && (
        <motion.div
          className="border-t border-border py-3 md:hidden"
          initial={{ height: 0, opacity: 0 }}
          animate={{ height: "auto", opacity: 1 }}
          exit={{ height: 0, opacity: 0 }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
        >
          <div className="container">
            <motion.form
              className="relative flex items-center"
              onSubmit={handleSearch}
              initial={{ y: -10, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.1, duration: 0.3 }}
            >
              <Input
                type="search"
                placeholder="Пошук моделей..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pr-10 transition-all duration-300"
                disabled={isLoading}
                autoFocus
              />
              <Button
                type="submit"
                size="icon"
                variant="ghost"
                className="absolute right-0 top-0 h-full rounded-l-none hover:bg-primary/10 transition-colors"
                disabled={isLoading}
              >
                <AnimatePresence mode="wait">
                  {isLoading ? (
                    <motion.div
                      key="loading-mobile"
                      initial={{ opacity: 0, rotate: 0 }}
                      animate={{ opacity: 1, rotate: 360 }}
                      exit={{ opacity: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Loader2 className="h-4 w-4 animate-spin" />
                    </motion.div>
                  ) : (
                    <motion.div
                      key="search-mobile"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Search className="h-4 w-4" />
                    </motion.div>
                  )}
                </AnimatePresence>
                <span className="sr-only">Пошук</span>
              </Button>
            </motion.form>
          </div>
        </motion.div>
      )}
    </motion.header>
  );
};

export default MainHeader;
